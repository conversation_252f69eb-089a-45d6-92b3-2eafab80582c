import { DataTypes } from "sequelize";

const GLAccountMasterModel = (sequelize) => {
  const GLAccountMaster = sequelize.define(
    "gl_account_master",
    {
      access_token: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      refresh_token: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "qb_gl_account_master",
      timestamps: false,
    }
  );

  // No associations per instructions

  return GLAccountMaster;
};

export default GLAccountMasterModel;
