"use client";

import Filters from "@/components/listing/Filters";
import StatsCards from "@/components/listing/StatsCards";
import { useState, useEffect } from "react";
import ClientTable from "@/components/listing/ClientTable";
import { LISTING_CONSTANTS } from "@/utils/constants";
import { useDispatch, useSelector } from "react-redux";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";

export default function ListingPage() {
  const [searchTerm, setSearchTerm] = useState(LISTING_CONSTANTS.SEARCH.ALL);
  const [statusFilter, setStatusFilter] = useState(
    LISTING_CONSTANTS.STATUS_FILTER.ALL
  );

  const dispatch = useDispatch();
  const { organizations, loading, error } = useSelector(
    (state) => state.organizations
  );

  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  return (
    <div className="min-h-screen max-w-7xl mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <Filters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          addButtonText={LISTING_CONSTANTS.ADD_BUTTON_TEXT}
          addButtonPath={LISTING_CONSTANTS.ADD_BUTTON_PATH}
        />
      </div>
      
      {/* Stats Cards */}
      <div className="mb-6">
        <StatsCards organizations={organizations} loading={loading} />
      </div>
      
      {/* Client Table */}
      <div className="bg-white rounded-xl shadow-sm">
        <ClientTable
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          organizations={organizations}
          loading={loading}
          error={error}
        />
      </div>
    </div>
  );
}
