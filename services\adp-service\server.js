import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import adpRoutes from "./app/routes/index.js";
import dotenv from "dotenv";
import { createLogger } from "./app/utils/logger.util.js";

dotenv.config();

const logger = createLogger("ADP_SERVER");
const app = express();

// Apply CORS BEFORE routes
const allowedOrigins =
  process.env.ALLOWED_ORIGINS?.split(",").map((o) => o.trim()) || [];

app.use(
  cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like Postman or server-to-server)
      if (!origin) return callback(null, true);

      // Check if the origin is in the allowed list
      const isAllowed = allowedOrigins.some((allowed) => {
        // Handle trailing slashes and wildcard subdomains (like *.devtunnels.ms)
        if (allowed.endsWith("/")) allowed = allowed.slice(0, -1);
        if (
          allowed.startsWith("https://*.") ||
          allowed.startsWith("http://*.")
        ) {
          const base = allowed.replace("*.", "");
          return origin.endsWith(base);
        }
        return origin === allowed;
      });

      if (isAllowed) return callback(null, true);
      logger.warn(`🚫 CORS blocked: ${origin}`);
      return callback(new Error(`Not allowed by CORS: ${origin}`));
    },

    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
      "Origin",
    ],
    optionsSuccessStatus: 200,
  })
);

// Handle preflight requests
app.options("*", cors());

// Middleware
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Routes
app.use("/api", adpRoutes);

// Global error handler
app.use((err, req, res, next) => {
  if (err.message === "Not allowed by CORS") {
    logger.warn("CORS policy violation", {
      origin: req.get("Origin"),
      allowedOrigins: allowedOrigins,
    });
    return res.status(403).json({
      success: false,
      service: "ADP Service",
      message: "CORS policy violation - Origin not allowed",
      error: err.message,
      origin: req.get("Origin"),
      allowedOrigins: allowedOrigins,
    });
  }

  logger.error("Unhandled error", {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
  });

  res.status(500).json({
    success: false,
    service: "ADP Service",
    message: "Internal Server Error",
    ...(process.env.NODE_ENV === "development" && { error: err.message }),
  });
});

// Start server
const PORT = process.env.PORT || 3006;
app.listen(PORT, () => {
  logger.info(`🚀 ADP Service running on port ${PORT}`);
});
