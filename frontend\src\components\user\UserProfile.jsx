"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Separator } from "../ui/separator";
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Shield,
  Clock,
  Edit,
  Settings,
  CheckCircle,
  AlertCircle,
  MoreVertical,
} from "lucide-react";
import tokenStorage from "@/lib/tokenStorage";
import { getVersionedImage } from "@/utils/imageVersions";

export default function UserProfile() {
  const router = useRouter();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = () => {
      try {
        const user = tokenStorage.getUserData();
        setUserData(user);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching user data:", error);
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleBack = () => {
    router.back();
  };

  const handleEditProfile = () => {
    // TODO: Implement edit profile functionality
  };

  const handleSettings = () => {
    router.push("/settings");
  };

  const getRoleBadgeColor = (role) => {
    switch (role?.toLowerCase()) {
      case "admin":
        return "bg-red-100 text-red-800";
      case "org_head":
        return "bg-blue-100 text-blue-800";
      case "user":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusBadgeColor = (isActive) => {
    return isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-6"></div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Loading Profile
          </h3>
          <p className="text-gray-500">
            Please wait while we fetch your information...
          </p>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6 border border-red-100">
            <AlertCircle className="w-10 h-10 text-red-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3">
            Profile Not Found
          </h2>
          <p className="text-gray-500 mb-8 leading-relaxed">
            We couldn&apos;t load your profile information. This might be due to a
            network issue or session timeout.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={handleBack}
              variant="outline"
              className="w-full sm:w-auto border-gray-200 hover:bg-gray-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
            <Button
              onClick={() => window.location.reload()}
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-600"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleBack}
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <Separator orientation="vertical" className="h-6 bg-gray-200" />
              <div>
                <h1 className="text-xl font-semibold text-gray-800">Profile</h1>
                <p className="text-sm text-gray-500">
                  Manage your account information
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleEditProfile}
                variant="outline"
                size="sm"
                className="border-gray-200 hover:bg-gray-50"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button
                onClick={handleSettings}
                variant="outline"
                size="sm"
                className="border-gray-200 hover:bg-gray-50"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <Card className="overflow-hidden border-gray-200 shadow-sm">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 border-b border-gray-100">
                <div className="flex flex-col items-center text-center">
                  <Avatar className="w-20 h-20 mb-4 ring-4 ring-white shadow-lg">
                    <AvatarImage src="" alt={userData.name} />
                    <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                      {userData.name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <h2 className="text-xl font-bold mb-1 text-gray-800">
                    {userData.name || "Unknown User"}
                  </h2>
                  <p className="text-gray-500 text-sm mb-3">
                    {userData.email || "No email"}
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                      {userData.role?.name || "No Role"}
                    </Badge>
                    <Badge className={getStatusBadgeColor(userData.is_active)}>
                      {userData.is_active ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Inactive
                        </>
                      )}
                    </Badge>
                  </div>
                </div>
              </div>
              <CardContent className="p-6 space-y-4 bg-white">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 text-sm p-3 bg-gray-50 rounded-lg">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600 truncate">
                      {userData.email || "No email"}
                    </span>
                  </div>
                  {userData.phone_number && (
                    <div className="flex items-center space-x-3 text-sm p-3 bg-gray-50 rounded-lg">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">
                        {userData.phone_number}
                      </span>
                    </div>
                  )}
                  {userData.organization && (
                    <div className="flex items-center space-x-3 text-sm p-3 bg-gray-50 rounded-lg">
                      <Building className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600 truncate">
                        {userData.organization.name}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="lg:col-span-3 space-y-6">
            <Card className="border-gray-200 shadow-sm">
              <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-transparent border-b border-gray-100">
                <CardTitle className="flex items-center space-x-2 text-lg text-gray-800">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <User className="w-4 h-4 text-blue-600" />
                  </div>
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Full Name
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.name || "Not provided"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Email Address
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.email || "Not provided"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Phone Number
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.phone_number || "Not provided"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Role
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.role?.name || "Not assigned"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-gray-200 shadow-sm">
              <CardHeader className="pb-4 bg-gradient-to-r from-green-50 to-transparent border-b border-gray-100">
                <CardTitle className="flex items-center space-x-2 text-lg text-gray-800">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Shield className="w-4 h-4 text-green-600" />
                  </div>
                  <span>Account Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Account Status
                    </label>
                    <div className="mt-1">
                      <Badge
                        className={getStatusBadgeColor(userData.is_active)}
                      >
                        {userData.is_active ? (
                          <>
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-3 h-3 mr-1" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      User ID
                    </label>
                    <p className="text-base text-gray-800 font-mono text-sm">
                      {userData.id || "Not available"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Tenant ID
                    </label>
                    <p className="text-base text-gray-800 font-mono text-sm">
                      {userData.tenant_id || "Not assigned"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Organization ID
                    </label>
                    <p className="text-base text-gray-800 font-mono text-sm">
                      {userData.organization_id || "Not assigned"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-gray-200 shadow-sm">
              <CardHeader className="pb-4 bg-gradient-to-r from-purple-50 to-transparent border-b border-gray-100">
                <CardTitle className="flex items-center space-x-2 text-lg text-gray-800">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Clock className="w-4 h-4 text-purple-600" />
                  </div>
                  <span>Activity Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Login
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.last_login
                        ? new Date(userData.last_login).toLocaleString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )
                        : "Never logged in"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Account Created
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.created_at
                        ? new Date(userData.created_at).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            }
                          )
                        : "Not available"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Updated
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.updated_at
                        ? new Date(userData.updated_at).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            }
                          )
                        : "Not available"}
                    </p>
                  </div>
                  <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Created By
                    </label>
                    <p className="text-base text-gray-800 font-medium">
                      {userData.created_by || "System"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
