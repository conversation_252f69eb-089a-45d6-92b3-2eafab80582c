/* PDF Viewer Styles */
.react-pdf__Page {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  position: relative !important;
  overflow: visible !important;
  padding: 0 !important;
  margin: 0 !important;
}

.react-pdf__Page__canvas {
  display: block !important;
  max-width: 100% !important;
  height: auto !important;
  margin: 0 auto !important;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative !important;
  overflow: visible !important;
}

.react-pdf__Page__textContent {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: scale(1) !important;
  transform-origin: 0 0 !important;
  pointer-events: none !important;
  width: 100% !important;
  height: 100% !important;
}

.react-pdf__Page__annotations {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: scale(1) !important;
  transform-origin: 0 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.react-pdf__Document {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Ensure proper text rendering */
.react-pdf__Page__textContent span {
  color: transparent !important;
  position: absolute !important;
  white-space: pre !important;
  cursor: text !important;
  transform-origin: 0% 0% !important;
  font-family: inherit !important;
}

/* Fix for text layer positioning */
.react-pdf__Page__textContent .textLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  opacity: 0.2 !important;
  line-height: 1.0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fix for annotation layer */
.react-pdf__Page__annotations .annotationLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  width: 100% !important;
  height: 100% !important;
}

/* Additional fixes for proper PDF rendering */
.react-pdf__Page__textContent .textLayer > span {
  position: absolute !important;
  white-space: pre !important;
  color: transparent !important;
  font-family: sans-serif !important;
  transform-origin: 0% 0% !important;
  left: 0 !important;
  top: 0 !important;
  cursor: text !important;
  user-select: text !important;
}

/* Ensure PDF content is properly contained */
.react-pdf__Page__textContent .textLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  opacity: 0.2 !important;
  line-height: 1.0 !important;
  text-align: left !important;
  transform-origin: 0% 0% !important;
  z-index: 2 !important;
}

/* Fix for annotation layer positioning */
.react-pdf__Page__annotations .annotationLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  z-index: 3 !important;
}

/* Ensure PDF container doesn't clip content */
.react-pdf__Document {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  overflow: visible !important;
  width: 100% !important;
  height: auto !important;
}

/* Fix for any clipping issues */
.pdf-viewer-container {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* Ensure PDF page is fully visible */
.react-pdf__Page__canvas {
  max-height: none !important;
  object-fit: contain !important;
}
