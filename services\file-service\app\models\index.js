// app/models/index.js
import { Sequelize } from "sequelize";
import documentModel from "./document.model.js";
import dbConfig from "../../config/database.config.js";

const sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
  host: dbConfig.host,
  port: dbConfig.port,
  dialect: dbConfig.dialect,
  logging: dbConfig.logging,
  pool: dbConfig.pool,
  dialectOptions: dbConfig.dialectOptions,
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
});

const db = {};

// Initialize all models
db.Document = documentModel(sequelize);

// Initialize associations if needed
Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Export models and sequelize instance
export const Document = db.Document;
export { sequelize };
export default db;

