// app/services/blobStorage.service.js
import { BlobServiceClient } from "@azure/storage-blob";
import { blobConfig, uploadOptions } from "../../config/blob.config.js";
import logger from "../../config/logger.config.js";

/**
 * Azure Blob Storage Service
 * Handles all interactions with Azure Blob Storage
 */
class BlobStorageService {
  constructor() {
    // Try to validate configuration, but don't throw if missing
    try {
      blobConfig.validate();
      
      // Initialize blob service client
      this.blobServiceClient = BlobServiceClient.fromConnectionString(
        blobConfig.connectionString
      );
      
      // Get container client
      this.containerClient = this.blobServiceClient.getContainerClient(
        blobConfig.containerName
      );
      
      this.isConfigured = true;
    } catch (error) {
      logger.warn("Azure Blob Storage not configured:", error.message);
      this.isConfigured = false;
    }
  }

  /**
   * Ensure the container exists, create if it doesn't
   */
  async ensureContainer() {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }
    
    try {
      const exists = await this.containerClient.exists();
      if (!exists) {
        logger.info(`Creating container: ${blobConfig.containerName}`);
        await this.containerClient.create();
        logger.info(`Container created successfully: ${blobConfig.containerName}`);
      } else {
        logger.info(`Container already exists: ${blobConfig.containerName}`);
      }
    } catch (error) {
      logger.error("Error ensuring container exists:", error);
      throw error;
    }
  }

  /**
   * Upload a file to Azure Blob Storage
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} fileName - Name of the file
   * @param {string} contentType - MIME type of the file
   * @returns {Promise<Object>} Upload result with blob URL and metadata
   */
  async uploadFile(fileBuffer, fileName, contentType = "application/pdf") {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      const uploadOptions = {
        blobHTTPHeaders: {
          blobContentType: contentType,
          blobCacheControl: "public, max-age=31536000",
        },
      };

      await blobClient.upload(fileBuffer, fileBuffer.length, uploadOptions);
      
      logger.info(`File uploaded successfully: ${fileName}`);
      
      return {
        success: true,
        blobUrl: blobClient.url,
        fileName,
        size: fileBuffer.length,
        contentType,
      };
    } catch (error) {
      logger.error(`Error uploading file ${fileName}:`, error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Download a file from Azure Blob Storage
   * @param {string} fileName - Name of the file to download
   * @returns {Promise<ReadableStream>} File download stream
   */
  async downloadFile(fileName) {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      const exists = await blobClient.exists();
      if (!exists) {
        throw new Error(`File not found: ${fileName}`);
      }

      const [properties, downloadResponse] = await Promise.all([
        blobClient.getProperties(),
        blobClient.download(),
      ]);
      
      logger.info(`File downloaded successfully: ${fileName}`);
      
      return {
        stream: downloadResponse.readableStreamBody,
        properties,
      };
    } catch (error) {
      logger.error(`Error downloading file ${fileName}:`, error);
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  /**
   * List all files in the container
   * @returns {Promise<Array>} Array of file names and metadata
   */
  async listFiles() {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }

    try {
      const files = [];
      
      for await (const blob of this.containerClient.listBlobsFlat()) {
        files.push({
          name: blob.name,
          size: blob.properties.contentLength,
          contentType: blob.properties.contentType,
          lastModified: blob.properties.lastModified,
          etag: blob.properties.etag,
        });
      }
      
      logger.info(`Listed ${files.length} files`);
      
      return {
        success: true,
        files,
        count: files.length,
      };
    } catch (error) {
      logger.error("Error listing files:", error);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  /**
   * Delete a file from Azure Blob Storage
   * @param {string} fileName - Name of the file to delete
   * @returns {Promise<Object>} Deletion result
   */
  async deleteFile(fileName) {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      const exists = await blobClient.exists();
      if (!exists) {
        throw new Error(`File not found: ${fileName}`);
      }

      await blobClient.delete();
      
      logger.info(`File deleted successfully: ${fileName}`);
      
      return {
        success: true,
        message: `File deleted successfully: ${fileName}`,
      };
    } catch (error) {
      logger.error(`Error deleting file ${fileName}:`, error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Get blob URL for a file
   * @param {string} fileName - Name of the file
   * @returns {string} Blob URL
   */
  getBlobUrl(fileName) {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }
    const blobClient = this.containerClient.getBlockBlobClient(fileName);
    return blobClient.url;
  }

  /**
   * Check if a file exists
   * @param {string} fileName - Name of the file
   * @returns {Promise<boolean>} True if file exists
   */
  async fileExists(fileName) {
    if (!this.isConfigured) {
      throw new Error("Azure Blob Storage is not configured");
    }
    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      return await blobClient.exists();
    } catch (error) {
      logger.error(`Error checking file existence ${fileName}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export default new BlobStorageService();

