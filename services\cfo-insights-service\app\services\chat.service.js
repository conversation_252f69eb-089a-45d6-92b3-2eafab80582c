// app/services/chat.service.js
import crypto from "crypto";
import { chatWithContext } from "../utils/azureOpenAI.js";
import { downloadPdfBlob } from "../utils/blobClient.js";
import { extractTextFromPdfBuffer } from "../utils/pdfLoader.js";
import {
  createSession,
  getSession,
  appendHistory,
  getCachedDocText,
  setCachedDocText,
  deleteSession,
} from "../utils/memoryStore.js";
import { searchCompetitorData } from "../utils/azureSearchClient.js";
import { CHAT_ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { sanitizeToPlain, toBoolean } from "../utils/helpers.js";

/**
 * Helpers
 */

// sanitizeToPlain and toBoolean moved to app/utils/helpers.js

/** Ensure a valid active session, throw if missing */
function ensureSession(sessionId) {
  const session = getSession(sessionId);
  if (!session) throw new Error(CHAT_ERROR_MESSAGES.INVALID_SESSION_ID);
  return session;
}

/** Get document text (with caching). Downloads and extracts when needed. */
async function getDocumentText(filename) {
  const cached = getCachedDocText(filename);
  if (cached) return cached;

  const buffer = await downloadPdfBlob(filename);
  const text = await extractTextFromPdfBuffer(buffer);

  setCachedDocText(filename, text);
  return text;
}

/** Add org context to message when in summary mode and not already present */
function enhanceMessageWithOrganization(message, organization, summaryMode) {
  if (!summaryMode || !organization) return message;
  const regex = new RegExp(organization, "i");
  if (regex.test(message)) return message;
  return `${message}. Analyze the performance of ${organization} based on the provided financial document, and compare it with other relevant competitors in the same industry.`;
}

/** Collect competitor data for summary mode based on user intent and organization */
async function collectCompetitorData(
  processedMessage,
  organization,
  summaryMode,
  skipCompetitorSearch = false
) {
  if (!summaryMode || skipCompetitorSearch) return [];

  const isCompetitorQuery =
    /(compare|vs|versus|competitor|benchmark|against)/i.test(processedMessage);

  let competitorName = null;
  if (isCompetitorQuery) {
    const match = processedMessage.match(/vs\s+([\w\s&]+)/i);
    competitorName = match ? match[1].trim() : null;
  }

  if (competitorName) {
    return await searchCompetitorData(competitorName, 3000); // Reduced timeout to 3s
  }

  // Only search competitor data if explicitly requested via comparison keywords
  // Skip for default summary requests to improve speed
  if (organization && isCompetitorQuery) {
    return await searchCompetitorData(organization, 3000); // Reduced timeout to 3s
  }

  return [];
}

/**
 * Start chat session tied to a specific document.
 */
export async function startChat(filename) {
  if (!filename) throw new Error(CHAT_ERROR_MESSAGES.FILENAME_REQUIRED);
  await getDocumentText(filename);
  const sessionId = crypto.randomUUID();
  createSession({ sessionId, filename });
  return { sessionId, filename };
}

/**
 * Send message — handles competitor comparison & organization fallback.
 * @param {string} sessionId - Chat session ID
 * @param {string} userMessage - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summaryMode=false] - If true, use summary prompt; if false, use conversational chat prompt
 * @param {boolean} [skipCompetitorSearch=false] - If true, skip competitor search for faster responses
 */
export async function sendMessage(
  sessionId,
  userMessage,
  organization,
  summaryMode = false,
  skipCompetitorSearch = false
) {
  const { filename, history } = ensureSession(sessionId);

  const isSummary = toBoolean(summaryMode, false);
  
  // OPTIMIZATION: Check if this is a default summary request BEFORE enhancement
  // This prevents competitor search when org enhancement adds comparison keywords
  const originalMessageHasComparison = /(compare|vs|versus|competitor|benchmark|against)/i.test(userMessage);
  const isDefaultSummary = isSummary && !originalMessageHasComparison;
  const shouldSkipCompetitor = skipCompetitorSearch || isDefaultSummary;
  
  const processedMessage = enhanceMessageWithOrganization(
    userMessage,
    organization,
    isSummary
  );

  // OPTIMIZATION: Run document fetch and competitor search in parallel
  const [docText, competitorData] = await Promise.all([
    getDocumentText(filename),
    collectCompetitorData(processedMessage, organization, isSummary, shouldSkipCompetitor)
  ]);

  const competitorBlock = competitorData.length
    ? `--- COMPETITOR DATA (${
        competitorData.length
      } results) ---\n${competitorData
        .map(
          (s) =>
            `${s.title}\n${s.snippet}\n(Source: ${s.url || "public source"})`
        )
        .join("\n\n")}`
    : "";

  const fullContext = `--- DOCUMENT CONTEXT ---\n${docText}\n\n${competitorBlock}`;

  // OPTIMIZATION: Use lower max_tokens for summary mode to get faster responses
  const maxTokens = isSummary ? 1200 : 3000; // Reduced from 2000 to 1200 for faster generation

  const answer = await chatWithContext({
    contextText: fullContext,
    userQuestion: processedMessage,
    history,
    summaryMode: isSummary,
    organization,
    maxTokens,
  });

  appendHistory(sessionId, "user", processedMessage);
  appendHistory(sessionId, "assistant", answer);

  return { plainAnswer: sanitizeToPlain(answer), filename };
}

/**
 * End chat session — explicitly expire a sessionId
 */
export async function endChat(sessionId) {
  const id = String(sessionId || "").trim();
  if (!id) throw new Error(CHAT_ERROR_MESSAGES.SESSION_ID_REQUIRED);
  const removed = deleteSession(id);
  if (!removed) throw new Error(CHAT_ERROR_MESSAGES.INVALID_SESSION_ID);
  return { success: true, sessionId: id };
}
