import { createLogger } from "../utils/logger.util.js";
import {
  STATUS_CODE_INTERNAL_SERVER_ERROR,
  STATUS_CODE_SUCCESS,
} from "../utils/status_code.util.js";
import {
  LOGGER_NAMES,
  LOG_ACTIONS,
  CONTROLLER_MESSAGES,
} from "../utils/constants.util.js";
import * as sikkaService from "../services/sikka.service.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import * as kpisController from "./kpis.controller.js";

const logger = createLogger(LOGGER_NAMES.SIKKA_CONTROLLER);

/**
 * Generate Request key from Sikka
 * @route POST /api/sikka/request-key
 * @access Public
 */
export const requestKey = async (req, res) => {
  try {
    logger.info(LOG_ACTIONS.REQUESTING_KEY);
    const { office_id } = req.body;

    const requestKeyRecord = await sikkaService.generateRequestKey(office_id);

    logger.info(LOG_ACTIONS.REQUEST_KEY_SUCCESS);
    return res
      .status(STATUS_CODE_SUCCESS)
      .json(
        successResponse(
          CONTROLLER_MESSAGES.REQUEST_KEY_GENERATED,
          requestKeyRecord
        )
      );
  } catch (error) {
    logger.error(LOG_ACTIONS.REQUEST_KEY_FAILED, { error: error.message });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Get information about all Sequelize models
 * @route GET /api/sikka/tables
 * @access Public
 */
export const getTables = async (req, res) => {
  try {
    logger.info(LOG_ACTIONS.GETTING_MODELS_INFO);

    const modelsInfo = await sikkaService.fetchAllModelsInfo();

    logger.info(
      `Successfully retrieved information for ${modelsInfo.totalModels} models`
    );

    return res.status(STATUS_CODE_SUCCESS).json(
      successResponse(CONTROLLER_MESSAGES.MODELS_INFO_FETCHED, {
        modelsInfo,
      })
    );
  } catch (error) {
    logger.error(LOG_ACTIONS.ERROR_GETTING_MODELS_INFO, {
      error: error.message,
    });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Run all KPI endpoints and report their status
 * @route POST /api/sikka/all-kpi-reports
 * @access Public
 */
export const runAllKpiReports = async (req, res) => {
  // List of KPIs with their corresponding controller functions
  const kpiControllers = [
    {
      name: "account_receivables",
      controller: kpisController.accountReceivables,
    },
    {
      name: "treatment_plan_analysis",
      controller: kpisController.treatmentAnalysis,
    },
    // {
    //   name: "avg_daily_production",
    //   controller: kpisController.avgDailyProduction,
    // },
    {
      name: "no_show_appointments",
      controller: kpisController.noShowAppointments,
    },
    { name: "new_patients", controller: kpisController.newPatients },
    {
      name: "total_production_per_day",
      controller: kpisController.totalProductionPerDay,
    },
    {
      name: "total_production_by_dentist",
      controller: kpisController.totalProductionByDentist,
    },
    {
      name: "direct_restorations",
      controller: kpisController.directRestorations,
    },
    // {
    //   name: "hygiene_reappointment",
    //   controller: kpisController.hygieneReappointment,
    // },
    {
      name: "total_production_by_hygienist",
      controller: kpisController.totalProductionByHygienist,
    },
  ];

  let results = await Promise.all(
    kpiControllers.map(async (kpi) => {
      try {
        // Create a mock response object to capture the response
        const mockRes = {
          status: (code) => ({ json: (data) => ({ statusCode: code, data }) }),
          json: (data) => ({ data }),
        };

        // Call the controller function directly
        await kpi.controller(req, mockRes);
        return { name: kpi.name, status: "success" };
      } catch (err) {
        return {
          name: kpi.name,
          status: "fail",
          error: err.message,
        };
      }
    })
  );

  const successCount = results.filter((r) => r.status === "success").length;
  const failCount = results.filter((r) => r.status === "fail").length;
  const failedApis = results
    .filter((r) => r.status === "fail")
    .map((r) => ({ name: r.name, error: r.error }));

  return res.status(STATUS_CODE_SUCCESS).json(
    successResponse("Batch KPI run complete", {
      total: results.length,
      success: successCount,
      fail: failCount,
      failed: failedApis,
      summary: results.map((r) => ({ name: r.name, status: r.status })),
    })
  );
};
