// Cache for formatted messages to avoid re-processing
const formatCache = new Map();

// Function to format message content with proper HTML structure
export const formatMessage = (content) => {
  if (!content) return "";

  // Normalize content first (trim and fix common issues) before checking cache
  const normalizedContent = content.trim();

  // Check cache first
  if (formatCache.has(normalizedContent)) {
    return formatCache.get(normalizedContent);
  }

  let formattedContent = normalizedContent;

  // Check if content already contains HTML tags (like <h2>, <table>, etc.)
  const hasHtmlTags = /<[^>]+>/g.test(normalizedContent);

  if (hasHtmlTags) {
    // Content is already HTML - apply our design system and override inline styles
    // First, fix malformed HTML tags (remove spaces in tag names like < td -> <td)
    formattedContent = formattedContent
      // Fix ALL closing tag variations with spaces (e.g., </ table>, < / table>, </table >)
      // Must be done first before other tag fixes
      .replace(/<\s*\/\s*([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, '</$1>')
      .replace(/<\s+\/\s*([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, '</$1>')
      .replace(/<\s*\/\s+([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, '</$1>')
      // Fix opening tags with spaces (e.g., < td -> <td, < table -> <table)
      .replace(/<\s+([a-zA-Z][a-zA-Z0-9]*)/g, '<$1')
      // Fix opening tags with trailing spaces before closing bracket
      .replace(/<\s*([a-zA-Z][a-zA-Z0-9]*)\s+>/g, '<$1>')
      // Fix attributes with extra spaces (e.g., style = -> style=)
      .replace(/\s+=\s+/g, '=')
      // Fix values with spaces in style attributes (e.g., border : 1 px -> border:1px)
      .replace(/:\s+/g, ':')
      .replace(/;\s+/g, ';')
      // Fix spaces in CSS values (e.g., "1 px" -> "1px", "# 999" -> "#999")
      .replace(/style="([^"]*)"/gi, (match, styleContent) => {
        const fixedStyle = styleContent
          .replace(/\s+/g, ' ') // Normalize multiple spaces to single space
          .replace(/(\d+)\s+px/gi, '$1px') // Fix "1 px" -> "1px"
          .replace(/(#)\s+(\w+)/gi, '$1$2') // Fix "# 999" -> "#999"
          .replace(/\s*:\s*/g, ':') // Fix "border :" -> "border:"
          .replace(/\s*;\s*/g, ';') // Fix ";" spacing
          .trim();
        return `style="${fixedStyle}"`;
      })
      // Normalize single quotes to double quotes and strip any inline styles globally
      .replace(/style='([^']*)'/gi, 'style="$1"')
      .replace(/\sstyle=["'][^"']*["']/gi, '')
      // Fix cases where table cells aren't properly closed (add missing closing tags)
      .replace(/<td([^>]*)>\s*(.+?)\s*<td/gi, '<td$1>$2</td><td')
      .replace(/<td([^>]*)>\s*(.+?)\s*<\/tr>/gi, '<td$1>$2</td></tr>')
      .replace(/<td([^>]*)>\s*(.+?)\s*<\/table>/gi, '<td$1>$2</td></table>')
      // Ensure table rows are properly closed
      .replace(/<tr([^>]*)>\s*(.+?)\s*<tr/gi, '<tr$1>$2</tr><tr')
      .replace(/<tr([^>]*)>\s*(.+?)\s*<\/table>/gi, '<tr$1>$2</tr></table>');

    formattedContent = formattedContent
      // Remove inline styles from h2 and apply our design
      .replace(
        /<h2([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<h2${cleanAttrs ? ` ${cleanAttrs}` : ''} class="text-2xl font-bold text-gray-900 mt-8 mb-5 pb-3 border-b-2 border-indigo-200 first:mt-0">`;
        }
      )
      // Style h3 tags
      .replace(
        /<h3([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<h3${cleanAttrs ? ` ${cleanAttrs}` : ''} class="text-xl font-semibold text-gray-800 mt-6 mb-4">`;
        }
      )
      // Style tables - wrap in scrollable container, remove inline styles, apply our design
      // Match table tags (case insensitive, handle both single and double quotes)
      .replace(
        /<table([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<div class="overflow-x-auto my-6 rounded-xl border border-gray-300 shadow-lg bg-white"><table${cleanAttrs ? ` ${cleanAttrs}` : ''} class="w-full border-collapse min-w-full">`;
        }
      )
      // Close tables properly - handle case insensitive and ensure closing div
      .replace(/<\/table>/gi, '</table></div>')
      // Style table headers - remove inline styles, apply our design
      .replace(
        /<th([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<th${cleanAttrs ? ` ${cleanAttrs}` : ''} class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-bold text-sm px-4 py-3 text-left border-b-2 border-indigo-800 uppercase tracking-wide">`;
        }
      )
      // Style table cells - remove inline styles, apply our design
      .replace(
        /<td([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<td${cleanAttrs ? ` ${cleanAttrs}` : ''} class="px-4 py-3 border-b border-gray-200 text-gray-700 text-sm bg-white">`;
        }
      )
      // Style table rows - add hover and alternating colors
      .replace(
        /<tr([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<tr${cleanAttrs ? ` ${cleanAttrs}` : ''} class="hover:bg-indigo-50/50 transition-colors duration-150 even:bg-gray-50/30">`;
        }
      )
      // Style ordered lists - remove inline styles
      .replace(
        /<ol([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<ol${cleanAttrs ? ` ${cleanAttrs}` : ''} class="list-decimal list-inside space-y-3 my-5 text-gray-700 ml-4">`;
        }
      )
      // Style list items
      .replace(
        /<li([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<li${cleanAttrs ? ` ${cleanAttrs}` : ''} class="mb-3 text-base leading-relaxed">`;
        }
      )
      // Style paragraphs - remove inline styles
      .replace(
        /<p([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<p${cleanAttrs ? ` ${cleanAttrs}` : ''} class="text-gray-700 mb-4 leading-7 text-base">`;
        }
      )
      // Enhance bold tags - remove inline styles
      .replace(
        /<b([^>]*)>/gi,
        (match, attrs = '') => {
          const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, '').trim();
          return `<strong${cleanAttrs ? ` ${cleanAttrs}` : ''} class="font-bold text-indigo-700">`;
        }
      )
      .replace(/<\/b>/gi, '</strong>')
      // Remove extra spaces between elements
      .replace(/>\s+</g, '><');
  } else {
    // Content is plain text/markdown - convert to HTML
    formattedContent = formattedContent
      // Convert ### headers to h3 tags
      .replace(
        /^### (.+)$/gm,
        '<h3 class="text-lg font-semibold text-gray-800 mb-2 mt-4 first:mt-0">$1</h3>'
      )
      // Convert ## headers to h4 tags
      .replace(
        /^## (.+)$/gm,
        '<h4 class="text-base font-medium text-gray-700 mb-2 mt-3">$1</h4>'
      )
      // Convert # headers to h5 tags
      .replace(
        /^# (.+)$/gm,
        '<h5 class="text-sm font-medium text-gray-600 mb-1 mt-2">$1</h5>'
      )
      // Convert **text** to <strong>text</strong>
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      // Convert *text* to <em>text</em>
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      // Convert bullet points (•) to proper list items with styling
      .replace(
        /^•\s*(.+)$/gm,
        '<li class="text-sm text-gray-700 mb-1 flex items-start"><span class="text-blue-500 mr-2">•</span>$1</li>'
      )
      // Convert numbered lists
      .replace(
        /^\d+\. (.+)$/gm,
        '<li class="text-sm text-gray-700 mb-1 flex items-start"><span class="text-gray-500 mr-2">$&</span></li>'
      )
      // Convert line breaks to proper spacing
      .replace(/\n/g, "<br />")
      // Group consecutive list items into proper ul tags
      .replace(
        /(<li[^>]*>.*?<\/li>)(<br \/>)(<li[^>]*>.*?<\/li>)/gs,
        '<ul class="space-y-1 mb-3">$1$2$3</ul>'
      )
      // Clean up multiple line breaks
      .replace(/(<br \/>){3,}/g, "<br /><br />");
  }

  // Cache the result (limit cache size to prevent memory leaks)
  if (formatCache.size > 100) {
    formatCache.clear();
  }
  formatCache.set(normalizedContent, formattedContent);

  return formattedContent;
};

// Alias for backward compatibility
export const formatMessageContent = formatMessage;
