import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { CHAT_API_CONFIG, CHAT_DEFAULTS, CHAT_ERRORS } from "../../utils/constants/chat";

// Create optimized axios instance for CFO Insights Service (singleton pattern)
let chatApiClient = null;

const getChatApiClient = () => {
  if (!chatApiClient) {
    chatApiClient = axios.create({
      baseURL: CHAT_API_CONFIG.BASE_URL,
      headers: {
        "Content-Type": "application/json",
      },
      timeout: CHAT_API_CONFIG.TIMEOUT,
    });
  }
  return chatApiClient;
};

/**
 * Start a chat session for a specific document
 * POST /api/chat/start
 */
export const startChatSession = createAsyncThunk(
  "chat/startChatSession",
  async (payload, { rejectWithValue }) => {
    try {
      const { filename } = payload;
      
      // Enhanced validation
      if (!filename || typeof filename !== 'string' || filename.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.FILENAME_REQUIRED);
      }
      
      const apiClient = getChatApiClient();
      const response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.START, {
        filename: filename.trim(),
      });

      // Debug: Log the actual API response

      // Handle both success wrapper and direct response formats
      if (response.data.success) {
        // API returns { success: true, data: { sessionId, filename } }
        const { sessionId, filename: responseFilename } = response.data.data;
        
        if (!sessionId) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_INVALID);
        }
        
        return {
          sessionId,
          filename: responseFilename || filename,
        };
      } else if (response.data.sessionId) {
        // API returns { sessionId, filename } directly
        const { sessionId, filename: responseFilename } = response.data;
        
        if (!sessionId) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_INVALID);
        }
        
        return {
          sessionId,
          filename: responseFilename || filename,
        };
      } else {
        throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
      }
    } catch (error) {
      // Handle different error types
      
      // Enhanced error handling
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVICE_UNAVAILABLE);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }

      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.START_SESSION_FAILED
      );
    }
  }
);

/**
 * Send a message in an existing chat session
 * POST /api/chat/message
 */
export const sendChatMessage = createAsyncThunk(
  "chat/sendChatMessage",
  async (payload, { rejectWithValue }) => {
    try {
      const { 
        sessionId, 
        message, 
        organization
      } = payload;
      
      // Enhanced validation
      if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_EMPTY);
      }
      if (message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }
      
      const apiClient = getChatApiClient();
      const response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.MESSAGE, {
        sessionId: sessionId.trim(),
        message: message.trim(),
        organization: organization?.trim(),
      });

      // Debug: Log the actual API response

      // Handle both success wrapper and direct response formats
      if (response.data.success) {
        // API returns { success: true, data: { plainAnswer, filename } }
        const { plainAnswer, filename } = response.data.data;
        
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        
        return {
          answer: plainAnswer,
          filename: filename,
        };
      } else if (response.data.plainAnswer) {
        // API returns { plainAnswer, filename } directly
        const { plainAnswer, filename } = response.data;
        
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        
        return {
          answer: plainAnswer,
          filename: filename,
        };
      } else {
        throw new Error(response.data.message || CHAT_ERRORS.SEND_MESSAGE_FAILED);
      }
    } catch (error) {
      // Enhanced error handling
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.SEND_MESSAGE_FAILED
      );
    }
  }
);

/**
 * Send a summary request in an existing chat session (summary mode)
 * POST /api/chat/summary
 */
export const sendSummaryMessage = createAsyncThunk(
  "chat/sendSummaryMessage",
  async (payload, { rejectWithValue }) => {
    try {
      const { 
        sessionId, 
        message, 
        organization
      } = payload;

      // Validation
      if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (message && message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }

      const apiClient = getChatApiClient();
      const body = {
        sessionId: sessionId.trim(),
        organization: organization?.trim(),
      };
      if (typeof message === 'string' && message.trim().length > 0) {
        body.message = message.trim();
      }
      const response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.SUMMARY, body);

      if (response.data.success) {
        const { plainAnswer, filename } = response.data.data;
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        return { answer: plainAnswer, filename };
      } else if (response.data.plainAnswer) {
        const { plainAnswer, filename } = response.data;
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        return { answer: plainAnswer, filename };
      } else {
        throw new Error(response.data.message || CHAT_ERRORS.GET_SUMMARY_FAILED);
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.GET_SUMMARY_FAILED
      );
    }
  }
);