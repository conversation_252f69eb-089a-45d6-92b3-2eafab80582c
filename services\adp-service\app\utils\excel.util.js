import path from "path";
import xlsx from "xlsx";

export function extractDatesFromFileName(fileName) {
  const baseName = path.basename(fileName, path.extname(fileName));
  const parts = baseName.split("_");
  const toDatePart = parts.pop();
  const fromDatePart = parts.pop();

  function parseDate(str) {
    const match = str.match(/(\d{1,2})([a-zA-Z]+)(\d{4})/);
    if (!match) return null;
    const [, day, monthStr, year] = match;
    const month = new Date(`${monthStr} 1, 2000`).getMonth() + 1;
    return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
      2,
      "0"
    )}`;
  }

  return {
    from_date: parseDate(fromDatePart),
    to_date: parseDate(toDatePart),
  };
}

export function cleanAndParseNumeric(rawValue) {
  if (rawValue === null || rawValue === undefined || rawValue === "") return 0;
  let cleanedString = String(rawValue)
    .trim()
    .replace(/[,£$€%]/g, "");
  const parsedValue = parseFloat(cleanedString);
  return isNaN(parsedValue) ? 0 : parsedValue;
}

export function sanitizeColumnName(header, index) {
  if (!header) return `column_${index}`;
  const rawTrimmedHeader = header.trim();

  if (rawTrimmedHeader === "401K Retirement %")
    return "retirement_401k_percentage";
  if (index === 0) return "employee_name";
  if (index === 1) return "ssn";
  if (index === 2) return "tin";
  if (index === 3) return "pay_frequency";
  if (index === 4) return "department";

  if (index >= 5) {
    const isEarningGroup =
      rawTrimmedHeader.startsWith("Earning") ||
      ["Hours", "Rate", "Amount"].includes(rawTrimmedHeader);
    if (isEarningGroup) {
      const relativeIndex = index - 5;
      const groupIndex = Math.floor(relativeIndex / 4) + 1;
      const typeIndex = relativeIndex % 4;
      return (
        ["earning", "hours", "rate", "amount"][typeIndex] + `_${groupIndex}`
      );
    }
  }

  let cleanHeader = rawTrimmedHeader
    .replace(/[\s\.\-\/\#\%]/g, "_")
    .toLowerCase();
  cleanHeader = cleanHeader
    .replace(/[^a-z0-9_]/g, "")
    .replace(/__/g, "_")
    .replace(/_$/, "");
  return cleanHeader;
}

export function determineDataType(sqlName) {
  if (
    [
      "hours",
      "rate",
      "amount",
      "total",
      "pay",
      "fit",
      "socsec",
      "medcare",
      "tax",
      "deduction",
      "er",
      "futa",
      "sui",
      "payment_",
      "match",
      "401k",
      "roth",
      "pre_tax",
      "loan",
      "percentage",
    ].some((k) => sqlName.includes(k))
  ) {
    if (
      !["name", "id", "frequency", "tin", "ssn"].some((k) =>
        sqlName.includes(k)
      )
    )
      return "NUMERIC";
  }
  if (sqlName.includes("date")) return "DATE";
  return "VARCHAR(255)";
}

export function getDynamicColumnMapping(filePath) {
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const rawData = xlsx.utils.sheet_to_json(worksheet, {
    header: 1,
    range: 0,
    raw: false,
  });
  if (!rawData.length) throw new Error("Header row not found.");

  const headerRow = rawData[0];
  const mapping = [];
  const uniqueSqlNames = new Set();

  for (let i = 5; i < headerRow.length; i++) {
    const excelName = String(headerRow[i] || "").trim();
    const sqlName = sanitizeColumnName(excelName, i);
    if (sqlName.startsWith("column_") || uniqueSqlNames.has(sqlName)) continue;
    uniqueSqlNames.add(sqlName);
    const dataType = determineDataType(sqlName);
    mapping.push({ index: i, excelName, sqlName, dataType });
  }
  return mapping;
}

export function toSqlDate(dateString) {
  if (!dateString || !String(dateString).trim()) return null;
  const date = new Date(dateString);
  return isNaN(date) ? null : date.toISOString().split("T")[0];
}

export function readAndProcessFile(
  filePath,
  payrollColumnMapping,
  companyName,
  payrollMonth,
  from_date,
  to_date
) {
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const rawData = xlsx.utils.sheet_to_json(worksheet, {
    header: 1,
    range: 1,
    raw: false,
  });
  const records = rawData.filter((r) => r && r.length && r[0]);

  return records.map((row) => {
    const record = {
      employee_name: String(row[0] || "").trim(),
      ssn: String(row[1] || "").trim(),
      tin: String(row[2] || "").trim(),
      pay_frequency: String(row[3] || "").trim(),
      department: String(row[4] || "").trim(),
      company_name: companyName,
      payroll_month: payrollMonth,
      from_date,
      to_date,
    };
    payrollColumnMapping.forEach((col) => {
      const rawValue = row[col.index] || "";
      if (col.dataType === "NUMERIC")
        record[col.sqlName] = cleanAndParseNumeric(rawValue);
      else if (col.dataType === "DATE")
        record[col.sqlName] = toSqlDate(rawValue);
      else record[col.sqlName] = String(rawValue).trim();
    });
    return record;
  });
}
