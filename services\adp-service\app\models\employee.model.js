import { DataTypes } from "sequelize";
import { sequelize } from "../utils/db.util.js"; // Sequelize instance

export const Employee = sequelize.define(
  "adp_employee",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    employee_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    department: {
      type: DataTypes.STRING,
    },
    company_name: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: "adp_employee",
    timestamps: false,
    indexes: [
      {
        unique: false,
        fields: ["employee_name", "department"],
      },
    ],
  }
);
