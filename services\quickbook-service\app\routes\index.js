import express from 'express';
import quickbooksRoutes from './quickbooks.route.js';
import accountsRoutes from './accounts.route.js';
import reportsRoutes from './reports.route.js';

const router = express.Router();

// Route mappings for better organization

const ROUTE_MAPPINGS = {
  quickbooks: { path: '/quickbooks', router: quickbooksRoutes },
  reports: { path: '/reports', router: reportsRoutes },
  accounts: { path: '/accounts', router: accountsRoutes }
};




Object.values(ROUTE_MAPPINGS).forEach(({ path, router: routeRouter }) => {
  router.use(path, routeRouter);
});

export default router;
