import { DataTypes } from "sequelize";
import {
  TABLE_NAMES,
  MODEL_FIELDS,
  INDEX_NAMES,
} from "../utils/constants.util.js";

const AppUserModel = (sequelize) => {
  const AppUser = sequelize.define(
    TABLE_NAMES.APP_USER,
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      full_name: {
        type: DataTypes.TEXT,
      },
      email: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      phone_number: {
        type: DataTypes.TEXT,
      },
      password_hash: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      organization_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      role: {
        type: DataTypes.TEXT,
      },
      last_login: {
        type: DataTypes.DATE,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      access_token: {
        type: DataTypes.TEXT,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: TABLE_NAMES.APP_USER,
      timestamps: true,
      schema: "Authentication",
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: INDEX_NAMES.UNIQUE_EMAIL_PER_TENANT,
          unique: true,
          fields: ["organization_id", "email"],
        },
      ],
    }
  );

  return AppUser;
};

export default AppUserModel;
