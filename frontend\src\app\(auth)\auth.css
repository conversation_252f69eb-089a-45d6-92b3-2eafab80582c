/* Auth Pages Styles */
html, body {
  height: 100%;
  
  margin: 0;
  padding: 0;
}

.auth-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

@media (min-width: 768px) {
  .auth-layout {
    flex-direction: row;
  }
}

.left-column {
  display: flex;
  width: 100%;
  height: 40vh;
  min-height: 300px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(0deg, #18181B, #18181B);
  position: relative;
  opacity: 1;
  padding: 20px;
}

@media (min-width: 768px) {
  .left-column {
    width: 50%;
    height: 100vh;
    padding: 40px;
  }
}

.right-column {
  display: flex;
  width: 100%;
  height: 60vh;
  min-height: 400px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  padding: 20px;
  position: relative;
}

@media (min-width: 768px) {
  .right-column {
    width: 50%;
    height: 100vh;
    gap: 24px;
    padding: 20px;
  }
}

.form-container {
  display: flex;
  padding: 24px 32px;
  align-items: center;
  gap: 10px;
  border-radius: 10px;
  background: #FFF;
  box-shadow: 0 0 10px 0 #E2EBEB;
  width: 100%;
  max-width: 450px;
}

.login-form {
  display: flex;
  width: 100%;
  max-width: 400px;
  flex-direction: column;
  align-items: center;
}

.frame-element {
  display: flex;
  width: 379px;
  height: 365px;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 10;
  
}

/* Color Variables */
:root {
  --background-dark: #141329;
  --text-dark: #5F6E69;
  --background-light: #FFFFFF;
  --selection-gray: #9AA4AE;
  --blue-primary: #3782F5;
  --dark-gray: #3B3A3B;
  --light-gray: #98999E;
  --very-light-gray: #E5E5E5;
  --lighter-gray: #EEEEEE;
  --off-white: #F5FAFA;
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  .left-column {
    width: 50%;
    height: 100vh;
    padding: 40px;
    justify-content: space-between;
    opacity: 1;
    background: linear-gradient(0deg, #18181B, #18181B);
  }
  
  .right-column {
    padding: 40px;
  }
  
  .form-container {
    padding: 32px 40px;
    max-width: 500px;
  }
  
  .login-form {
    max-width: 450px;
  }
}

/* Desktop (1024px to 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  .left-column {
    width: 50%;
    height: 100vh;
    padding: 40px;
    justify-content: space-between;
    opacity: 1;
    background: linear-gradient(0deg, #18181B, #18181B);
  }
  
  .right-column {
    padding: 30px;
  }
  
  .form-container {
    padding: 28px 36px;
    max-width: 480px;
  }
}

/* Tablet (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .auth-layout {
    flex-direction: column;
    height: 100vh;
    
  }
  
  .left-column {
    height: 250px;
    padding: 30px;
    justify-content: center;
    
  }
  
  .right-column {
    padding: 30px;
    height: calc(100vh - 250px);
    
  }
  
  .form-container {
    padding: 24px 28px;
    max-width: 450px;
  }
  
  .frame-element {
    display: none;
  }
}

/* Mobile Large (481px to 767px) */
@media (min-width: 481px) and (max-width: 767px) {
  .auth-layout {
    flex-direction: column;
    height: 100vh;
    
  }
  
  .left-column {
    height: 200px;
    padding: 20px;
    justify-content: center;
    
  }
  
  .right-column {
    padding: 20px;
    height: calc(100vh - 200px);
    
  }
  
  .form-container {
    padding: 20px 24px;
    max-width: 400px;
  }
  
  .login-form {
    max-width: 100%;
  }
  
  .frame-element {
    display: none;
  }
}

/* Mobile Small (320px to 480px) */
@media (max-width: 480px) {
  .auth-layout {
    flex-direction: column;
    height: 100vh;
    
  }
  
  .left-column {
    height: 180px;
    padding: 16px;
    justify-content: center;
    
  }
  
  .right-column {
    padding: 16px;
    height: calc(100vh - 180px);
    
  }
  
  .form-container {
    padding: 16px 20px;
    max-width: 100%;
    margin: 0 8px;
  }
  
  .login-form {
    max-width: 100%;
  }
  
  .frame-element {
    display: none;
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .auth-layout {
    height: 100vh;
    
  }
  
  .left-column {
    padding: 12px;
    height: 150px;
    
  }
  
  .right-column {
    padding: 12px;
    height: calc(100vh - 150px);
    
  }
  
  .form-container {
    padding: 12px 16px;
    margin: 0 4px;
  }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .auth-layout {
    flex-direction: row;
    height: 100vh;
    
  }
  
  .left-column {
    height: 100vh;
    flex: 0.4;
    
  }
  
  .right-column {
    flex: 0.6;
    height: 100vh;
    
  }
  
  .form-container {
    max-width: 350px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .form-container {
    box-shadow: 0 0 20px 0 rgba(226, 235, 235, 0.3);
  }
} 