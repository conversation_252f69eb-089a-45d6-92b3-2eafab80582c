import sequelize from "../../config/postgres.js";
import EmployeeModel from "./employee.model.js";
import PayrollDetailsModel from "./payroll_details.model.js";

const models = {
  adp_employee: EmployeeModel(sequelize),
  adp_payroll_details: PayrollDetailsModel(sequelize),
};

// Set up associations
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Export individual models for convenience
export const {
  adp_employee: Employee,
  adp_payroll_details: PayrollDetails,
} = models;

// Export all models and sequelize instance
export { sequelize };
export default models;
