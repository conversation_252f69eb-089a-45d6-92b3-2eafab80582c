// DATABASE UTILITIES - Centralized Database Operations
import { createLogger } from "./logger.util.js";
import { LOGGER_NAMES, PAGINATION } from "./constants.util.js";

const logger = createLogger(LOGGER_NAMES.DB_UTILS);

/**
 * Create a new record
 * @param {Object} model - Sequelize model
 * @param {Object} data - Data to create
 * @returns {Promise<Object>} Created record
 */
export const create = async (model, data) => {
  const record = await model.create(data);
  logger.info(`${model.name} created successfully with ID: ${record.id}`);
  return record;
};

/**
 * Find all records with optional query conditions
 * @param {Object} model - Sequelize model
 * @param {Object} query - Query options (where, include, order, etc.)
 * @returns {Promise<Array>} Array of records
 */
export const findAll = async (model, query = {}) => {
  const getALLQuery = {
    ...query,
    where: { is_deleted: false },
    order: [["created_at", "ASC"]],
  };
  const records = await model.findAll(getALLQuery);
  logger.info(`Found ${records.length} ${model.name} records`);
  return records;
};

/**
 * Find a record by ID with optional query conditions
 * @param {Object} model - Sequelize model
 * @param {number} id - Record ID
 * @param {Object} query - Additional query options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findById = async (model, id, query = {}) => {
  const record = await model.findOne({
    ...query,
    where: {
      id,
      is_deleted: false,
    },
  });
  if (record) {
    logger.info(`${model.name} found with ID: ${id}`);
  } else {
    logger.warn(`${model.name} not found with ID: ${id}`);
  }
  return record;
};

/**
 * Update a record by ID
 * @param {Object} model - Sequelize model
 * @param {number} id - Record ID
 * @param {Object} data - Data to update
 * @returns {Promise<Object|null>} Updated record or null
 */
export const update = async (model, id, data) => {
  const existingRecord = await model.findOne({
    where: { id },
  });
  if (!existingRecord) {
    logger.warn(`${model.name} not found or already deleted with ID: ${id}`);
    return null;
  }

  await model.update(data, {
    where: { id },
  });

  const updatedRecord = await model.findOne({
    where: { id },
  });

  logger.info(`${model.name} updated successfully with ID: ${id}`);
  return updatedRecord;
};

/**
 * Soft delete a record by setting is_deleted flag
 * @param {Object} model - Sequelize model
 * @param {number} id - Record ID
 * @returns {Promise<boolean>} Success status
 */
export const softDelete = async (model, id) => {
  const existingRecord = await model.findOne({
    where: { id },
  });

  if (!existingRecord) {
    logger.warn(`${model.name} not found or already deleted with ID: ${id}`);
    return false;
  }

  logger.info(`${model.name} soft deleted successfully with ID: ${id}`);
  return true;
};

/**
 * Hard delete a record permanently
 * @param {Object} model - Sequelize model
 * @param {number} id - Record ID
 * @returns {Promise<boolean>} Success status
 */
export const destroy = async (model, id) => {
  const existingRecord = await model.findOne({
    where: { id, is_deleted: false },
  });

  if (!existingRecord) {
    logger.warn(`${model.name} not found or already deleted with ID: ${id}`);
    return false;
  }

  await model.destroy({
    where: { id, is_deleted: false },
  });

  logger.info(`${model.name} hard deleted successfully with ID: ${id}`);
  return true;
};

/**
 * Check if a record exists by ID
 * @param {Object} model - Sequelize model
 * @param {number} id - Record ID
 * @returns {Promise<boolean>} True if exists, false otherwise
 */
export const exists = async (model, id) => {
  const record = await model.findOne({
    where: { id, is_deleted: false },
    attributes: ["id"],
  });
  return !!record;
};

/**
 * Find one record with query conditions
 * @param {Object} model - Sequelize model
 * @param {Object} query - Query options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findOne = async (model, query = {}) => {
  const record = await model.findOne({
    ...query,
    where: { ...(query.where || {}) },
  });
  return record;
};

/**
 * Count records with optional conditions
 * @param {Object} model - Sequelize model
 * @param {Object} where - Where conditions
 * @returns {Promise<number>} Count of records
 */
export const count = async (model, where = {}) => {
  const countResult = await model.count({
    where: { is_deleted: false, ...where },
  });
  return countResult;
};

/**
 * Find and count records with pagination
 * @param {Object} model - Sequelize model
 * @param {Object} query - Query options including pagination
 * @returns {Promise<Object>} Records with count
 */
export const findAndCountAll = async (model, query = {}) => {
  const result = await model.findAndCountAll({
    ...query,
    where: { ...(query.where || {}) },
  });
  return result;
};

/**
 * Paginate records with standard pagination
 * @param {Object} model - Sequelize model
 * @param {Object} options - Pagination options
 * @returns {Promise<Object>} Paginated result
 */
export const paginate = async (model, options = {}) => {
  const {
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    include = [],
    order = [["created_at", "ASC"]],
    attributes,
    where = {},
  } = options;

  const offset = (page - 1) * limit;
  const queryOptions = {
    include,
    order,
    attributes,
    limit: parseInt(limit),
    offset: parseInt(offset),
    where: { is_deleted: false, ...where },
  };
  const { rows, count } = await model.findAndCountAll(queryOptions);
  const totalPages = Math.ceil(count / limit);

  return {
    data: rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

/**
 * Bulk create records
 * @param {Object} model - Sequelize model
 * @param {Array} data - Array of data objects
 * @param {Object} options - Bulk create options
 * @returns {Promise<Array>} Created records
 */
export const bulkCreate = async (model, data, options = {}) => {
  const records = await model.bulkCreate(data, {
    ...options,
    returning: true,
  });
  logger.info(`Bulk created ${records.length} ${model.name} records`);
  return records;
};

/**
 * Bulk update records
 * @param {Object} model - Sequelize model
 * @param {Array} data - Array of data objects
 * @param {Object} options - Bulk update options
 * @returns {Promise<Array>} Updated records
 */
export const bulkUpdate = async (model, data, options = {}) => {
  const records = await model.bulkCreate(data, {
    ...options,
    updateOnDuplicate: Object.keys(data[0] || {}),
    returning: true,
  });
  logger.info(`Bulk updated ${records.length} ${model.name} records`);
  return records;
};

/**
 * Create include configuration for Sequelize associations
 * @param {Object} model - Sequelize model
 * @param {string} as - Association alias
 * @returns {Object} Include configuration
 */
export const getIncludeConfig = (model, as) => {
  return {
    model,
    as,
    required: false,
  };
};

/**
 * Find one record in specific organization schema
 * @param {Object} model - Sequelize model
 * @param {string} organizationName - Organization name for schema
 * @param {Object} options - Query options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findOneInSchema = async (model, schemaName, options = {}) => {
  try {
    // Set search path to organization schema
    await model.sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Perform query
    const record = await model.findOne(options);

    // Reset search path
    await model.sequelize.query("SET search_path TO public");

    logger.info(
      `Found ${record ? "1" : "0"} ${
        model.name
      } record in schema: ${schemaName}`
    );
    return record;
  } catch (error) {
    // Reset search path on error
    try {
      await model.sequelize.query("SET search_path TO public");
    } catch (resetError) {
      logger.error(`Error resetting search path: ${resetError.message}`);
    }

    logger.error(
      `Error finding ${model.name} record in organization schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Create one record in specific organization schema
 * @param {Object} model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {Object} data - Data to create
 * @returns {Promise<Object>} Created record
 */
export const createInSchema = async (model, schemaName, data) => {
  try {
    // Set search path to organization schema
    await model.sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Create the record
    const record = await model.create(data);

    // Reset search path
    await model.sequelize.query("SET search_path TO public");

    logger.info(
      `Created ${model.name} record with ID ${record.id} in schema: ${schemaName}`
    );
    return record;
  } catch (error) {
    // Reset search path on error
    try {
      await model.sequelize.query("SET search_path TO public");
    } catch (resetError) {
      logger.error(`Error resetting search path: ${resetError.message}`);
    }

    logger.error(
      `Error creating ${model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Update one record in specific organization schema
 * @param {Object} model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {string|number} id - Record ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object|null>} Updated record or null
 */
export const updateInSchema = async (model, schemaName, id, updateData) => {
  try {
    // Set search path to organization schema
    await model.sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Find the record first
    const record = await model.findByPk(id);
    if (!record) {
      logger.info(`Record with ID ${id} not found in schema: ${schemaName}`);
      return null;
    }

    // Update the record
    await record.update(updateData);

    // Reset search path
    await model.sequelize.query("SET search_path TO public");

    logger.info(
      `Updated ${model.name} record with ID ${id} in schema: ${schemaName}`
    );
    return record;
  } catch (error) {
    // Reset search path on error
    try {
      await model.sequelize.query("SET search_path TO public");
    } catch (resetError) {
      logger.error(`Error resetting search path: ${resetError.message}`);
    }

    logger.error(
      `Error updating ${model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Bulk create records in specific organization schema
 * @param {Object} model - Sequelize model
 * @param {Array} data - Array of data objects
 * @param {string} organizationName - Organization name for schema
 * @param {Object} options - Bulk create options
 * @returns {Promise<Array>} Created records
 */
export const bulkCreateInSchema = async (
  model,
  data,
  organizationName,
  options = {}
) => {
  const {
    getOrganizationSchemaName,
    createOrganizationSchema,
    createTableInSchema,
  } = await import("./schema.util.js");

  try {
    // Get schema name from organization name
    const schemaName = getOrganizationSchemaName(organizationName);

    // Ensure schema exists
    await createOrganizationSchema(organizationName);

    // Ensure table exists in schema
    await createTableInSchema(schemaName, model.tableName, model);

    // Set search path to organization schema
    await model.sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Perform bulk create
    const records = await model.bulkCreate(data, {
      ...options,
      returning: true,
    });

    // Reset search path
    await model.sequelize.query("SET search_path TO public");

    logger.info(
      `Bulk created ${records.length} ${model.name} records in schema: ${schemaName}`
    );
    return records;
  } catch (error) {
    // Reset search path on error
    try {
      await model.sequelize.query("SET search_path TO public");
    } catch (resetError) {
      logger.error(`Error resetting search path: ${resetError.message}`);
    }

    logger.error(
      `Error bulk creating ${model.name} records in organization schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Find all records in specific organization schema
 * @param {Object} model - Sequelize model
 * @param {string} organizationName - Organization name for schema
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Found records
 */
export const findAllInSchema = async (
  model,
  organizationName,
  options = {}
) => {
  const { getOrganizationSchemaName } = await import("./schema.util.js");

  try {
    const schemaName = getOrganizationSchemaName(organizationName);

    // Set search path to organization schema
    await model.sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Perform query
    const records = await model.findAll(options);

    // Reset search path
    await model.sequelize.query("SET search_path TO public");

    logger.info(
      `Found ${records.length} ${model.name} records in schema: ${schemaName}`
    );
    return records;
  } catch (error) {
    // Reset search path on error
    try {
      await model.sequelize.query("SET search_path TO public");
    } catch (resetError) {
      logger.error(`Error resetting search path: ${resetError.message}`);
    }

    logger.error(
      `Error finding ${model.name} records in organization schema: ${error.message}`
    );
    throw error;
  }
};

export default {
  create,
  findAll,
  findById,
  update,
  softDelete,
  destroy,
  exists,
  findOne,
  count,
  findAndCountAll,
  paginate,
  bulkCreate,
  bulkUpdate,
  getIncludeConfig,
  bulkCreateInSchema,
  findAllInSchema,
  findOneInSchema,
  createInSchema,
  updateInSchema,
};
