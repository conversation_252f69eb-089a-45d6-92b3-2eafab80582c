"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { UploadCloud } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import { formatDateToMMDDYYYY, getMonthDateRange } from "@/utils/methods";

export default function UploadCard({
  title,
  icon,
  onSync,
  isLoading = false,
  lastSync = null,
  isFinancial = false,
  realmId = null,
  schemaName = null,
  selectedMonth = null,
}) {
  const [files, setFiles] = useState([]);
  const [synced, setSynced] = useState(false);
  const [lastSyncedDate, setLastSyncedDate] = useState(lastSync);

  const handleSync = async () => {
    if (onSync) {
      if (isFinancial) {
        // For financial cards, call the sync function directly without files
        await onSync();
      } else {
        // For other cards, pass files as before
        onSync(files);
      }
    }
    setSynced(true);
    const { endDate } = getMonthDateRange(selectedMonth);
    setLastSyncedDate(formatDateToMMDDYYYY(endDate));
  };

  return (
    <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-white shadow-sm flex flex-col h-full">
      <div className="mb-4 flex justify-center">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
      {lastSyncedDate && (
        <div className="mb-4 flex items-center justify-center gap-2">
          <span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
          <span className="text-xs text-green-700 font-medium bg-green-50 px-2 py-0.5 rounded">
            {BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.LAST_SYNCED_LABEL}{" "}
            {lastSyncedDate}
          </span>
        </div>
      )}
      {/* Sync Button */}
      <Button
        onClick={handleSync}
        disabled={
          isLoading || synced || (isFinancial && (!realmId || !schemaName))
        }
        variant="default"
        className="mt-auto w-full"
        leftIcon={<UploadCloud className="w-4 h-4" />}
      >
        {isLoading
          ? BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNCING
          : synced
          ? BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNCED
          : isFinancial && (!realmId || !schemaName)
          ? "Loading..."
          : BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNC}
      </Button>
    </div>
  );
}
