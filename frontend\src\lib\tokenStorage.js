import Cookies from "js-cookie";

// Token storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: "accessToken",
  REFRESH_TOKEN: "refreshToken",
  USER_DATA: "userData",
  RESET_TOKEN: "resetToken",
};

/**
 * Check if localStorage is available (client-side only)
 * @returns {boolean} True if localStorage is available
 */
const isLocalStorageAvailable = () => {
  return typeof window !== "undefined" && window.localStorage;
};

/**
 * Token storage utility for localStorage operations
 */
export const tokenStorage = {
  /**
   * Store access token in localStorage
   * @param {string} token - Access token
   */
  setAccessToken: (token) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
        // Also set as cookie for SSR middleware
        Cookies.set(STORAGE_KEYS.ACCESS_TOKEN, token, {
          expires: 1, // 1 day
          secure: process.env.NODE_ENV === "production",
          sameSite: "Strict",
        });
      }
    } catch (error) {
      console.error("Error storing access token:", error);
    }
  },

  /**
   * Get access token from localStorage
   * @returns {string|null} Access token or null
   */
  getAccessToken: () => {
    try {
      if (isLocalStorageAvailable()) {
        return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      }
      return null;
    } catch (error) {
      console.error("Error getting access token:", error);
      return null;
    }
  },

  /**
   * Store refresh token in localStorage
   * @param {string} token - Refresh token
   */
  setRefreshToken: (token) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);
        // Also set as cookie for SSR middleware
        Cookies.set(STORAGE_KEYS.REFRESH_TOKEN, token, {
          expires: 7, // 7 days
          secure: process.env.NODE_ENV === "production",
          sameSite: "Strict",
        });
      }
    } catch (error) {
      console.error("Error storing refresh token:", error);
    }
  },

  /**
   * Get refresh token from localStorage
   * @returns {string|null} Refresh token or null
   */
  getRefreshToken: () => {
    try {
      if (isLocalStorageAvailable()) {
        return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      }
      return null;
    } catch (error) {
      console.error("Error getting refresh token:", error);
      return null;
    }
  },

  /**
   * Store reset token in localStorage
   * @param {string} token - Reset token
   */
  setResetToken: (token) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.RESET_TOKEN, token);
      }
    } catch (error) {
      console.error("Error storing reset token:", error);
    }
  },

  /**
   * Get reset token from localStorage
   * @returns {string|null} Reset token or null
   */
  getResetToken: () => {
    try {
      if (isLocalStorageAvailable()) {
        return localStorage.getItem(STORAGE_KEYS.RESET_TOKEN);
      }
      return null;
    } catch (error) {
      console.error("Error getting reset token:", error);
      return null;
    }
  },

  /**
   * Store user data in localStorage
   * @param {Object} userData - User data object
   */
  setUserData: (userData) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      }
    } catch (error) {
      console.error("Error storing user data:", error);
    }
  },

  /**
   * Get user data from localStorage
   * @returns {Object|null} User data object or null
   */
  getUserData: () => {
    try {
      if (isLocalStorageAvailable()) {
        const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
        return userData ? JSON.parse(userData) : null;
      }
      return null;
    } catch (error) {
      console.error("Error getting user data:", error);
      return null;
    }
  },

  /**
   * Store all authentication data
   * @param {Object} authData - Authentication data object
   */
  setAuthData: (authData) => {
    const { accessToken, refreshToken, user, resetToken } = authData;

    if (accessToken) {
      tokenStorage.setAccessToken(accessToken);
    }

    if (refreshToken) {
      tokenStorage.setRefreshToken(refreshToken);
    }

    if (resetToken) {
      tokenStorage.setResetToken(resetToken);
    }

    if (user) {
      tokenStorage.setUserData(user);
    }
  },

  /**
   * Get all authentication data
   * @returns {Object} Authentication data object
   */
  getAuthData: () => {
    return {
      accessToken: tokenStorage.getAccessToken(),
      refreshToken: tokenStorage.getRefreshToken(),
      resetToken: tokenStorage.getResetToken(),
      user: tokenStorage.getUserData(),
    };
  },

  /**
   * Clear all authentication data from localStorage
   */
  clearAuthData: () => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_DATA);
        localStorage.removeItem(STORAGE_KEYS.RESET_TOKEN);

        // Also clear cookies
        Cookies.remove(STORAGE_KEYS.ACCESS_TOKEN);
        Cookies.remove(STORAGE_KEYS.REFRESH_TOKEN);
      }
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
  },

  /**
   * Check if user is authenticated
   * @returns {boolean} True if user is authenticated
   */
  isAuthenticated: () => {
    const accessToken = tokenStorage.getAccessToken();
    const user = tokenStorage.getUserData();
    return !!(accessToken && user);
  },

  /**
   * Get user role
   * @returns {string|null} User role or null
   */
  getUserRole: () => {
    const user = tokenStorage.getUserData();
    return user?.role?.name || null;
  },

  /**
   * Check if token is expired
   * @param {string} token - JWT token
   * @returns {boolean} True if token is expired
   */
  isTokenExpired: (token) => {
    if (!token) return true;

    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error("Error checking token expiration:", error);
      return true;
    }
  },

  /**
   * Check if access token is expired
   * @returns {boolean} True if access token is expired
   */
  isAccessTokenExpired: () => {
    const accessToken = tokenStorage.getAccessToken();
    return tokenStorage.isTokenExpired(accessToken);
  },

  /**
   * Check if refresh token is expired
   * @returns {boolean} True if refresh token is expired
   */
  isRefreshTokenExpired: () => {
    const refreshToken = tokenStorage.getRefreshToken();
    return tokenStorage.isTokenExpired(refreshToken);
  },

  /**
   * Decode JWT token and extract payload
   * @param {string} token - JWT token
   * @returns {Object|null} Decoded payload or null
   */
  decodeToken: (token) => {
    if (!token) {
      return null;
    }

    try {
      // Check if token has the correct JWT format (3 parts separated by dots)
      const parts = token.split(".");
      if (parts.length !== 3) {
        console.error(
          "Invalid JWT token format - expected 3 parts, got:",
          parts.length
        );
        return null;
      }

      // Decode the payload (middle part)
      const payload = JSON.parse(atob(parts[1]));
      return payload;
    } catch (error) {
      console.error("Error decoding token:", error);
      console.error(
        "Token that failed to decode:",
        token.substring(0, 50) + "..."
      );
      return null;
    }
  },

  /**
   * Get user data from access token
   * @returns {Object|null} User data from token or null
   */
  getUserDataFromToken: () => {
    const accessToken = tokenStorage.getAccessToken();
    if (!accessToken) {
      return null;
    }

    const payload = tokenStorage.decodeToken(accessToken);

    // Transform JWT payload to match expected user structure
    if (payload) {
      // Get role from the stored user data in localStorage first, fallback to JWT
      const storedUser = tokenStorage.getUserData();
      const userData = {
        id: payload.userId,
        email: payload.email,
        role:
          storedUser?.role ||
          (payload.roles && payload.roles.length > 0
            ? payload.roles[0]
            : "user"),
        roles: payload.roles || [],
        organization_id: payload.organization_id,
        schema_name: payload.schema_name,
        iat: payload.iat,
        exp: payload.exp,
      };
      return userData;
    }

    return null;
  },

  /**
   * Get user data from refresh token
   * @returns {Object|null} User data from token or null
   */
  getUserDataFromRefreshToken: () => {
    const refreshToken = tokenStorage.getRefreshToken();
    if (!refreshToken) return null;

    const payload = tokenStorage.decodeToken(refreshToken);
    return payload || null;
  },
};

export default tokenStorage;
