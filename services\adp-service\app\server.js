import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import { createLogger } from "./utils/logger.util.js";
import {
  SERVER_CONSTANTS,
  SERVER_MESSAGES,
  REQUEST_CONFIG,
} from "./utils/constants.util.js";
import { successResponse, errorResponse } from "./utils/response.util.js";
import { testConnection } from "../config/postgres.js";
import routes from "./routes/index.route.js";

// Load environment variables
dotenv.config();

const app = express();
const logger = createLogger(SERVER_CONSTANTS.ADP_SERVER_LABEL);
const PORT = process.env.ADP_SERVICE_PORT || 3004;
const NODE_ENV = process.env.NODE_ENV || SERVER_CONSTANTS.ENVIRONMENT_DEVELOPMENT;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || [SERVER_CONSTANTS.DEFAULT_ALLOWED_ORIGIN],
  credentials: true,
}));
app.use(morgan('combined'));
app.use(express.json({ limit: REQUEST_CONFIG.JSON_LIMIT }));
app.use(express.urlencoded({ 
  extended: true, 
  limit: REQUEST_CONFIG.URL_ENCODED_LIMIT 
}));

// Health check endpoint
app.get(SERVER_CONSTANTS.HEALTH_ENDPOINT, async (req, res) => {
  try {
    const dbHealth = await testConnection();
    res.json(successResponse(SERVER_MESSAGES.ADP_SERVICE_HEALTHY, {
      service: 'adp-service',
      version: '1.0.0',
      environment: NODE_ENV,
      database: dbHealth.success ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString(),
    }));
  } catch (error) {
    res.status(500).json(errorResponse(SERVER_MESSAGES.INTERNAL_SERVER_ERROR, {
      service: 'adp-service',
      database: 'error',
      error: error.message,
    }));
  }
});

// API routes
app.use(SERVER_CONSTANTS.API_ENDPOINT, routes);

// 404 handler
app.use(SERVER_CONSTANTS.CATCH_ALL_ROUTE, (req, res) => {
  res.status(404).json(errorResponse(SERVER_MESSAGES.ROUTE_NOT_FOUND, {
    path: req.path,
    method: req.method,
  }));
});

// Global error handler
app.use((error, req, res, next) => {
  logger.error(SERVER_MESSAGES.UNHANDLED_ERROR, {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
  });
  
  res.status(500).json(errorResponse(SERVER_MESSAGES.INTERNAL_SERVER_ERROR, {
    error: NODE_ENV === SERVER_CONSTANTS.ENVIRONMENT_DEVELOPMENT ? error.message : undefined,
  }));
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`${SERVER_MESSAGES.ADP_SERVICE_STARTED} ${PORT}`);
  logger.info(`${SERVER_MESSAGES.HEALTH_CHECK_URL} http://localhost:${PORT}${SERVER_CONSTANTS.HEALTH_ENDPOINT}`);
  logger.info(`${SERVER_MESSAGES.API_ENDPOINT_URL} http://localhost:${PORT}${SERVER_CONSTANTS.API_ENDPOINT}`);
  logger.info(`${SERVER_MESSAGES.ENVIRONMENT_INFO} ${NODE_ENV}`);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);
  server.close(() => {
    logger.info(SERVER_MESSAGES.PROCESS_TERMINATED);
    process.exit(0);
  });
};

process.on('SIGTERM', () => gracefulShutdown(SERVER_MESSAGES.SIGTERM_RECEIVED));
process.on('SIGINT', () => gracefulShutdown(SERVER_MESSAGES.SIGINT_RECEIVED));

export default app;
