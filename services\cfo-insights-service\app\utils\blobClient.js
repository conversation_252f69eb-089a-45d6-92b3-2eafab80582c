// app/utils/blobClient.js
import { BlobServiceClient } from "@azure/storage-blob";
import dotenv from "dotenv";
dotenv.config();

let containerClient = null;

/**
 * Get or create the Azure Blob Storage container client.
 * Lazy-loaded to ensure environment variables are available.
 */
function getContainerClient() {
  if (!containerClient) {
    const connectionString = process.env.AZURE_BLOB_CONNECTION_STRING;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;

    if (!connectionString) {
      throw new Error("Missing AZURE_BLOB_CONNECTION_STRING in .env");
    }
    if (!containerName) {
      throw new Error("Missing AZURE_BLOB_CONTAINER_NAME in .env");
    }

    const blobService = BlobServiceClient.fromConnectionString(connectionString);
    containerClient = blobService.getContainerClient(containerName);
  }
  return containerClient;
}

/**
 * Convert a blob stream to a Buffer.
 */
async function streamToBuffer(stream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on("data", (d) => chunks.push(d));
    stream.on("end", () => resolve(Buffer.concat(chunks)));
    stream.on("error", reject);
  });
}

/**
 * Download a PDF from Azure Blob Storage as a buffer.
 */
export async function downloadPdfBlob(filename) {
  const client = getContainerClient();
  const blobClient = client.getBlobClient(filename);
  const response = await blobClient.download();
  return await streamToBuffer(response.readableStreamBody);
}

/**
 * List all PDF files in the container.
 */
export async function listBlobFiles({ suffix = ".pdf" } = {}) {
  const client = getContainerClient();
  const items = [];
  for await (const blob of client.listBlobsFlat()) {
    if (!suffix || blob.name.toLowerCase().endsWith(suffix)) {
      items.push({
        name: blob.name,
        size: blob.properties.contentLength ?? 0,
        lastModified: blob.properties.lastModified ?? null
      });
    }
  }

  // Sort newest first
  return items.sort(
    (a, b) => new Date(b.lastModified) - new Date(a.lastModified)
  );
}
