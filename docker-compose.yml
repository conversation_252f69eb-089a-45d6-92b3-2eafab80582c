version: "3.9"
 
services:
  nginx:
    image: nginx:latest
    container_name: cpa-nginx
    ports:
      - "8080:80"
    volumes:
      - ./gateway/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - frontend
      - gateway
      - user-service
      - auth-service
      - tenant-service
      - sikka-services
      - adp-service
      - quickbook-service
    networks:
      - cpa-network
 
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    container_name: cpa-gateway
    depends_on:
      - user-service
      - auth-service
      - frontend
    networks:
      - cpa-network
 
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: perfino-dashboard-user-service
    ports:
      - "3003:3003"
    volumes:
      - ./services/user-service/app:/app/app
      - ./services/user-service/config:/app/config
      - ./services/user-service/migrations:/app/migrations
      - ./services/user-service/index.js:/app/index.js
      - ./services/user-service/.env:/app/.env
      - user-service-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
    networks:
      - cpa-network
 
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: perfino-dashboard-auth-service
    ports:
      - "3001:3001"
    depends_on:
      - user-service
    volumes:
      - ./services/auth-service/app:/app/app
      - ./services/auth-service/config:/app/config
      - ./services/auth-service/migrations:/app/migrations
      - ./services/auth-service/server.js:/app/index.js
      - ./services/auth-service/.env:/app/.env
      - auth-service-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
      USER_SERVICE_URL: http://user-service:3003/api/users
    networks:
      - cpa-network
 
  tenant-service:
    build:
      context: ./services/tenant-service
      dockerfile: Dockerfile
    container_name: perfino-dashboard-tenant-service
    ports:
      - "3002:3002"
    volumes:
      - ./services/tenant-service/app:/app/app
      - ./services/tenant-service/config:/app/config
      - ./services/tenant-service/migrations:/app/migrations
      - ./services/tenant-service/index.js:/app/index.js
      - ./services/tenant-service/.env:/app/.env
      - tenant-service-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
      DIALECT: postgres # <-- optional env if needed
    networks:
      - cpa-network
 
  sikka-services:
    build:
      context: ./services/sikka-services
      dockerfile: Dockerfile
    container_name: perfino-dashboard-sikka-services
    ports:
      - "3004:3004"
    volumes:
      - ./services/sikka-services/app:/app/app
      - ./services/sikka-services/config:/app/config
      - ./services/sikka-services/migrations:/app/migrations
      - ./services/sikka-services/index.js:/app/index.js
      - ./services/sikka-services/.env:/app/.env
      - sikka-services-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
    networks:
      - cpa-network
 
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: perfino-dashboard-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - frontend-node-modules:/app/node_modules
    environment:
      NODE_ENV: ${NODE_ENV}
      NEXT_PUBLIC_API_URL: http://nginx/api/v1
      NEXT_PUBLIC_CFO_ENDPOINT: http://cfo-insights-service:3007
    depends_on:
      - user-service
    networks:
      - cpa-network
 
  adp-service:
    build:
      context: ./services/adp-service
      dockerfile: Dockerfile
    container_name: perfino-dashboard-adp-service
    ports:
      - "3006:3006"
    volumes:
      - ./services/adp-service/app:/app/app
      - ./services/adp-service/config:/app/config
      - ./services/adp-service/migrations:/app/migrations
      - ./services/adp-service/server.js:/app/index.js
      - ./services/adp-service/.env:/app/.env
      - ./shared:/shared
    environment:
      DB_HOST: host.docker.internal
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASS: parshwa@1234
      DB_NAME: perfino-dashboard
      PORT: 3006
    networks:
      - cpa-network
 
  quickbook-service:
    build:
      context: ./services/quickbook-service
      dockerfile: Dockerfile
    container_name: perfino-dashboard-quickbook-service
    ports:
      - "3005:3005"
    volumes:
      - ./services/quickbook-service/app:/app/app
      - ./services/quickbook-service/config:/app/config
      - ./services/quickbook-service/migrations:/app/migrations
      - ./services/quickbook-service/server.js:/app/index.js
      - ./services/quickbook-service/.env:/app/.env
      - quickbook-service-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      USE_LOCAL_DB: ${USE_LOCAL_DB}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
      LOCAL_DB_HOST: ${LOCAL_DB_HOST}
      LOCAL_DB_PORT: ${LOCAL_DB_PORT}
      LOCAL_DB_USER: ${LOCAL_DB_USER}
      LOCAL_DB_PASS: ${LOCAL_DB_PASS}
      LOCAL_DB_NAME: ${LOCAL_DB_NAME}
    networks:
      - cpa-network

  cfo-insights-service:
    build:
      context: ./services/cfo-insights-service
      dockerfile: dockerfile
    container_name: perfino-dashboard-cfo-insights-service
    ports:
      - "3007:3007"
    volumes:
      - ./services/cfo-insights-service/app:/app/app
      - ./services/cfo-insights-service/server.js:/app/server.js
      - ./services/cfo-insights-service/.env:/app/.env
      - cfo-insights-service-node-modules:/app/node_modules
      - ./shared:/shared
    environment:
      CFO_INSIGHTS_SERVICE_PORT: 3007
      NODE_ENV: ${NODE_ENV}
    networks:
      - cpa-network
 
volumes:
  postgres_data:
  user-service-node-modules:
  tenant-service-node-modules:
  sikka-services-node-modules:
  quickbook-service-node-modules:
  auth-service-node-modules:
  cfo-insights-service-node-modules:
  frontend-node-modules:
 
networks:
  cpa-network:
    driver: bridge