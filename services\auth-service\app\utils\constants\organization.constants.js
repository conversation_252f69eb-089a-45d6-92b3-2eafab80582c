// <PERSON><PERSON>NI<PERSON><PERSON>ION CONSTANTS - All organization-related messages and field names

export const OR<PERSON><PERSON><PERSON><PERSON>ION_MESSAGES = {
  // Success Messages
  ORGANI<PERSON>ATION_CREATED_SUCCESSFULLY: 'Organization created successfully',
  ORGANIZATION_UPDATED_SUCCESSFULLY: 'Organization updated successfully',
  ORGANIZATION_DELETED_SUCCESSFULLY: 'Organization deleted successfully',
  ORGANIZATION_FOUND: 'Organization found',
  ORGANIZATIONS_RETRIEVED: 'Organizations retrieved successfully',
  
  // Error Messages
  OR<PERSON>NIZATION_CREATION_FAILED: 'Failed to create organization',
  ORGANIZATION_UPDATE_FAILED: 'Failed to update organization',
  ORGANIZATION_DELETE_FAILED: 'Failed to delete organization',
  ORGANIZ<PERSON>ION_NOT_FOUND: 'Organization not found',
  ORGANIZATION_ALREADY_EXISTS: 'Organization already exists',
  EMAIL_ALREADY_EXISTS: 'Organization with this email already exists',
  
  // Validation Messages
  MISSING_REQUIRED_FIELDS: 'Missing required fields',
  INVALID_SERVICES: 'Invalid services provided',
  VALIDATION_ERROR: 'Organization validation failed',
  INVALID_ORGANIZATION_DATA: 'Invalid organization data',
  
  // Service Messages
  INVALID_SERVICE_TYPE: 'Invalid service type',
  DUPLICATE_SERVICES: 'Duplicate services are not allowed',
  SERVICES_REQUIRED: 'At least one service must be specified',
  
  // General Messages
  UNAUTHORIZED_ACCESS: 'Unauthorized access to organization',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions to perform this action',
  ORGANIZATION_INACTIVE: 'Organization is inactive',
  ORGANIZATION_DELETED: 'Organization has been deleted',
};

export const ORGANIZATION_FIELD_NAMES = {
  // Basic Fields
  ID: 'id',
  NAME: 'name',
  EMAIL: 'email',
  PHONE: 'phone',
  WEBSITE: 'website',
  DESCRIPTION: 'description',
  SERVICES: 'services',
  
  // Status Fields
  IS_DELETED: 'is_deleted',
  IS_ACTIVE: 'is_active',
  
  // Audit Fields
  CREATED_AT: 'created_at',
  CREATED_BY: 'created_by',
  UPDATED_AT: 'updated_at',
  UPDATED_BY: 'updated_by',
  
  // Query Fields
  SEARCH: 'search',
  SERVICE_FILTER: 'service_filter',
  STATUS_FILTER: 'status_filter',
};

export const ORGANIZATION_SERVICES = {
  FINANCIAL: 'financial',
  OPERATIONAL: 'operational',
  PMS: 'pms',
};

export const ORGANIZATION_SERVICES_ARRAY = [
  ORGANIZATION_SERVICES.FINANCIAL,
  ORGANIZATION_SERVICES.OPERATIONAL,
  ORGANIZATION_SERVICES.PMS,
];

export const ORGANIZATION_VALIDATION_MESSAGES = {
  // Name Validation
  NAME_REQUIRED: 'Organization name is required',
  NAME_TOO_SHORT: 'Organization name must be at least 2 characters',
  NAME_TOO_LONG: 'Organization name must be less than 255 characters',
  
  // Email Validation
  EMAIL_REQUIRED: 'Organization email is required',
  EMAIL_INVALID: 'Invalid email format',
  EMAIL_TOO_LONG: 'Email must be less than 255 characters',
  EMAIL_ALREADY_EXISTS: 'Organization with this email already exists',
  
  // Phone Validation
  PHONE_INVALID: 'Invalid phone number format',
  PHONE_TOO_LONG: 'Phone number must be less than 20 characters',
  
  // Website Validation
  WEBSITE_INVALID: 'Invalid website URL format',
  WEBSITE_TOO_LONG: 'Website URL must be less than 500 characters',
  WEBSITE_PROTOCOL_REQUIRED: 'Website URL must include http or https protocol',
  
  // Description Validation
  DESCRIPTION_TOO_LONG: 'Description must be less than 1000 characters',
  
  // Services Validation
  SERVICES_REQUIRED: 'Services are required',
  SERVICES_MUST_BE_ARRAY: 'Services must be an array',
  SERVICES_EMPTY_ARRAY: 'At least one service must be specified',
  SERVICES_INVALID_TYPE: 'Invalid service type',
  SERVICES_DUPLICATE: 'Duplicate services are not allowed',
  SERVICES_INVALID_VALUES: `Services must be one of: ${ORGANIZATION_SERVICES_ARRAY.join(', ')}`,
};

export const ORGANIZATION_VALIDATION_RULES = {
  // Name Rules
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 255,
  
  // Email Rules
  EMAIL_MAX_LENGTH: 255,
  
  // Phone Rules
  PHONE_MAX_LENGTH: 20,
  
  // Website Rules
  WEBSITE_MAX_LENGTH: 500,
  
  // Description Rules
  DESCRIPTION_MAX_LENGTH: 1000,
  
  // Services Rules
  SERVICES_MIN_COUNT: 1,
  SERVICES_MAX_COUNT: 3, // Since we only have 3 service types
};

export const ORGANIZATION_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DELETED: 'deleted',
  PENDING: 'pending',
};

export const ORGANIZATION_SORT_FIELDS = {
  NAME: 'name',
  EMAIL: 'email',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at',
};

export const ORGANIZATION_SORT_ORDERS = {
  ASC: 'ASC',
  DESC: 'DESC',
};

// Default export
export default {
  ORGANIZATION_MESSAGES,
  ORGANIZATION_FIELD_NAMES,
  ORGANIZATION_SERVICES,
  ORGANIZATION_SERVICES_ARRAY,
  ORGANIZATION_VALIDATION_MESSAGES,
  ORGANIZATION_VALIDATION_RULES,
  ORGANIZATION_STATUS,
  ORGANIZATION_SORT_FIELDS,
  ORGANIZATION_SORT_ORDERS,
};
