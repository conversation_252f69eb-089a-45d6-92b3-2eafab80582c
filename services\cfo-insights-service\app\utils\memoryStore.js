// app/utils/memoryStore.js

/**
 * A tiny store for:
 * 1) Cached document text per filename (in-memory only)
 * 2) Chat sessions: { sessionId: { filename, history: [...] } } (in-memory only)
 */

const docTextCache = new Map(); // filename -> text (not persisted)
const sessions = new Map();     // sessionId -> { filename, history }

export function getCachedDocText(filename) {
  return docTextCache.get(filename) || null;
}
export function setCachedDocText(filename, text) {
  docTextCache.set(filename, text);
}

export function createSession({ sessionId, filename }) {
  sessions.set(sessionId, { filename, history: [] });
  return sessions.get(sessionId);
}
export function getSession(sessionId) {
  return sessions.get(sessionId) || null;
}
export function appendHistory(sessionId, role, content) {
  const s = sessions.get(sessionId);
  if (!s) return;
  s.history.push({ role, content });
  // Keep last ~12 messages to stay small
  if (s.history.length > 12) s.history.splice(0, s.history.length - 12);
}

export function deleteSession(sessionId) {
  return sessions.delete(sessionId);
}
