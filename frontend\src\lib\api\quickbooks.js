import { apiService } from "@/lib/axiosConfig";

// QuickBooks API endpoints
const QUICKBOOKS_ENDPOINTS = {
  ACCOUNTS: "/api/quickbooks/accounts",
  SYNC: "/api/quickbooks/sync",
  UPDATE_STATUS: "/api/quickbooks/accounts/status",
};

/**
 * Fetch QuickBooks accounts for an organization
 */
export const fetchQuickbooksAccounts = async (params = {}) => {
  try {
    const response = await apiService.get(QUICKBOOKS_ENDPOINTS.ACCOUNTS, {
      params,
    });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message || "Failed to fetch QuickBooks accounts",
    };
  }
};

/**
 * Update QuickBooks account status
 */
export const updateQuickbooksAccountStatus = async (data) => {
  try {
    const response = await apiService.put(
      QUICKBOOKS_ENDPOINTS.UPDATE_STATUS,
      data
    );
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message ||
        "Failed to update QuickBooks account status",
    };
  }
};

/**
 * Sync QuickBooks account
 */
export const syncQuickbooksAccount = async (data) => {
  try {
    const response = await apiService.post(QUICKBOOKS_ENDPOINTS.SYNC, data);
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message || "Failed to sync QuickBooks account",
    };
  }
};

/**
 * Create new QuickBooks connection
 */
export const createQuickbooksConnection = async (data) => {
  try {
    const response = await apiService.post(QUICKBOOKS_ENDPOINTS.ACCOUNTS, data);
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message ||
        "Failed to create QuickBooks connection",
    };
  }
};

/**
 * Delete QuickBooks account
 */
export const deleteQuickbooksAccount = async (id) => {
  try {
    const response = await apiService.delete(
      `${QUICKBOOKS_ENDPOINTS.ACCOUNTS}/${id}`
    );
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message || "Failed to delete QuickBooks account",
    };
  }
};

/**
 * Get QuickBooks OAuth URL
 */
export const getQuickbooksOAuthUrl = async (organizationId) => {
  try {
    const response = await apiService.get("/api/quickbooks/oauth-url", {
      params: { organization_id: organizationId },
    });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.response?.data?.message || "Failed to get QuickBooks OAuth URL",
    };
  }
};

const quickbooksApi = {
  fetchQuickbooksAccounts,
  updateQuickbooksAccountStatus,
  syncQuickbooksAccount,
  createQuickbooksConnection,
  deleteQuickbooksAccount,
  getQuickbooksOAuthUrl,
};

export default quickbooksApi;
