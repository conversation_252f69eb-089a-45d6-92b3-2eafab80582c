import React from "react";
import StatsCard from "./StatsCard";

export default function StatsGrid({
  stats = [],
  className = "",
  columns = { sm: 1, md: 2, lg: 4 },
  loading = false,
}) {
  if (!stats || stats.length === 0) {
    return null;
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {stats.map((stat, index) => (
        <StatsCard
          key={stat.id || index}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          bgColor={stat.bgColor}
          iconColor={stat.iconColor}
          onClick={stat.onClick}
          loading={loading}
        />
      ))}
    </div>
  );
}
