import { Client } from "pg";
import { Sequelize } from "sequelize";
import { POSTGRES_CONFIG } from "../../config/db.config.js";

let client = null;

// -------------------- pg Client (raw queries) --------------------
/**
 * Connect to PostgreSQL and return the pg client instance
 */
export const connectDb = async () => {
  if (!client) {
    client = new Client(POSTGRES_CONFIG);
    await client.connect();
    console.log("✅ Connected to PostgreSQL (pg)");
  }
  return client;
};

/**
 * Disconnect the pg client
 */
export const disconnectDb = async () => {
  if (client) {
    await client.end();
    console.log("✅ Disconnected from PostgreSQL (pg)");
    client = null;
  }
};

/**
 * Higher-order function to execute queries with automatic connect/disconnect
 * @param {Function} fn - async function that receives the pg client as argument
 */
export const withDb = async (fn) => {
  try {
    const dbClient = await connectDb();
    return await fn(dbClient);
  } finally {
    await disconnectDb();
  }
};

// -------------------- Sequelize instance --------------------
const sequelizeConfig = {
  host: POSTGRES_CONFIG.host,
  port: POSTGRES_CONFIG.port,
  dialect: "postgres",
  logging: process.env.NODE_ENV === "development" ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};

// Add SSL configuration if present in POSTGRES_CONFIG
if (POSTGRES_CONFIG.ssl) {
  sequelizeConfig.dialectOptions = {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  };
  console.log("🔒 SSL enabled for Sequelize connection");
} else {
  console.log("🔓 SSL disabled for Sequelize connection");
}

export const sequelize = new Sequelize(
  POSTGRES_CONFIG.database,
  POSTGRES_CONFIG.user,
  POSTGRES_CONFIG.password,
  sequelizeConfig
);

// Test Sequelize connection
sequelize
  .authenticate()
  .then(() => console.log("✅ Connected to PostgreSQL (Sequelize)"))
  .catch((err) => console.error("❌ Sequelize connection error:", err));