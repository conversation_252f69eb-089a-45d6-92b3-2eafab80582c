{"name": "perfino-dashboard-cfo-insights-service", "version": "1.0.0", "type": "module", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "pinecone:create-index": "node scripts/createPineconeIndex.js", "check:azure": "node scripts/checkAzure.js", "check:pinecone": "node scripts/checkPinecone.js", "check:blob": "node scripts/checkBlob.js"}, "description": "CPA Dashboard CFO Insights Service - Document analysis and insights microservice", "author": "Mobio Solutions", "license": "ISC", "keywords": ["cpa", "dashboard", "cfo", "insights", "document", "analysis", "ai", "microservice", "nodejs", "express", "azure", "pinecone"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@azure/storage-blob": "^12.16.0", "@langchain/textsplitters": "^0.0.3", "@pinecone-database/pinecone": "^2.2.2", "axios": "^1.12.2", "cors": "^2.8.5", "detect-port": "^2.1.0", "dotenv": "^16.4.5", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.0.0", "joi": "^17.13.3", "langchain": "^0.2.17", "morgan": "^1.10.0", "nodemon": "^3.1.0", "openai": "^4.57.0", "pdf-parse": "^1.1.1", "pdf2json": "^3.0.5", "pdfjs-dist": "^4.6.82", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"eslint": "^9.34.0", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.3.0", "jest": "^29.7.0", "supertest": "^7.0.0"}}