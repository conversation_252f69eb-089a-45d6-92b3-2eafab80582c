import { embedText, chatWithContext } from "../app/utils/azureOpenAI.js";

async function run() {
  try {
    const vec = await embedText("healthcheck");
    console.log("Embeddings OK. Dimension:", vec.length);
  } catch (e) {
    console.error("Embeddings FAILED:", e?.message || e);
    process.exit(1);
  }

  try {
    const answer = await chatWithContext("ping", "The correct reply is: pong.");
    console.log("Chat OK. Sample answer:", JSON.stringify(answer));
  } catch (e) {
    console.error("Chat FAILED:", e?.message || e);
    process.exit(1);
  }
}

run();
