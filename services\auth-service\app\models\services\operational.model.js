import { DataTypes } from 'sequelize';

/**
 * Operational Service Models
 * These models will be created in organization-specific schemas
 */

/**
 * Projects Model - Manages operational projects
 */
export const ProjectsModel = (sequelize, schemaName) => {
  const Projects = sequelize.define(
    'projects',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      project_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: true,
      },
      project_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('planning', 'active', 'on_hold', 'completed', 'cancelled'),
        defaultValue: 'planning',
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      budget: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      actual_cost: {
        type: DataTypes.DECIMAL(15, 2),
        defaultValue: 0.00,
      },
      project_manager_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      client_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      priority: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
        defaultValue: 'medium',
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'projects',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_projects_code', fields: ['project_code'] },
        { name: 'idx_projects_status', fields: ['status'] },
        { name: 'idx_projects_manager', fields: ['project_manager_id'] },
        { name: 'idx_projects_priority', fields: ['priority'] },
      ],
    }
  );

  return Projects;
};

/**
 * Tasks Model - Manages project tasks and activities
 */
export const TasksModel = (sequelize, schemaName) => {
  const Tasks = sequelize.define(
    'tasks',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      project_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
      },
      task_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'blocked', 'cancelled'),
        defaultValue: 'not_started',
      },
      assigned_to: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      estimated_hours: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
      },
      actual_hours: {
        type: DataTypes.DECIMAL(8, 2),
        defaultValue: 0.00,
      },
      due_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      completed_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      priority: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
        defaultValue: 'medium',
      },
      parent_task_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'tasks',
          key: 'id',
        },
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'tasks',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_tasks_project', fields: ['project_id'] },
        { name: 'idx_tasks_status', fields: ['status'] },
        { name: 'idx_tasks_assigned', fields: ['assigned_to'] },
        { name: 'idx_tasks_due_date', fields: ['due_date'] },
        { name: 'idx_tasks_parent', fields: ['parent_task_id'] },
      ],
    }
  );

  return Tasks;
};

/**
 * Resources Model - Manages operational resources
 */
export const ResourcesModel = (sequelize, schemaName) => {
  const Resources = sequelize.define(
    'resources',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      resource_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      resource_type: {
        type: DataTypes.ENUM('equipment', 'material', 'software', 'facility', 'vehicle', 'other'),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('available', 'in_use', 'maintenance', 'retired'),
        defaultValue: 'available',
      },
      location: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      cost_per_hour: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      cost_per_unit: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      quantity_available: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
      },
      quantity_in_use: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      supplier_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      purchase_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      warranty_expiry: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'resources',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_resources_type', fields: ['resource_type'] },
        { name: 'idx_resources_status', fields: ['status'] },
        { name: 'idx_resources_location', fields: ['location'] },
        { name: 'idx_resources_supplier', fields: ['supplier_name'] },
      ],
    }
  );

  return Resources;
};

// Export all operational models
export const OperationalModels = {
  ProjectsModel,
  TasksModel,
  ResourcesModel,
};

export default OperationalModels;
