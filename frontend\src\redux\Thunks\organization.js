import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.AUTH);
// Fetch all organizations
export const fetchOrganizations = createAsyncThunk(
  "organizations/fetchAll",
  async (params, { rejectWithValue }) => {
    try {
      return await axios
        .get("/organization", { params })
        .then((res) => res.data.data);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Create organization
export const createOrganization = createAsyncThunk(
  "organizations/create",
  async (data, { rejectWithValue }) => {
    try {
      // POST to the specified endpoint
      return await axios.post("/organization", data);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Update organization
export const updateOrganization = createAsyncThunk(
  "organizations/update",
  async ({ id, data }, { rejectWithValue }) => {
    try {
      return await axios.put(`/auth/organizations/${id}`, data);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Delete organization
export const deleteOrganization = createAsyncThunk(
  "organizations/delete",
  async (id, { rejectWithValue }) => {
    try {
      return await axios.delete(`/auth/organizations/${id}`);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get organization by ID
export const getOrganizationById = createAsyncThunk(
  "organizations/getById",
  async (id, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/organization/${id}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);
