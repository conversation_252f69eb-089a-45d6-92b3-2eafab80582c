"use strict";

module.exports = {
  up: async function (queryInterface, Sequelize) {
    // Check if the column already exists before adding it
    const tableDescription = await queryInterface.describeTable("app_user", {
      schema: "Authentication",
    });

    if (!tableDescription.updated_at) {
      return queryInterface.addColumn(
        "app_user",
        "updated_at",
        {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: true,
        },
        {
          schema: "Authentication",
        }
      );
    }
  },

  down: async function (queryInterface, Sequelize) {
    const tableDescription = await queryInterface.describeTable("app_user", {
      schema: "Authentication",
    });

    if (tableDescription.updated_at) {
      return queryInterface.removeColumn("app_user", "updated_at", {
        schema: "Authentication",
      });
    }
  },
};
