import dotenv from "dotenv";
dotenv.config();

// Helper function to get password and strip quotes
const getPassword = (password) => {
  if (!password) return '';
  return String(password).replace(/^["']|["']$/g, '');
};

// Get database environment variables
const dbHost = process.env.DB_HOST ;
const dbName = process.env.DB_NAME ;
const dbUser = process.env.DB_USER ;
const dbPass = process.env.DB_PASS ;
const dbSsl = process.env.DB_SSL;
const dbPort = process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432;

// Process password (strip quotes)
const password = getPassword(dbPass);

// Determine SSL configuration
// If DB_SSL is explicitly set, use it; otherwise auto-detect based on host
// Enable SSL for AWS RDS or any remote host (not localhost)
const isRemoteHost = dbHost && !dbHost.includes('localhost') && !dbHost.includes('127.0.0.1');
const dbSslEnabled = typeof dbSsl !== 'undefined'
  ? (dbSsl.toLowerCase() === 'true')
  : isRemoteHost;

// PostgreSQL configuration
export const POSTGRES_CONFIG = {
  user: dbUser,
  host: dbHost,
  database: dbName,
  password: password,
  port: dbPort,
  // Add SSL configuration if enabled
  ...(dbSslEnabled ? {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    }
  } : {}),
};

// Debug logging
if (process.env.NODE_ENV === 'development') {
  console.log('📊 Database Configuration:', {
    host: dbHost,
    port: dbPort,
    database: dbName,
    user: dbUser,
    ssl: dbSslEnabled,
    isRemote: isRemoteHost,
    hasSslConfig: !!POSTGRES_CONFIG.ssl,
  });
}