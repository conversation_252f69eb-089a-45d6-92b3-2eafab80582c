// Packages
import path from "path";
import fs from "fs/promises";

// Services
import { adpService } from "../services/adp.service.js";

// Utils
import {
  successResponse,
  errorResponse,
} from "../../../../shared/utils/response.util.js";
import * as status from "../../../../shared/utils/status_code.util.js";
import * as constants from "../../../../shared/utils/constants.util.js";
import { createLogger } from "../utils/logger.util.js";

const logger = createLogger("ADP_CONTROLLER");

/**
 * Sync Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
export const syncData = async (req, res) => {
  try {
    logger.info("Sync data request received", {
      fileName: req.file?.originalname,
      schemaName: req.body?.schemaName,
      orgId: req.body?.orgId,
    });

    if (!req.file) {
      logger.warn("Sync data request failed: No file uploaded");
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CONSTANT_MESSAGES.NO_FILE_UPLOADED));
    }
    const { schemaName, orgId } = req.body;

    if (!schemaName) {
      logger.warn("Sync data request failed: Schema name required");
      return res.status(status.STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: constants.CONSTANT_MESSAGES.SCHEMA_NAME_REQUIRED,
      });
    }

    const uploadsDir = path.join(process.cwd(), "uploads");

    await fs.mkdir(uploadsDir, { recursive: true });

    const filePath = path.join(
      uploadsDir,
      Date.now() + "-" + req.file.originalname
    );

    logger.info("Writing uploaded file", { filePath });
    await fs.writeFile(filePath, req.file.buffer);

    logger.info("Starting ADP data sync", { filePath, schemaName, orgId });
    const result = await adpService.syncData(filePath, schemaName, orgId);

    logger.info("ADP data sync completed successfully", {
      insertedRecords: result.insertedRecords,
    });

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CONSTANT_MESSAGES.PAYROLL_DATA_SYNCED, result)
      );
  } catch (error) {
    logger.error("ADP data sync failed", {
      error: error.message,
      stack: error.stack,
    });
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};
