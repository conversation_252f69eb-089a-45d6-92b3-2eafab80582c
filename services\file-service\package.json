{"name": "file-storage-service", "version": "1.0.0", "description": "Microservice for managing files in Azure Blob Storage", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "migrate:status": "sequelize-cli db:migrate:status"}, "keywords": ["azure", "blob-storage", "file-management", "microservice", "express"], "author": "", "license": "ISC", "dependencies": {"@azure/storage-blob": "^12.25.0", "axios": "^1.7.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.19.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "sequelize": "^6.37.7", "winston": "^3.13.0"}, "devDependencies": {"nodemon": "^3.1.0", "sequelize-cli": "^6.6.3"}}