# Schema-based KPI Usage Examples

This document demonstrates how to use the schema-based multi-tenant KPI functionality.

## Overview

The KPI controllers now support storing data in organization-specific PostgreSQL schemas. Each organization gets its own schema (e.g., `acme_corp`, `medical_group_123`) where their KPI data is isolated.

## API Usage

### Account Receivables

**Without Schema (Legacy)**:
```json
POST /api/sikka/account_receivables
{
  "office_id": "12345"
}
```

**With Schema (Multi-tenant)**:
```json
POST /api/sikka/account_receivables
{
  "office_id": "12345",
  "organization_name": "Acme Corp"
}
```

### Treatment Analysis

**With Schema**:
```json
POST /api/sikka/treatment_analysis
{
  "office_id": "12345",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "organization_name": "Medical Group Inc"
}
```

### Direct Restorations

**With Schema**:
```json
POST /api/sikka/direct_restorations
{
  "office_id": "12345",
  "start_date": "2024-01-01", 
  "end_date": "2024-01-31",
  "organization_name": "Dental Practice LLC"
}
```

## Schema Naming Convention

Organization names are automatically converted to valid PostgreSQL schema names:

- `"Acme Corp"` → `acme_corp`
- `"Medical Group Inc."` → `medical_group_inc`
- `"123 Dental Practice"` → `org_123_dental_practice`
- `"ABC & Co. (2024)"` → `abc_co_2024`

## Database Structure

When an organization makes their first API call:

1. **Schema Creation**: A schema named after the organization is created (e.g., `acme_corp`)
2. **Table Creation**: KPI tables are created in the organization's schema
3. **Data Storage**: All subsequent data for that organization is stored in their schema

Example database structure:
```
Database: cpa_dashboard
├── public (default schema)
├── acme_corp (organization schema)
│   ├── sikka_accounts_receivable
│   ├── sikka_treatment_plan_analysis
│   └── sikka_direct_restoration
└── medical_group_inc (another organization schema)
    ├── sikka_accounts_receivable
    ├── sikka_treatment_plan_analysis
    └── sikka_no_show_appointments
```

## Backward Compatibility

The API maintains backward compatibility:

- **Without `organization_name`**: Data is stored in the default `public` schema (legacy behavior)
- **With `organization_name`**: Data is stored in the organization-specific schema

## Error Handling

The system handles various error scenarios:

1. **Invalid Organization Names**: Automatically sanitized to valid schema names
2. **Schema Creation Failures**: Proper error messages returned
3. **Table Creation Issues**: Automatic retry and error logging

## Benefits

1. **Data Isolation**: Each organization's data is completely isolated
2. **Scalability**: Better performance with smaller, organization-specific tables
3. **Security**: Organizations cannot access each other's data
4. **Compliance**: Easier to meet data residency and privacy requirements
5. **Maintenance**: Easier to backup, restore, or migrate specific organization data

## Implementation Notes

- Schema names follow PostgreSQL naming conventions (lowercase, underscores, max 63 chars)
- Reserved schema names (`public`, `information_schema`, etc.) are automatically avoided
- Tables are created automatically when first accessed
- Search path is managed automatically for each request
