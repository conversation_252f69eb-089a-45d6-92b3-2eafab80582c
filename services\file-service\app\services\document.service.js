// app/services/document.service.js
import { Document } from "../models/index.js";
import logger from "../../config/logger.config.js";
import {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
  DOCUMENT_VALIDATION_RULES,
  DOCUMENT_SERVICE_LIST,
} from "../utils/constants/document.constants.js";
import {
  STATUS_CODE_OK,
  STATUS_CODE_CREATED,
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "../utils/status_code.utils.js";

// HELPER FUNCTIONS
const createServiceResponse = (
  success,
  statusCode,
  message,
  data = null,
  error = null
) => ({
  success,
  statusCode,
  message,
  data,
  error,
});

const validateService = (service) => {
  return DOCUMENT_SERVICE_LIST.includes(service);
};

const validateMonth = (month) => {
  return month >= DOCUMENT_VALIDATION_RULES.MONTH_MIN && month <= DOCUMENT_VALIDATION_RULES.MONTH_MAX;
};

const validateYear = (year) => {
  return year >= DOCUMENT_VALIDATION_RULES.YEAR_MIN && year <= DOCUMENT_VALIDATION_RULES.YEAR_MAX;
};

// SERVICE FUNCTIONS

/**
 * Store document metadata
 */
export const storeDocumentService = async (documentData) => {
  logger.info(DOCUMENT_LOG_MESSAGES.SERVICE_START_STORAGE);
  
  try {
    const {
      organization_id,
      blob_storage_path,
      service,
      month,
      year,
      file_name,
      file_size,
      mime_type,
      metadata,
    } = documentData;

    // Validate required fields
    if (!organization_id || !blob_storage_path || !service || !month || !year) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_MISSING_FIELDS);
      return createServiceResponse(
        false,
        STATUS_CODE_BAD_REQUEST,
        DOCUMENT_MESSAGES.MISSING_REQUIRED_FIELDS,
        null,
        DOCUMENT_MESSAGES.ORGANIZATION_ID_REQUIRED_ERROR
      );
    }

    // Validate service
    if (!validateService(service)) {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_SERVICE}: ${service}`);
      return createServiceResponse(
        false,
        STATUS_CODE_BAD_REQUEST,
        DOCUMENT_MESSAGES.INVALID_SERVICE_TYPE,
        null,
        DOCUMENT_MESSAGES.INVALID_SERVICE_ERROR
      );
    }

    // Validate month
    if (!validateMonth(month)) {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_MONTH}: ${month}`);
      return createServiceResponse(
        false,
        STATUS_CODE_BAD_REQUEST,
        DOCUMENT_MESSAGES.INVALID_MONTH,
        null,
        DOCUMENT_MESSAGES.INVALID_MONTH_ERROR
      );
    }

    // Validate year
    if (!validateYear(year)) {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_YEAR}: ${year}`);
      return createServiceResponse(
        false,
        STATUS_CODE_BAD_REQUEST,
        DOCUMENT_MESSAGES.INVALID_YEAR,
        null,
        DOCUMENT_MESSAGES.INVALID_YEAR_ERROR
      );
    }

    // Check if document already exists
    const existingDocument = await Document.findOne({
      where: {
        organization_id,
        service,
        month,
        year,
        is_deleted: false,
      },
    });

    if (existingDocument) {
      logger.info(DOCUMENT_LOG_MESSAGES.SERVICE_DOCUMENT_EXISTS);
      
      // Update existing document
      Object.assign(existingDocument, {
        blob_storage_path,
        ...(file_name && { file_name }),
        ...(file_size && { file_size }),
        ...(mime_type && { mime_type }),
        ...(metadata && { metadata }),
        is_active: true,
      });
      
      await existingDocument.save();
      
      logger.info(`${DOCUMENT_LOG_MESSAGES.SERVICE_DOCUMENT_UPDATED}: ${existingDocument.id}`);
      
      return createServiceResponse(
        true,
        STATUS_CODE_OK,
        DOCUMENT_MESSAGES.UPDATED_SUCCESSFULLY,
        existingDocument
      );
    }

    // Create new document
    const newDocument = await Document.create({
      organization_id,
      blob_storage_path,
      service,
      month,
      year,
      file_name,
      file_size,
      mime_type,
      metadata: metadata || {},
    });

    logger.info(`${DOCUMENT_LOG_MESSAGES.SERVICE_DOCUMENT_STORED}: ${newDocument.id}`);

    return createServiceResponse(
      true,
      STATUS_CODE_CREATED,
      DOCUMENT_MESSAGES.STORED_SUCCESSFULLY,
      newDocument
    );
  } catch (error) {
    logger.error(`${DOCUMENT_LOG_MESSAGES.SERVICE_STORAGE_ERROR}: ${error.message}`, { stack: error.stack });
    return createServiceResponse(
      false,
      STATUS_CODE_INTERNAL_SERVER_ERROR,
      DOCUMENT_MESSAGES.STORAGE_FAILED,
      null,
      error.message
    );
  }
};

/**
 * Get documents by organization and filters
 */
export const getDocumentsService = async (organization_id, filters = {}) => {
  logger.info(DOCUMENT_LOG_MESSAGES.SERVICE_GETTING_DOCUMENTS);
  
  try {
    const whereClause = {
      organization_id,
      is_deleted: false,
    };

    if (filters.service) {
      whereClause.service = filters.service;
    }
    if (filters.month) {
      whereClause.month = filters.month;
    }
    if (filters.year) {
      whereClause.year = filters.year;
    }

    const documents = await Document.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
    });

    return createServiceResponse(
      true,
      STATUS_CODE_OK,
      DOCUMENT_MESSAGES.RETRIEVED_SUCCESSFULLY,
      documents
    );
  } catch (error) {
    logger.error(`${DOCUMENT_LOG_MESSAGES.SERVICE_RETRIEVAL_ERROR}: ${error.message}`, { stack: error.stack });
    return createServiceResponse(
      false,
      STATUS_CODE_INTERNAL_SERVER_ERROR,
      DOCUMENT_MESSAGES.RETRIEVAL_FAILED,
      null,
      error.message
    );
  }
};

