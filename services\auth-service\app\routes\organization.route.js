import express from "express";
import {
  addOrganization,
  getOrganization,
  updateOrganization,
  deleteOrganization,
  getAllOrganizations,
  getOrganizationByOfficeId,
  getOrganizationByRealmId,
  updateSyncField,
  updateRealmId,
  checkRealmIdExists,
  checkRealmIdAssociation,
  updateQbConnection,
} from "../controllers/organization.controller.js";
import { verifyToken } from "../middleware/auth.middleware.js";
import { authLimiter } from "../middleware/ratelimit.middleware.js";
import { validate } from "../middleware/validation.middleware.js";
import { organizationValidationSchemas } from "../validators/organization.validator.js";

const router = express.Router();

// ORGANIZATION ROUTES

/**
 * Add a new organization
 * @route POST /add-org
 * @access Public (or Protected - depending on requirements)
 * @description Creates a new organization with the provided details
 */
router.post(
  "/",
  // authLimiter,
  // validate(organizationValidationSchemas.addOrganization),
  addOrganization
);

/**
 * Get all organizations
 * @route GET /
 * @access Protected
 * @description Retrieves all organizations (paginated)
 */
router.get("/", authLimiter, getAllOrganizations);

/**
 * Get organization by ID
 * @route GET /:id
 * @access Protected
 * @description Retrieves organization details by ID
 */
router.get("/:id", authLimiter, getOrganization);

/**
 * Get organization by office_id
 * @route GET /office/:office_id
 * @access Protected
 * @description Retrieves organization details by office_id
 */
router.get("/office/:office_id", authLimiter, getOrganizationByOfficeId);

/**
 * Get organization by realm_id
 * @route GET /realm/:realm_id
 * @access Protected
 * @description Retrieves organization details by realm_id
 */
router.get("/realm/:realm_id", authLimiter, getOrganizationByRealmId);

/**
 * Update organization (placeholder for future implementation)
 * @route PUT /:id
 * @access Protected
 * @description Updates organization details
 */
router.put(
  "/:id",
  // verifyToken,
  authLimiter,
  validate(organizationValidationSchemas.updateOrganization),
  updateOrganization
);

/**
 * Delete organization (placeholder for future implementation)
 * @route DELETE /:id
 * @access Protected
 * @description Soft deletes an organization
 */
router.delete("/:id", verifyToken, deleteOrganization);

/**
 * Update sync field for organization
 * @route PUT /:id/sync
 * @access Protected
 * @description Updates the last sync timestamp for a specific service type
 */
router.put("/sync/:id", updateSyncField);

/**
 * Update realm_id for organization
 * @route PUT /realm/:organization_id
 * @access Protected
 * @description Updates the realm_id field for an organization
 */
router.put("/realm/:organization_id", updateRealmId);

/**
 * Check if realm_id exists for organization
 * @route GET /check-realm/:org_id
 * @access Protected
 * @description Checks if a realm_id exists for the given organization ID
 */
router.get("/check-realm/:org_id", checkRealmIdExists);

/**
 * Check if realm_id is associated with any organization
 * @route GET /check-realm-association/:realm_id
 * @access Protected
 * @description Checks if a realm_id is associated with any organization
 */
router.get(
  "/check-realm-association/:realm_id",
  authLimiter,
  checkRealmIdAssociation
);

/**
 * Update QB connection status for organization
 * @route PUT /:org_id/qb-connect
 * @access Protected
 * @description Updates the is_qb_connected field to true for the given organization
 */
router.put("/:org_id/qb-connect", authLimiter, updateQbConnection);

export default router;
