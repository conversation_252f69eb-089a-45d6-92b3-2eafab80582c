import { validationRules } from "@/utils/methods/validation";
import { ORGANIZATION_FIELD_CONSTANTS } from "@/utils/constants/organization";

export const organizationsData = [
  {
    id: 1,
    name: "Finance Department",
    type: "Department",
    parent: "Acme Corporation",
    members: 15,
    location: "New York",
    status: "Active",
    createdAt: "2024-01-15",
    manager: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0100",
    description: "Handles all financial operations and accounting.",
  },
  {
    id: 2,
    name: "IT Department",
    type: "Department",
    parent: "TechStart Inc",
    members: 8,
    location: "San Francisco",
    status: "Active",
    createdAt: "2024-02-20",
    manager: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0200",
    description: "Manages technology infrastructure and development.",
  },
  {
    id: 3,
    name: "Marketing Team",
    type: "Team",
    parent: "Global Solutions",
    members: 5,
    location: "Remote",
    status: "Inactive",
    createdAt: "2024-01-10",
    manager: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0300",
    description: "Responsible for marketing campaigns and brand management.",
  },
  {
    id: 4,
    name: "HR Division",
    type: "Division",
    parent: "Acme Corporation",
    members: 12,
    location: "New York",
    status: "Active",
    createdAt: "2024-01-20",
    manager: "Lisa Wilson",
    email: "<EMAIL>",
    phone: "******-0400",
    description: "Human resources and employee management.",
  },
  {
    id: 5,
    name: "Sales Branch",
    type: "Branch",
    parent: "TechStart Inc",
    members: 20,
    location: "Los Angeles",
    status: "Active",
    createdAt: "2024-02-01",
    manager: "Robert Brown",
    email: "<EMAIL>",
    phone: "******-0500",
    description: "Sales operations and customer relations.",
  },
];

export const organizationFields = [
  {
    name: "name",
    label: ORGANIZATION_FIELD_CONSTANTS.NAME.LABEL,
    type: "text",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.NAME.PLACEHOLDER,
    validation: { required: ORGANIZATION_FIELD_CONSTANTS.NAME.VALIDATION },
  },
  {
    name: "email",
    label: ORGANIZATION_FIELD_CONSTANTS.EMAIL.LABEL,
    type: "email",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.EMAIL.PLACEHOLDER,
    validation: {
      required: "Email is required",
      pattern: {
        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Please enter a valid email address",
      },
    },
  },
  {
    name: "phone",
    label: ORGANIZATION_FIELD_CONSTANTS.PHONE.LABEL,
    type: "tel",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.PHONE.PLACEHOLDER,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.PHONE.VALIDATION,
      pattern: {
        value: /^\+?[\d\s\-\(\)]{10,}$/,
        message: "Please enter a valid phone number",
      },
    },
  },
  {
    name: "website",
    label: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.LABEL,
    type: "url",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.PLACEHOLDER,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.VALIDATION,
      pattern: {
        value: /^(https?:\/\/)?[a-z0-9.-]+\.[a-z]{2,}(\/.*)?$/i,
        message: "Please enter a valid URL",
      },
    },
  },
  {
    name: "description",
    label: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.LABEL,
    type: "textarea",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.PLACEHOLDER,
    colSpan: 2,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.VALIDATION,
    },
  },
  {
    name: "services",
    label: ORGANIZATION_FIELD_CONSTANTS.SERVICES.LABEL,
    type: "multiselect",
    options: [
      { label: "Financial", value: "financial" },
      { label: "Operational", value: "operational" },
      { label: "Payroll", value: "payroll" },
    ],
    validation: { required: ORGANIZATION_FIELD_CONSTANTS.SERVICES.VALIDATION },
  },
  {
    name: "office_id",
    type: "text",
    placeholder: "Enter Sikka ID",
    render: (field, form) => {
      const selectedServices = form.watch("services") || [];
      const errors = form.formState.errors;
      const fieldError = errors?.office_id;
      if (selectedServices.includes("operational")) {
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sikka ID
            </label>
            <input
              type="text"
              placeholder="Enter Sikka ID"
              {...field}
              className={`w-full h-10 px-3 py-2 text-sm bg-white border rounded-lg shadow-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 ${
                fieldError
                  ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                  : "border-gray-300"
              }`}
            />
            {fieldError && (
              <p className="text-xs text-red-500 mt-1">
                {fieldError.message || fieldError}
              </p>
            )}
          </div>
        );
      }
      return null;
    },
    validation: {
      validate: (value, allValues) => {
        // force required only if operational is selected
        const selected = allValues?.services || [];
        if (selected.includes("operational") && (!value || value.trim() === "")) {
          return "Sikka ID is required when Operational is selected.";
        }
        return true;
      },
    },
  },
];

export const initialValues = {
  name: "",
  email: "",
  phone: "",
  website: "",
  description: "",
  services: [],
  realm_id: "",
  office_id: "",
};

export const stats = [
  {
    title: "Total Organizations",
    value: organizationsData.length.toString(),
    icon: "building",
    bgColor: "bg-blue-100",
    iconColor: "text-blue-600",
  },
  {
    title: "Total Members",
    value: organizationsData
      .reduce((sum, org) => sum + org.members, 0)
      .toString(),
    icon: "users",
    bgColor: "bg-green-100",
    iconColor: "text-green-600",
  },
  {
    title: "Locations",
    value: new Set(
      organizationsData.map((org) => org.location)
    ).size.toString(),
    icon: "map-pin",
    bgColor: "bg-purple-100",
    iconColor: "text-purple-600",
  },
  {
    title: "New client onboarded TM",
    value: "2",
    icon: "calendar",
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-600",
  },
];

export const columns = [
  {
    key: "name",
    title: "Organization Name",
    width: "20%",
  },
  {
    key: "type",
    title: "Type",
    width: "15%",
    render: (value) => renderStatusBadge(value),
  },
  {
    key: "parent",
    title: "Parent",
    width: "15%",
  },
  {
    key: "members",
    title: "Members",
    width: "10%",
  },
  {
    key: "location",
    title: "Location",
    width: "15%",
  },
  {
    key: "status",
    title: "Status",
    width: "10%",
    render: (value) => renderStatusBadge(value),
  },
  {
    key: "createdAt",
    title: "Created",
    width: "10%",
  },
  {
    key: "actions",
    title: "Actions",
    width: "15%",
    sortable: false,
    render: (_, item) =>
      renderActionButtons(item, handleView, handleEdit, handleDelete),
  },
];
