import { createLogger } from "./logger.utils.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";
import sequelize from "../../config/postgres.config.js";

const logger = createLogger(LOGGER_NAMES.SCHEMA_UTILS);

/**
 * Convert organization name to snake_case for schema naming
 * @param {string} organizationName - The organization name
 * @returns {string} Snake case schema name
 */
export const convertToSnakeCase = (organizationName) => {
  if (!organizationName || typeof organizationName !== "string") {
    throw new Error("Organization name must be a non-empty string");
  }

  return (
    organizationName
      .toLowerCase()
      .trim()
      // Replace spaces and special characters with underscores
      .replace(/[^a-z0-9]/g, "_")
      // Remove multiple consecutive underscores
      .replace(/_+/g, "_")
      // Remove leading and trailing underscores
      .replace(/^_+|_+$/g, "")
      // Ensure it starts with a letter (PostgreSQL requirement)
      .replace(/^[0-9]/, "org_$&")
      // Limit length to 63 characters (PostgreSQL limit)
      .substring(0, 63)
  );
};

/**
 * Validate schema name according to PostgreSQL rules
 * @param {string} schemaName - The schema name to validate
 * @returns {boolean} True if valid, throws error if invalid
 */
export const validateSchemaName = (schemaName) => {
  if (!schemaName || typeof schemaName !== "string") {
    throw new Error("Schema name must be a non-empty string");
  }

  // PostgreSQL identifier rules
  const validPattern = /^[a-z][a-z0-9_]*$/;

  if (!validPattern.test(schemaName)) {
    throw new Error(
      "Schema name must start with a letter and contain only lowercase letters, numbers, and underscores"
    );
  }

  if (schemaName.length > 63) {
    throw new Error("Schema name must be 63 characters or less");
  }

  // Check for reserved words
  const reservedWords = [
    "public",
    "information_schema",
    "pg_catalog",
    "pg_toast",
    "pg_temp",
    "pg_toast_temp",
    "postgres",
    "template0",
    "template1",
  ];

  if (reservedWords.includes(schemaName.toLowerCase())) {
    throw new Error(
      `Schema name '${schemaName}' is reserved and cannot be used`
    );
  }

  return true;
};

/**
 * Create a new PostgreSQL schema
 * @param {string} schemaName - The name of the schema to create
 * @returns {Promise<boolean>} True if created successfully
 */
export const createSchema = async (schemaName) => {
  logger.info(`schemaUtils.createSchema - Creating schema: ${schemaName}`);

  try {
    // Validate schema name
    validateSchemaName(schemaName);

    // Check if schema already exists
    const schemaExists = await checkSchemaExists(schemaName);
    if (schemaExists) {
      logger.warn(
        `schemaUtils.createSchema - Schema already exists: ${schemaName}`
      );
      return true;
    }

    // Create the schema
    const query = `CREATE SCHEMA IF NOT EXISTS "${schemaName}"`;
    await sequelize.query(query);

    logger.info(
      `schemaUtils.createSchema - Schema created successfully: ${schemaName}`
    );
    return true;
  } catch (error) {
    logger.error(
      `schemaUtils.createSchema - Error creating schema ${schemaName}: ${error.message}`
    );
    throw error;
  }
};

/**
 * Check if a schema exists
 * @param {string} schemaName - The name of the schema to check
 * @returns {Promise<boolean>} True if schema exists
 */
export const checkSchemaExists = async (schemaName) => {
  logger.debug(
    `schemaUtils.checkSchemaExists - Checking if schema exists: ${schemaName}`
  );

  try {
    const query = `
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name = :schemaName
    `;

    const results = await sequelize.query(query, {
      replacements: { schemaName },
      type: sequelize.QueryTypes.SELECT,
    });

    const exists = Array.isArray(results) && results.length > 0;
    logger.debug(
      `schemaUtils.checkSchemaExists - Schema ${schemaName} exists: ${exists}`
    );

    return exists;
  } catch (error) {
    logger.error(
      `schemaUtils.checkSchemaExists - Error checking schema ${schemaName}: ${error.message}`
    );
    throw error;
  }
};

/**
 * Drop a schema (use with caution)
 * @param {string} schemaName - The name of the schema to drop
 * @param {boolean} cascade - Whether to cascade the drop
 * @returns {Promise<boolean>} True if dropped successfully
 */
export const dropSchema = async (schemaName, cascade = false) => {
  logger.warn(
    `schemaUtils.dropSchema - Dropping schema: ${schemaName} (cascade: ${cascade})`
  );

  try {
    // Validate schema name
    validateSchemaName(schemaName);

    // Prevent dropping system schemas
    const systemSchemas = ["public", "information_schema", "pg_catalog"];
    if (systemSchemas.includes(schemaName.toLowerCase())) {
      throw new Error(`Cannot drop system schema: ${schemaName}`);
    }

    const cascadeClause = cascade ? "CASCADE" : "RESTRICT";
    const query = `DROP SCHEMA IF EXISTS "${schemaName}" ${cascadeClause}`;

    await sequelize.query(query);

    logger.info(
      `schemaUtils.dropSchema - Schema dropped successfully: ${schemaName}`
    );
    return true;
  } catch (error) {
    logger.error(
      `schemaUtils.dropSchema - Error dropping schema ${schemaName}: ${error.message}`
    );
    throw error;
  }
};

/**
 * List all schemas in the database
 * @returns {Promise<Array>} Array of schema names
 */
export const listSchemas = async () => {
  logger.debug("schemaUtils.listSchemas - Listing all schemas");

  try {
    const query = `
      SELECT schema_name 
      FROM information_schema.schemata 
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY schema_name
    `;

    const results = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
    });

    const schemas = Array.isArray(results)
      ? results.map((row) => row.schema_name)
      : [];
    logger.debug(`schemaUtils.listSchemas - Found ${schemas.length} schemas`);

    return schemas;
  } catch (error) {
    logger.error(
      `schemaUtils.listSchemas - Error listing schemas: ${error.message}`
    );
    throw error;
  }
};

/**
 * Get organization schema name from organization name
 * @param {string} organizationName - The organization name
 * @returns {string} The schema name for the organization
 */
export const getOrganizationSchemaName = (organizationName) => {
  logger.debug(
    `schemaUtils.getOrganizationSchemaName - Converting organization name: ${organizationName}`
  );

  try {
    const schemaName = convertToSnakeCase(organizationName);
    validateSchemaName(schemaName);

    logger.debug(
      `schemaUtils.getOrganizationSchemaName - Schema name: ${schemaName}`
    );
    return schemaName;
  } catch (error) {
    logger.error(
      `schemaUtils.getOrganizationSchemaName - Error converting organization name: ${error.message}`
    );
    throw error;
  }
};

/**
 * Create organization schema with validation
 * @param {string} organizationName - The organization name
 * @returns {Promise<string>} The created schema name
 */
export const createOrganizationSchema = async (organizationName) => {
  logger.info(
    `schemaUtils.createOrganizationSchema - Creating schema for organization: ${organizationName}`
  );

  try {
    const schemaName = getOrganizationSchemaName(organizationName);
    await createSchema(schemaName);

    logger.info(
      `schemaUtils.createOrganizationSchema - Organization schema created: ${schemaName}`
    );
    return schemaName;
  } catch (error) {
    logger.error(
      `schemaUtils.createOrganizationSchema - Error creating organization schema: ${error.message}`
    );
    throw error;
  }
};

export default {
  convertToSnakeCase,
  validateSchemaName,
  createSchema,
  checkSchemaExists,
  dropSchema,
  listSchemas,
  getOrganizationSchemaName,
  createOrganizationSchema,
};
