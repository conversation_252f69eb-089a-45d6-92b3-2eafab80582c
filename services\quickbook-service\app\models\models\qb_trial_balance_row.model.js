import { DataTypes } from "sequelize";

const QbTrialBalanceRowModel = (sequelize) => {
  const TrialBalanceRow = sequelize.define(
    "TrialBalanceRow",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "qb_trial_balance_reports",
          key: "id",
        },
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      quickbooks_account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      acct_num: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      class_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      account_type: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      column_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "qb_trial_balance_columns",
          key: "id",
        },
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      value: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "qb_trial_balance_rows",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );
  return TrialBalanceRow;
};

export default QbTrialBalanceRowModel;
