// ADP API CONFIGURATION
export const ADP_API = {
  BASE_URL: "https://api.adp.com",
  VERSION: "v1",
  ENDPOINTS: {
    EMPLOYEES: "/employees",
    PAYROLL: "/payroll",
    TIME_TRACKING: "/time-tracking",
  },
};

// METHOD TYPES
export const METHOD_TYPES = {
  GET: "get",
  POST: "post",
  PUT: "put",
  DELETE: "delete",
  PATCH: "patch",
};

// ADP MESSAGES
export const ADP_MESSAGES = {
  EMPLOYEES_FETCHED_SUCCESS: "Employees fetched successfully",
  EMPLOYEES_FETCH_FAILED: "Failed to fetch employees",
  PAYROLL_FETCHED_SUCCESS: "Payroll data fetched successfully",
  PAYROLL_FETCH_FAILED: "Failed to fetch payroll data",
  MODELS_INFO_FETCHED_SUCCESSFULLY: "Models information fetched successfully",
  FAILED_TO_LOAD_MODELS: "Failed to load models information",
  MODELS_DIRECTORY_NOT_FOUND: "Models directory not found",
  NO_DATA_RECEIVED: "No data received from API",
};

// MODEL FIELDS
export const MODEL_FIELDS = {
  EMPLOYEE_ID: "employee_id",
  EMPLOYEE_NAME: "employee_name",
  DEPARTMENT: "department",
  COMPANY_NAME: "company_name",
  PAYROLL_MONTH: "payroll_month",
  SSN: "ssn",
  TIN: "tin",
  PAY_FREQUENCY: "pay_frequency",
  TOTAL_HOURS: "total_hours",
  TOTAL_EARNINGS: "total_earnings",
  NET_PAY: "net_pay",
};

// VALIDATION RULES
export const VALIDATION_RULES = {
  EMPLOYEE_NAME_MIN_LENGTH: 1,
  EMPLOYEE_NAME_MAX_LENGTH: 255,
  DEPARTMENT_MIN_LENGTH: 1,
  DEPARTMENT_MAX_LENGTH: 100,
};

// VALIDATION MESSAGES
export const VALIDATION_MESSAGES = {
  EMPLOYEE_NAME_REQUIRED: "Employee name is required",
  EMPLOYEE_NAME_LENGTH: "Employee name must be between 1 and 255 characters",
  DEPARTMENT_REQUIRED: "Department is required",
  DEPARTMENT_LENGTH: "Department must be between 1 and 100 characters",
  VALIDATION_FAILED: "Validation failed",
  VALIDATION_PROCESSING_ERROR: "Error processing validation",
  DATA_MUST_BE_ARRAY: "Data must be an array",
  INVALID_DATA_TYPE: "Invalid data type",
  MISSING_REQUIRED_FIELDS: "Missing required fields",
  INVALID_OBJECT: "Invalid object",
};

// LOGGER CONFIGURATION
export const LOGGER_NAMES = {
  ADP_CONTROLLER: "adp-controller",
  ADP_SERVICE: "adp-service",
  VALIDATION_MIDDLEWARE: "validation-middleware",
  AWS_CONFIG: "aws-config",
};

// LOG ACTIONS
export const LOG_ACTIONS = {
  FETCHING_EMPLOYEES: "Fetching employees from ADP",
  EMPLOYEES_FETCHED: "Employees fetched successfully",
  EMPLOYEES_FETCH_FAILED: "Failed to fetch employees",
  FETCHING_PAYROLL: "Fetching payroll data from ADP",
  PAYROLL_FETCHED: "Payroll data fetched successfully",
  PAYROLL_FETCH_FAILED: "Failed to fetch payroll data",
  GETTING_MODELS_INFO: "Getting models information",
  ERROR_GETTING_MODELS_INFO: "Error getting models information",
  DATABASE_CONNECTION_CLOSE_SUCCESS: "Database connection closed successfully",
};

// MODULES AND OPERATIONS
export const MODULES = {
  ADP: "adp",
};

export const OPERATIONS = {
  FETCH_EMPLOYEES: "fetch_employees",
  FETCH_PAYROLL: "fetch_payroll",
  GET_MODELS_INFO: "get_models_info",
};

// CONFIGURATION DEFAULTS
export const CONFIG_DEFAULTS = {
  ADP_API_TIMEOUT: 30000, // 30 seconds
  DEFAULT_PAGE_SIZE: 50,
};

// VALIDATION DEFAULTS
export const VALIDATION_DEFAULTS = {
  DEFAULT_SOURCE: "body",
  LOG_MESSAGE_FAILED: "Validation failed",
  LOG_MESSAGE_MIDDLEWARE_ERROR: "Validation middleware error",
  LOG_MESSAGE_MULTI_FAILED: "Multi-source validation failed",
  LOG_MESSAGE_MULTI_ERROR: "Multi-source validation middleware error",
};

// BUSINESS LOGIC CONSTANTS
export const BUSINESS_CONSTANTS = {
  HTTP_SUCCESS_STATUS: 200,
  CREDENTIAL_SEPARATOR: ", ",
  STRING_TYPE: "string",
};

// HTTP HEADERS
export const HTTP_HEADERS = {
  AUTHORIZATION: "Authorization",
  CONTENT_TYPE: "Content-Type",
  APPLICATION_JSON: "application/json",
};

// ERROR MESSAGES FOR VALIDATION
export const ERROR_MESSAGES = {
  EMPLOYEE_NAME_REQUIRED_STRING: "Employee name is required and must be a string",
  DEPARTMENT_REQUIRED_STRING: "Department is required and must be a string",
  API_CALL_FAILED_FOR: "API call failed for",
  EMPLOYEES_FETCH_FAILED: "Failed to fetch employees",
  PAYROLL_FETCH_FAILED: "Failed to fetch payroll data",
};

// SERVER CONSTANTS
export const SERVER_CONSTANTS = {
  ADP_SERVER_LABEL: "adp-server",
  DEFAULT_ALLOWED_ORIGIN: "http://localhost:3000",
  HEALTH_ENDPOINT: "/health",
  API_ENDPOINT: "/api",
  CATCH_ALL_ROUTE: "*",
  ENVIRONMENT_DEVELOPMENT: "development",
};

// SERVER MESSAGES
export const SERVER_MESSAGES = {
  ADP_SERVICE_HEALTHY: "ADP service is healthy",
  ROUTE_NOT_FOUND: "Route not found",
  INTERNAL_SERVER_ERROR: "Internal server error",
  ADP_SERVICE_STARTED: "ADP service started on port",
  HEALTH_CHECK_URL: "Health check:",
  API_ENDPOINT_URL: "API endpoint:",
  ENVIRONMENT_INFO: "Environment:",
  SIGTERM_RECEIVED: "SIGTERM received, shutting down gracefully",
  SIGINT_RECEIVED: "SIGINT received, shutting down gracefully",
  PROCESS_TERMINATED: "Process terminated",
  UNHANDLED_ERROR: "Unhandled error:",
};

// REQUEST CONFIGURATION
export const REQUEST_CONFIG = {
  JSON_LIMIT: "10mb",
  URL_ENCODED_LIMIT: "10mb",
};

export const LOG_DATABASE = {
  INITIALIZE_DATABASE: "Initializing database connection...",
  INITIALIZE_DATABASE_SUCCESS: "Database connection initialized successfully",
  INITIALIZE_DATABASE_FAILED: "Database connection initialization failed",
  CONNECTION_FAILED: "Database connection failed",
  CONNECTED_TO_DATABASE: "Connected to database successfully",
  DATABASE_SYNCHRONIZED: "Database synchronized successfully",
  DATABASE_ERROR: "Database connection error occurred",
  DB_RECONNECT: "Attempting to reconnect to database",
  QUERY_TRYING: "Retrying database query",
  NO_EMPLOYEES_FOUND: "No employees found in API response",
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1,
};

export const LOG_ERRORS = {
  CREATING: "Error creating employee",
  ACCESS_DENIED: "Access denied. User does not have permission.",
  FETCHING_ALL: "Error fetching employees",
  FETCHING_BY_ID: "Error fetching employee by ID",
  UPDATING: "Error updating employee",
  DELETING: "Error deleting employee",
  CONNECTION_CLOSE_ERROR: "Error closing database connection",
};

// ADP SPECIFIC CONSTANTS
export const ADP_CONSTANTS = {
  EMPLOYEE_STATUS_ACTIVE: "active",
  EMPLOYEE_STATUS_INACTIVE: "inactive",
  PAYROLL_STATUS_PROCESSED: "processed",
  PAYROLL_STATUS_PENDING: "pending",
};

// CONTROLLER MESSAGES
export const CONTROLLER_MESSAGES = {
  EMPLOYEES_FETCHED: "Employees fetched successfully",
  PAYROLL_FETCHED: "Payroll data fetched successfully",
  OPERATION_FAILED: "Operation failed",
  MODELS_INFO_FETCHED: "Models information fetched successfully",
};

// LOGGER NAMES EXTENSION
export const LOGGER_NAMES_EXTENDED = {
  ADP_REPOSITORY: "adp-repository",
};

// SERVICE LAYER CONSTANTS
export const SERVICE_CONSTANTS = {
  FETCH_EMPLOYEES_OPERATION: "fetch_employees_operation",
  FETCH_PAYROLL_OPERATION: "fetch_payroll_operation",
  GET_MODELS_INFO_OPERATION: "get_models_info_operation",
};

// API RESPONSE FIELD MAPPINGS
export const API_RESPONSE_FIELDS = {
  EMPLOYEES: "employees",
  PAYROLL_DATA: "payroll_data",
  TOTAL_COUNT: "total_count",
  PAGE: "page",
  LIMIT: "limit",
};

// NUMERIC CONSTANTS
export const NUMERIC_CONSTANTS = {
  DEFAULT_EMPLOYEE_ID: 1,
  RADIX_DECIMAL: 10,
};
