export const monthToFile = {
  May: "/uploads/dashboard-may.pdf",
  June: "/uploads/dashboard-june.pdf",
  July: "/uploads/dashboard-july.pdf",
};

export const FinancePath = "/uploads/Financial.pdf";
export const OperationsPath = "/uploads/Operations.pdf";

// CHP specific file mappings
export const chpMonthToFile = {
  June: "/uploads/chp/Finance/CHP Finance Dashboard - June 2025.pdf",
  July: "/uploads/chp/Finance/CHP Finance Dashboard - July 2025.pdf",
  August: "/uploads/chp/Finance/CHP Finance Dashboard - August 2025.pdf",
};

export const chpOperationsMonthToFile = {
  June: "/uploads/chp/Operations/CHP Operations Dashboard - June 2025.pdf",
};

export const chpPayrollMonthToFile = {
  June: "/uploads/chp/Payroll/CHP Payroll Dashboard - June 2025.pdf",
  July: "/uploads/chp/Payroll/CHP Payroll Dashboard - July 2025.pdf",
  August: "/uploads/chp/Payroll/CHP Payroll Dashboard - August 2025.pdf",
};

// Dental specific file mappings
export const dentalMonthToFile = {
  June: "/uploads/dental/Finance/Dental Finance Dashboard - June 2025.pdf",
  July: "/uploads/dental/Finance/Dental Finance Dashboard - July 2025.pdf",
  August: "/uploads/dental/Finance/Dental Finance Dashboard - August 2025.pdf",
};

export const dentalOperationsMonthToFile = {
  June: "/uploads/dental/Operations/Dental Operations Dashboard - June 2025.pdf",
};

export const dentalPayrollMonthToFile = {
  June: "/uploads/dental/Payroll/Dental Payroll Dashboard - June 2025.pdf",
  July: "/uploads/dental/Payroll/Dental Payroll Dashboard - July 2025.pdf",
  August: "/uploads/dental/Payroll/Dental Payroll Dashboard - August 2025.pdf",
};