import React from "react";
import { <PERSON>, <PERSON>, Trash2, MoreHorizontal, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";

const iconMap = {
  edit: <Edit className="h-4 w-4" />,
  view: <Eye className="h-4 w-4" />,
  delete: <Trash2 className="h-4 w-4" />,
};

export default function TableActions({
  actions = [],
  item,
  className = "",
  variant = "buttons", // "buttons" or "dropdown"
}) {
  const handleAction = (action, item) => {
    if (action.onClick) {
      action.onClick(item);
    }
  };

  if (variant === "dropdown") {
    const hasLoading = actions.some((action) => action.loading);
    
    return (
      <div className={className}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" disabled={hasLoading}>
              {hasLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MoreHorizontal className="h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {actions.map((action, index) => {
              const isLoading = action.loading || false;
              
              return (
                <DropdownMenuItem
                  key={index}
                  onClick={() => !isLoading && handleAction(action, item)}
                  className={action.className || ""}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : action.icon ? (
                    <span className="mr-2">
                      {typeof action.icon === "string" ? iconMap[action.icon] : action.icon}
                    </span>
                  ) : null}
                  {action.label}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  // Map action labels to specific styles
  const getButtonStyles = (label, variant) => {
    // Dashboard button - secondary/outlined style
    if (label === "Dashboard" || label.includes("Dashboard")) {
      return "px-4 py-1.5 rounded-lg border border-indigo-200 text-indigo-600 hover:bg-indigo-50 text-sm font-medium transition-all duration-200";
    }
    
    // Submit Book-closure button - primary/solid style
    if (label === "Submit Book-closure" || label.includes("Book-closure")) {
      return "px-4 py-1.5 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 text-sm font-medium transition-all duration-200";
    }
    
    // Default styles
    return variant === "outline" 
      ? "px-4 py-1.5 rounded-lg border text-sm font-medium transition-all duration-200"
      : "px-4 py-1.5 rounded-lg text-sm font-medium transition-all duration-200";
  };

  return (
    <div className={`flex items-center ${className}`}>
      {actions.map((action, index) => {
        const isLoading = action.loading || false;
        const isDisabled = action.disabled || isLoading;
        const customStyles = getButtonStyles(action.label, action.variant);
        
        return (
          <button
            key={index}
            onClick={() => !isLoading && handleAction(action, item)}
            className={`${customStyles} ${index > 0 ? 'ml-2' : ''} ${action.className || ""} ${isDisabled ? "opacity-50 cursor-not-allowed" : ""} h-9 rounded-lg transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 flex items-center justify-center`}
            disabled={isDisabled}
          >
            {isLoading ? (
              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
            ) : action.icon ? (
              <span className="mr-1">
                {typeof action.icon === "string" ? iconMap[action.icon] : action.icon}
              </span>
            ) : null}
            {action.label}
          </button>
        );
      })}
    </div>
  );
}