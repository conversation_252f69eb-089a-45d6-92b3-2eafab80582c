import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";


import { AuthProvider } from "@/redux/Providers/AuthProvider";
import ToastProvider from "@/components/ui/toast";
import ErrorBoundary from "@/components/ui/error-boundary";
import ReduxProvider from "@/components/providers/ReduxProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: {
    default: "Perfino",
    template: "%s | Perfino",
  },
  icons: {
    icon: "/favicon.ico",
  },
  description:
    "Perfino - Your comprehensive financial dashboard for professional accounting management, bookkeeping, client management, and financial reporting. Streamline your accounting practice with our advanced dashboard.",
  keywords: [
    "Perfino",
    "Financial Dashboard",
    "Accounting Management",
    "Bookkeeping",
    "Client Management",
    "Financial Reporting",
    "Professional Accounting",
    "Perfino Software",
    "Accounting Dashboard",
  ],
  authors: [{ name: "Perfino" }],
  creator: "Perfino",
  publisher: "Perfino",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("http://perfino.getondataconsulting.in"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Perfino | Professional Financial Management",
    description:
      "Perfino - Your comprehensive financial dashboard for professional accounting management, bookkeeping, client management, and financial reporting.",
    url: "http://perfino.getondataconsulting.in",
    siteName: "Perfino",
    images: [
      {
        url: "/perfinologo.png",
        width: 1200,
        height: 630,
        alt: "Perfino - Professional Financial Management",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "business",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/perfinologo.png" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo-light.svg" />
        <meta name="theme-color" content="#4B3080" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          <ReduxProvider>
            <AuthProvider>
              <ToastProvider>{children}</ToastProvider>
            </AuthProvider>
          </ReduxProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
