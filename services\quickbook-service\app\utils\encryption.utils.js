import crypto from 'crypto';
import { ENCRYPTION_DEFAULTS } from './constants/config.constants.js';
import { ENCRYPTION_ERROR_MESSAGES } from './constants/error.constants.js';

 
// Secret key for encrypting and decrypting token
// const encryptionkey = process.env.ENCRYPTION_KEY;
const encryptionkey = crypto.scryptSync(process.env.ENCRYPTION_KEY, 'salt', 32); // Must be 32 bytes

/**
 * Simple encrypt function for backward compatibility
 * @param {string} data - Data to encrypt
 * @returns {Promise<string>} Encrypted data
 */
export const encrypt = async (text) => {
 
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(
    'aes-256-cbc',
    Buffer.from(encryptionkey),
    iv
  );
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`; // Store IV + Encrypted Text
};

/**
 * Simple decrypt function for backward compatibility
 * @param {string} encryptedData - Encrypted data
 * @returns {Promise<string>} Decrypted data
 */
export const decrypt = async (encryptedText) => {
  const [ivHex, encryptedData] = encryptedText.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(
    'aes-256-cbc',
    Buffer.from(encryptionkey),
    iv
  );
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

export default {
  encrypt,
  decrypt
};
