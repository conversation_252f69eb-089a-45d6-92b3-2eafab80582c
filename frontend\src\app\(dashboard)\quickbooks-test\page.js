"use client";

import { useState } from "react";
import {
  testQuickBooksCallback,
  validateQuickBooksConfig,
} from "@/utils/quickbooks";

export default function QuickBooksTestPage() {
  const [testParams, setTestParams] = useState({
    code: "L011234567890abcdef1234567890abcdef12345678",
    realmId: "123145678901234",
    state: "your_custom_state",
  });
  const [testResult, setTestResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const configValidation = validateQuickBooksConfig();

  const handleTest = async () => {
    setIsLoading(true);
    try {
      const result = await testQuickBooksCallback(testParams);
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setTestParams((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">
        QuickBooks OAuth2 Callback Test
      </h1>

      {/* Configuration Validation */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Configuration Status</h2>
        {configValidation.isValid ? (
          <div className="text-green-600">✅ Configuration is valid</div>
        ) : (
          <div className="text-red-600">
            ❌ Configuration errors:
            <ul className="list-disc list-inside mt-2">
              {configValidation.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="mt-3 text-sm text-gray-600">
          <div>
            <strong>Client ID:</strong>{" "}
            {configValidation.config.clientId || "Not set"}
          </div>
          <div>
            <strong>Redirect URI:</strong>{" "}
            {configValidation.config.redirectUri || "Not set"}
          </div>
          <div>
            <strong>Environment:</strong> {configValidation.config.environment}
          </div>
        </div>
      </div>

      {/* Test Parameters */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Test Parameters</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Authorization Code
            </label>
            <input
              type="text"
              value={testParams.code}
              onChange={(e) => handleInputChange("code", e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Enter authorization code"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              Realm ID (Company ID)
            </label>
            <input
              type="text"
              value={testParams.realmId}
              onChange={(e) => handleInputChange("realmId", e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Enter QuickBooks Company ID"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">State</label>
            <input
              type="text"
              value={testParams.state}
              onChange={(e) => handleInputChange("state", e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Enter state parameter"
            />
          </div>
        </div>

        <button
          onClick={handleTest}
          disabled={isLoading || !configValidation.isValid}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
        >
          {isLoading ? "Testing..." : "Test Callback API"}
        </button>
      </div>

      {/* Test Results */}
      {testResult && (
        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          <div
            className={`p-3 rounded ${
              testResult.success
                ? "bg-green-50 border-green-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div className="font-medium">
              {testResult.success ? "✅ Success" : "❌ Failed"}
            </div>
            {testResult.status && (
              <div className="text-sm mt-1">Status: {testResult.status}</div>
            )}
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">Response Data:</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(testResult.data || testResult, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>
            Make sure your environment variables are set correctly in .env.local
          </li>
          <li>
            The redirect URI in QuickBooks Developer Dashboard must match
            exactly:{" "}
            <code className="bg-white px-1 rounded">
              http://localhost:3000/api/quickbooks/callback
            </code>
          </li>
          <li>
            Use the test parameters above or get real values from a QuickBooks
            OAuth flow
          </li>
          <li>
            Click &quot;Test Callback API&quot; to verify the endpoint works
            correctly
          </li>
        </ol>
      </div>
    </div>
  );
}
