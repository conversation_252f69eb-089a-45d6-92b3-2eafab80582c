// app/utils/error.utils.js
import { STATUS_CODE_BAD_REQUEST, STATUS_CODE_INTERNAL_SERVER_ERROR } from "./status_code.utils.js";

/**
 * Error handler utility
 * @param {Error} error - Error object
 * @returns {Object} Handled error with status code and message
 */
export const errorHandler = (error) => {
  // Sequelize validation errors
  if (error.name === "SequelizeValidationError" || error.name === "SequelizeUniqueConstraintError") {
    return {
      statusCode: STATUS_CODE_BAD_REQUEST,
      message: error.message,
      details: error.errors?.map((err) => err.message).join(", "),
    };
  }

  // Sequelize database errors
  if (error.name === "SequelizeDatabaseError") {
    return {
      statusCode: STATUS_CODE_INTERNAL_SERVER_ERROR,
      message: "Database error occurred",
      details: error.message,
    };
  }

  // Generic error
  return {
    statusCode: STATUS_CODE_INTERNAL_SERVER_ERROR,
    message: error.message || "An unexpected error occurred",
    details: error.message,
  };
};

