import { DataTypes } from "sequelize";

const QbPnLSummaryModel = (sequelize) => {
  const PnLSummary = sequelize.define(
    "PnLSummary",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: "qb_pnl_reports",
          key: "id",
        },
      },
      group: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      label: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: true,
        defaultValue: 0,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "qb_pnl_summaries",
      timestamps: false,
    }
  );
  return PnLSummary;
};

export default QbPnLSummaryModel;
