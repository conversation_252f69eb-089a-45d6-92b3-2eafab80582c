// app/controllers/file.controller.js
import blobStorageService from "../services/blobStorage.service.js";
import logger from "../../config/logger.config.js";

/**
 * Upload a file to Azure Blob Storage
 * @route POST /api/files/upload
 */
export const uploadFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "No file provided",
        message: "Please provide a file to upload",
      });
    }

    const { originalname, buffer, mimetype } = req.file;
    
    // Use timestamp + original name for unique filenames
    const timestamp = Date.now();
    const fileName = `${timestamp}-${originalname}`;

    // Upload to Azure Blob Storage
    const result = await blobStorageService.uploadFile(
      buffer,
      fileName,
      mimetype
    );

    res.status(201).json({
      success: true,
      data: result,
      message: "File uploaded successfully",
    });
  } catch (error) {
    logger.error("Error in uploadFile controller:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to upload file",
    });
  }
};

/**
 * Download a file from Azure Blob Storage
 * @route GET /api/files/:filePath
 */
export const downloadFile = async (req, res) => {
  try {
    const { filePath } = req.params;

    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: "File path is required",
        message: "Please provide a file path",
      });
    }

    // Get file from Azure Blob Storage
    const { stream, properties } = await blobStorageService.downloadFile(filePath);

    // Set response headers
    res.setHeader("Content-Type", properties.contentType || "application/pdf");
    res.setHeader("Content-Length", properties.contentLength);
    res.setHeader("Content-Disposition", `inline; filename="${filePath}"`);
    res.setHeader("Cache-Control", "public, max-age=31536000");

    // Stream the file to client
    stream.pipe(res);
  } catch (error) {
    logger.error("Error in downloadFile controller:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        error: error.message,
        message: "File not found",
      });
    }

    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to download file",
    });
  }
};

/**
 * List all files in Azure Blob Storage
 * @route GET /api/files/list
 */
export const listFiles = async (req, res) => {
  try {
    const result = await blobStorageService.listFiles();

    res.status(200).json({
      success: true,
      data: result,
      message: "Files listed successfully",
    });
  } catch (error) {
    logger.error("Error in listFiles controller:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to list files",
    });
  }
};

/**
 * Delete a file from Azure Blob Storage
 * @route DELETE /api/files/:filePath
 */
export const deleteFile = async (req, res) => {
  try {
    const { filePath } = req.params;

    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: "File path is required",
        message: "Please provide a file path",
      });
    }

    const result = await blobStorageService.deleteFile(filePath);

    res.status(200).json({
      success: true,
      data: result,
      message: "File deleted successfully",
    });
  } catch (error) {
    logger.error("Error in deleteFile controller:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        error: error.message,
        message: "File not found",
      });
    }

    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to delete file",
    });
  }
};

/**
 * Get file metadata
 * @route GET /api/files/metadata/:filePath
 */
export const getFileMetadata = async (req, res) => {
  try {
    const { filePath } = req.params;

    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: "File path is required",
      });
    }

    const exists = await blobStorageService.fileExists(filePath);

    if (!exists) {
      return res.status(404).json({
        success: false,
        error: "File not found",
      });
    }

    const blobUrl = blobStorageService.getBlobUrl(filePath);

    res.status(200).json({
      success: true,
      data: {
        fileName: filePath,
        blobUrl,
        exists,
      },
    });
  } catch (error) {
    logger.error("Error in getFileMetadata controller:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to get file metadata",
    });
  }
};

