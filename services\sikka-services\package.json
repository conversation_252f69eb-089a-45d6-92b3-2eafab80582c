{"name": "perfino-dashboard-sikka-service", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest --coverage", "test:watch": "jest --watch"}, "description": "CPA Dashboard Sikka Service - Backend API for Sikka integration", "author": "Mobio Solutions", "license": "ISC", "keywords": ["cpa", "dashboard", "sikka", "api", "nodejs", "express"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.19.2", "express-validator": "^7.2.1", "helmet": "^8.0.0", "morgan": "^1.10.0", "nodemon": "^3.1.0", "pg": "^8.16.3", "sequelize": "^6.37.7", "winston": "^3.13.0"}, "devDependencies": {"@eslint/js": "^9.35.0", "eslint": "^9.35.0", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.15.0", "jest": "^29.7.0", "supertest": "^7.0.0"}}