"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import AddForm from "@/components/common/AddForm";
import { userFields } from "@/utils/data/users";
import { USER_CONSTANTS } from "@/utils/constants/user";
import { getUserById } from "@/redux/Thunks/userThunks.js";
import CustomSpinner from "@/components/common/CustomSpinner";

export default function ViewUserPage() {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch();
  const userId = params.id;

  const { user, loading, error } = useSelector((state) => ({
    user: state.users?.currentUser?.data || state.users?.currentUser || state.users?.data,
    loading: state.users?.loading,
    error: state.users?.error,
  }));

  useEffect(() => {
    if (userId) {
      dispatch(getUserById(userId));
    }
  }, [dispatch, userId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <CustomSpinner tip="Loading user details..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">
            Error Loading User
          </h2>
          <p className="text-gray-600 mt-2">
            {error?.message}
          </p>
          <button
            onClick={() => router.push("/masters/user")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  // User not found
  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">
            {USER_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_TITLE}
          </h2>
          <p className="text-gray-600 mt-2">
            {USER_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_MESSAGE}
          </p>
          <button
            onClick={() => router.push("/masters/user")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  const handleEdit = () => {
    router.push(`/masters/user/edit/${userId}`);
  };

  return (
    <div>
      {/* Temporary debug display */}
      {/* <div className="mb-4 p-4 bg-yellow-100 border border-yellow-400 rounded">
        <h3 className="font-bold text-yellow-800">Debug Information:</h3>
        <p>
          <strong>Loading:</strong> {loading ? "true" : "false"}
        </p>
        <p>
          <strong>Error:</strong> {error ? JSON.stringify(error) : "null"}
        </p>
        <p>
          <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : "null"}
        </p>
      </div> */}

      <AddForm
        heading={USER_CONSTANTS.VIEW_PAGE.HEADING}
        subTitle={USER_CONSTANTS.VIEW_PAGE.SUBTITLE}
        onBack={() => router.push("/masters/user")}
        backLabel={USER_CONSTANTS.VIEW_PAGE.BACK_LABEL}
        title={USER_CONSTANTS.VIEW_PAGE.TITLE}
        fields={userFields}
        initialValues={user}
        mode="view"
        onEdit={handleEdit}
      />
    </div>
  );
}
