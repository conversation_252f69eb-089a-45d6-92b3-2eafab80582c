import path from "path";
import fs from "fs";
import {
  readAndProcessFile,
  extractDatesFromFileName,
  getDynamicColumnMapping,
} from "../utils/excel.util.js";
import { adpRepository } from "../repositories/adp.repository.js";
import { withDb } from "../utils/db.util.js";
import { POSTGRES_CONFIG } from "../../config/db.config.js";
import { createLogger } from "../utils/logger.util.js";

const logger = createLogger("ADP_SERVICE");

/**
 * ADP Service
 */
export const adpService = {
  syncData: async (filePath, schemaName, orgId) => {
    logger.info("Starting ADP sync process", { filePath, schemaName, orgId });

    if (!fs.existsSync(filePath)) {
      logger.error("File not found", { filePath });
      throw new Error(`File not found: ${filePath}`);
    }

    const baseName = path.basename(filePath, path.extname(filePath));
    const parts = baseName.split("_");
    const companyName = parts[0];
    const payrollMonth = parts[parts.length - 1];

    logger.info("Parsed file information", { companyName, payrollMonth });

    const { from_date, to_date } = extractDatesFromFileName(filePath);
    logger.info("Extracted dates from file", { from_date, to_date });

    const payrollColumnMapping = getDynamicColumnMapping(filePath);
    logger.info("Retrieved dynamic column mapping", {
      columnCount: Object.keys(payrollColumnMapping).length,
    });

    return withDb(async (client) => {
      logger.info("Creating database tables", { schemaName });
      await adpRepository.createTables(
        client,
        payrollColumnMapping,
        schemaName
      );

      logger.info("Reading and processing file", { filePath });
      const records = readAndProcessFile(
        filePath,
        payrollColumnMapping,
        companyName,
        payrollMonth,
        from_date,
        to_date
      );

      if (!records || records.length === 0) {
        logger.warn("No records found in file", { filePath });
        return {
          message: `No records found in file ${filePath}`,
          insertedRecords: 0,
        };
      }

      logger.info("Inserting records into database", {
        recordCount: records.length,
        schemaName,
      });
      await adpRepository.insertData(
        client,
        records,
        payrollColumnMapping,
        schemaName
      );

      logger.info("Updating sync timestamp", { orgId, to_date });
      await adpService.updateSyncTime(client, orgId, to_date);

      logger.info("ADP sync completed successfully", {
        companyName,
        payrollMonth,
        insertedRecords: records.length,
      });

      return {
        message: `Data synced successfully for ${companyName} (${payrollMonth})`,
        insertedRecords: records.length,
        lastSyncedAt: to_date,
      };
    }, POSTGRES_CONFIG);
  },

  updateSyncTime: async (client, orgId, lastSyncedAt) => {
    logger.info("Updating sync time", { orgId, lastSyncedAt });

    // Update database sync time
    await adpRepository.updateSyncTime(client, orgId, lastSyncedAt);
    logger.info("Database sync time updated", { orgId });
  },
};
