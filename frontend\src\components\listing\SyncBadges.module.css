/* Sync Badges Styles - Modern, icon-based status indicators */

/* Container for all sync badges - Horizontal layout */
.syncBadgesContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem; /* gap-2 */
  align-items: center;
  min-width: 140px;
}

/* Individual sync badge chip - Rounded full style */
.syncBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem; /* 1.5 * 0.25rem = 6px */
  padding: 3px 0.75rem; /* py-[3px] px-3 */
  border-radius: 9999px; /* rounded-full */
  border-width: 1px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  width: fit-content;
  font-size: 0.75rem; /* text-xs */
}

.syncBadge:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow-md */
  transform: scale(1.02);
}

.syncBadge:active {
  transform: scale(0.98);
}

/* Status-specific background colors */

/* Operational - Amber/Orange */
.syncBadgeOperational {
  background-color: #FEF3C7; /* Amber-100 */
  border-color: #FCD34D; /* Amber-300 */
}

/* Payroll - Blue */
.syncBadgePayroll {
  background-color: #DBEAFE; /* Blue-100 */
  border-color: #93C5FD; /* Blue-300 */
}

/* Financial - Red */
.syncBadgeFinancial {
  background-color: #FEE2E2; /* Red-100 */
  border-color: #FCA5A5; /* Red-300 */
}

/* Synced - Green */
.syncBadgeSynced {
  background-color: rgb(240 253 244); /* bg-green-50 */
  border-color: rgb(187 247 208); /* border-green-200 */
}

.syncBadgeSyncing {
  background-color: rgb(254 252 232); /* bg-yellow-50 */
  border-color: rgb(254 240 138); /* border-yellow-200 */
}

.syncBadgeNotSynced {
  background-color: rgb(254 242 242); /* bg-red-50 */
  border-color: rgb(254 202 202); /* border-red-200 */
}

/* Status icon */
.syncIcon {
  width: 1rem; /* h-4 */
  height: 1rem; /* w-4 */
  flex-shrink: 0;
  transition: transform 0.2s ease-in-out;
}

.syncBadge:hover .syncIcon {
  transform: scale(1.1);
}

.syncIconSpinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Icon color variants */
.iconSynced {
  color: rgb(34 197 94); /* text-green-500 */
}

.iconSyncing {
  color: rgb(234 179 8); /* text-yellow-500 */
}

.iconNotSynced {
  color: rgb(239 68 68); /* text-red-500 */
}

/* Category-specific icon colors */
.iconOperational {
  color: #FBBF24; /* Amber-400 */
}

.iconPayroll {
  color: #60A5FA; /* Blue-400 */
}

.iconFinancial {
  color: #F87171; /* Red-400 */
}

/* Badge content container */
.badgeContent {
  display: flex;
  align-items: center;
  gap: 0.25rem; /* gap-1 */
  flex-wrap: nowrap;
}

/* Category name */
.categoryName {
  font-size: 0.75rem; /* text-xs */
  font-weight: 600; /* font-semibold */
  line-height: 1.25; /* leading-tight */
  white-space: nowrap;
}

/* Separator dot */
.separator {
  font-size: 0.75rem; /* text-xs */
  opacity: 0.7;
}

/* Status/Time text */
.statusText {
  font-size: 0.75rem; /* text-xs */
  font-weight: 500; /* font-medium */
  line-height: 1.25; /* leading-tight */
  white-space: nowrap;
}

/* Text color variants */
.textSynced {
  color: rgb(21 128 61); /* text-green-700 */
}

.textSyncing {
  color: rgb(161 98 7); /* text-yellow-700 */
}

.textNotSynced {
  color: rgb(185 28 28); /* text-red-700 */
}

/* Category-specific text colors */
.textOperational {
  color: #92400E; /* Amber-800 */
}

.textPayroll {
  color: #1E3A8A; /* Blue-900 */
}

.textFinancial {
  color: #991B1B; /* Red-800 */
}

