// Service configuration for external service endpoints
import dotenv from "dotenv";

dotenv.config();

export const SERVICES_CONFIG = {
  // Financial Service Configuration
  financial: {
    name: "Financial Service",
    baseUrl: process.env.FINANCIAL_SERVICE_URL || "http://localhost:3005",
    endpoints: {
      tables: "/api/quickbooks/tables",
      health: "/api/health",
    },
    timeout: 30000, // 30 seconds
    retries: 3,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  },

  // Operational Service Configuration
  operational: {
    name: "Operational Service",
    baseUrl: process.env.OPERATIONAL_SERVICE_URL || "http://localhost:3004",
    endpoints: {
      tables: "/api/sikka/tables",
      health: "/api/health",
    },
    timeout: 30000,
    retries: 3,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  },

  // Payroll Service Configuration
  payroll: {
    name: "Payroll Service",
    baseUrl: process.env.PAYROLL_SERVICE_URL || "http://localhost:3006",
    endpoints: {
      tables: "/api/adp/tables",
      health: "/api/health",
    },
    timeout: 30000,
    retries: 3,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  },
};

// Service validation
export const VALID_SERVICES = Object.keys(SERVICES_CONFIG);

// Default configuration
export const DEFAULT_CONFIG = {
  timeout: 30000,
  retries: 3,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
};

// Environment-specific overrides
export const getServiceConfig = (serviceName) => {
  const config = SERVICES_CONFIG[serviceName];
  if (!config) {
    throw new Error(
      `Invalid service name: ${serviceName}. Valid services: ${VALID_SERVICES.join(
        ", "
      )}`
    );
  }

  return {
    ...DEFAULT_CONFIG,
    ...config,
  };
};

// Health check configuration
export const HEALTH_CHECK_CONFIG = {
  interval: 60000, // 1 minute
  timeout: 5000, // 5 seconds
  retries: 2,
};

// API Response validation schema
export const API_RESPONSE_SCHEMA = {
  required: ["success", "message", "data"],
  properties: {
    success: { type: "boolean" },
    message: { type: "string" },
    data: {
      type: "object",
      required: ["modelsInfo"],
      properties: {
        modelsInfo: {
          type: "object",
          required: ["totalModels", "models"],
          properties: {
            totalModels: { type: "number" },
            models: { type: "object" },
            timestamp: { type: "string" },
          },
        },
      },
    },
  },
};

export default {
  SERVICES_CONFIG,
  VALID_SERVICES,
  DEFAULT_CONFIG,
  getServiceConfig,
  HEALTH_CHECK_CONFIG,
  API_RESPONSE_SCHEMA,
};
