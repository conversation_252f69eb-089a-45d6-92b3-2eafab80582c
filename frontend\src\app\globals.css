@import "tailwindcss";

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .gradient-bg {
    @apply bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500;
  }
  
  .gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-purple-500 to-pink-600;
  }

  /* Responsive utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .heading-responsive {
    @apply text-lg sm:text-xl lg:text-2xl xl:text-3xl;
  }

  .button-responsive {
    @apply text-xs sm:text-sm px-3 sm:px-4 py-2 sm:py-3;
  }

  .card-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .flex-responsive {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom scrollbar for Gen AI dialog */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #8b5cf6, #3b82f6);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7c3aed, #2563eb);
}

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* Basic animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Dashboard Summary Content Styling - Override inline styles */
.dashboard-summary-content {
  color: #374151 !important;
  font-size: 0.9375rem !important;
  line-height: 1.6 !important;
}

.dashboard-summary-content h2 {
  color: #111827 !important;
  font-weight: 700 !important;
  margin-top: 2.5rem !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 0.875rem !important;
  border-bottom: 2px solid #c7d2fe !important;
  font-size: 1.75rem !important;
  line-height: 1.4 !important;
}

.dashboard-summary-content h2:first-of-type {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.dashboard-summary-content h3 {
  color: #1f2937 !important;
  font-weight: 600 !important;
  margin-top: 2rem !important;
  margin-bottom: 1rem !important;
  font-size: 1.375rem !important;
  line-height: 1.5 !important;
}

/* Table wrapper styling */
.dashboard-summary-content > div[class*="overflow-x-auto"],
.dashboard-summary-content div.overflow-x-auto {
  margin: 2rem 0 !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  overflow: hidden !important;
}

.dashboard-summary-content table {
  width: 100% !important;
  border-collapse: collapse !important;
  background: white !important;
  margin: 0 !important;
  border: none !important;
  table-layout: auto !important;
}

.dashboard-summary-content table th {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  font-size: 0.8125rem !important;
  padding: 1rem 1.25rem !important;
  text-align: left !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  border-bottom: 2px solid #312e81 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: 1px solid rgba(255, 255, 255, 0.15) !important;
  white-space: nowrap !important;
}

.dashboard-summary-content table th:first-child {
  border-left: none !important;
}

.dashboard-summary-content table th:last-child {
  border-right: none !important;
}

.dashboard-summary-content table td {
  padding: 1rem 1.25rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  border-right: 1px solid #f3f4f6 !important;
  color: #374151 !important;
  font-size: 0.9375rem !important;
  background: white !important;
  border-top: none !important;
  border-left: none !important;
  vertical-align: top !important;
  word-wrap: break-word !important;
}

.dashboard-summary-content table td:first-child {
  border-left: none !important;
}

.dashboard-summary-content table td:last-child {
  border-right: none !important;
}

.dashboard-summary-content table tr {
  transition: background-color 0.15s ease !important;
}

.dashboard-summary-content table tbody tr:hover td {
  background-color: rgba(99, 102, 241, 0.05) !important;
}

.dashboard-summary-content table tbody tr:nth-child(even) td {
  background-color: rgba(249, 250, 251, 0.5) !important;
}

.dashboard-summary-content table tbody tr:last-child td {
  border-bottom: none !important;
}

.dashboard-summary-content strong,
.dashboard-summary-content b {
  font-weight: 700 !important;
  color: #4f46e5 !important;
}

.dashboard-summary-content ol {
  list-style-type: decimal !important;
  list-style-position: outside !important;
  margin: 1.5rem 0 !important;
  padding-left: 2rem !important;
  color: #374151 !important;
}

.dashboard-summary-content ol li {
  margin-bottom: 1rem !important;
  font-size: 1rem !important;
  line-height: 1.75 !important;
  color: #374151 !important;
  padding-left: 0.5rem !important;
}

.dashboard-summary-content p {
  color: #374151 !important;
  margin-bottom: 1.25rem !important;
  line-height: 1.75 !important;
  font-size: 1rem !important;
}

.dashboard-summary-content > *:first-child {
  margin-top: 0 !important;
}

.dashboard-summary-content > *:last-child {
  margin-bottom: 0 !important;
}

/* Ensure proper spacing between sections */
.dashboard-summary-content h2 + *,
.dashboard-summary-content h3 + * {
  margin-top: 0.5rem !important;
}
