import xlsx from "xlsx";
import path from "path";

export function extractDatesFromFileName(fileName) {
  const baseName = path.basename(fileName, path.extname(fileName));
  const parts = baseName.split("_");
  const toDatePart = parts.pop();
  const fromDatePart = parts.pop();

  function parseDate(str) {
    const match = str.match(/(\d{1,2})([a-zA-Z]+)(\d{4})/);
    if (!match) return null;
    const [, day, monthStr, year] = match;
    const month = new Date(`${monthStr} 1, 2000`).getMonth() + 1;
    return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
      2,
      "0"
    )}`;
  }

  return {
    from_date: parseDate(fromDatePart),
    to_date: parseDate(toDatePart),
  };
}

export function cleanAndParseNumeric(rawValue) {
  if (rawValue === null || rawValue === undefined || rawValue === "") return 0;
  let cleanedString = String(rawValue)
    .trim()
    .replace(/,/g, "")
    .replace(/\$/g, "")
    .replace(/£/g, "")
    .replace(/€/g, "")
    .replace(/%/g, "");
  const parsedValue = parseFloat(cleanedString);
  return isNaN(parsedValue) ? 0 : parsedValue;
}

export function sanitizeColumnName(header, index) {
  if (!header) return `column_${index}`;
  const rawTrimmedHeader = header.trim();
  if (rawTrimmedHeader === "401K Retirement %")
    return "retirement_401k_percentage";
  if (index === 0) return "employee_name";
  if (index === 1) return "ssn";
  if (index === 2) return "tin";
  if (index === 3) return "pay_frequency";
  if (index === 4) return "department";
  let cleanHeader = rawTrimmedHeader
    .replace(/[\s\.\-\/\#\%]/g, "_")
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, "")
    .replace(/__/g, "_")
    .replace(/_$/, "");
  return cleanHeader;
}

export function determineDataType(sqlName) {
  if (
    sqlName.includes("hours") ||
    sqlName.includes("rate") ||
    sqlName.includes("amount") ||
    sqlName.includes("total") ||
    sqlName.includes("tax") ||
    sqlName.includes("percentage")
  ) {
    return "NUMERIC";
  }
  if (sqlName.includes("date")) return "DATE";
  return "VARCHAR(255)";
}

export function getDynamicColumnMapping(filePath) {
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const rawData = xlsx.utils.sheet_to_json(worksheet, {
    header: 1,
    range: 0,
    raw: false,
  });
  const headerRow = rawData[0];
  const mapping = [];
  const uniqueNames = new Set();

  for (let i = 5; i < headerRow.length; i++) {
    const excelName = String(headerRow[i] || "").trim();
    const sqlName = sanitizeColumnName(excelName, i);
    if (sqlName.startsWith("column_") || uniqueNames.has(sqlName)) continue;
    uniqueNames.add(sqlName);
    const dataType = determineDataType(sqlName);
    mapping.push({ index: i, excelName, sqlName, dataType });
  }

  return mapping;
}

export function readAndProcessFile(
  filePath,
  payrollColumnMapping,
  COMPANY_NAME,
  PAYROLL_MONTH,
  from_date,
  to_date
) {
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const rawData = xlsx.utils.sheet_to_json(worksheet, {
    header: 1,
    range: 1,
    raw: false,
  });
  const records = rawData.filter((r) => r && r.length > 0 && r[0]);

  return records.map((row) => {
    const record = {
      employee_name: String(row[0] || "").trim(),
      ssn: String(row[1] || "").trim(),
      tin: String(row[2] || "").trim(),
      pay_frequency: String(row[3] || "").trim(),
      department: String(row[4] || "").trim(),
      company_name: COMPANY_NAME,
      payroll_month: PAYROLL_MONTH,
      from_date,
      to_date,
    };

    payrollColumnMapping.forEach((col) => {
      const rawValue = row[col.index] || "";
      if (col.dataType === "NUMERIC")
        record[col.sqlName] = cleanAndParseNumeric(rawValue);
      else record[col.sqlName] = String(rawValue).trim();
    });

    return record;
  });
}
