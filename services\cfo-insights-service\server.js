import dotenv from "dotenv";
dotenv.config();

import express from "express";
import bodyParser from "body-parser";
import { CHAT_ERROR_MESSAGES } from "./app/utils/constants/error.constants.js";
import cors from "cors";

import mainRouter from "./app/routes/index.js";

console.log("📝 Starting CFO Insights Service...");

const app = express();

// ✅ Middleware setup

// ✅ Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: "10mb" }));

// ✅ Graceful handling for invalid JSON payloads
app.use((err, _req, res, next) => {
  if (err instanceof SyntaxError && "body" in err) {
    console.error("Invalid JSON payload:", err.message);
    return res.status(400).json({
      success: false,
      message: CHAT_ERROR_MESSAGES.INVALID_JSON_PAYLOAD,
      error: err.message,
    });
  }
  next(err);
});

// ✅ API routes
app.use("/api", mainRouter);

// ✅ Default route
app.get("/", (_req, res) => {
  res.send("CFO Insights Chat Service is running 🚀");
});

// ✅ Port setup — will use 3009 by default
const PORT = process.env.CFO_INSIGHTS_SERVICE_PORT || 3007;

// ✅ Start the server
app.listen(PORT, () => {
  console.log(`✅ CFO Insights Service is running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}`);
  console.log(`📊 API base: http://localhost:${PORT}/api`);
});
