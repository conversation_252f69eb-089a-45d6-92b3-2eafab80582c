import api from "@/redux/ApiService/ApiService";

import { SERVICE_PORTS } from "@/utils/constants/api";

import { createAsyncThunk } from "@reduxjs/toolkit";

const axios = api(SERVICE_PORTS.QUICKBOOK);

// Fetch QuickBooks accounts
export const fetchQuickbooksAccounts = createAsyncThunk(
  "quickbooksAccount/fetchAccounts",
  async ({ organization_id }, { rejectWithValue }) => {
    try {
      return await axios.get("/quickbooks/accounts", {
        params: { organization_id },
      });
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Update QuickBooks account status
export const updateQuickbooksAccountStatus = createAsyncThunk(
  "quickbooksAccount/updateStatus",
  async ({ id, status }, { rejectWithValue }) => {
    try {
      return await axios.patch(`/quickbooks/accounts/${id}/status`, {
        status,
      });
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Sync QuickBooks account
export const syncQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/syncAccount",
  async ({ id, organization_id }, { rejectWithValue }) => {
    try {
      return await axios.post(`/quickbooks/accounts/${id}/sync`, {
        organization_id,
      });
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Delete QuickBooks account
export const deleteQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/deleteAccount",
  async ({ id }, { rejectWithValue }) => {
    try {
      return await axios.delete(`/quickbooks/accounts/${id}`);
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Get QuickBooks OAuth URL
export const getQuickbooksOAuthUrl = createAsyncThunk(
  "quickbooksAccount/getOAuthUrl",
  async (_, { rejectWithValue }) => {
    try {
      return await axios.get("/quickbooks/oauth-url");
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Add QuickBooks account with OAuth code
export const addQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/addAccount",
  async (
    { code, realmId, email, schemaName, organization_id },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.get("/quickbooks/add", {
        params: { code, realmId, email, schemaName, organization_id },
      });
      return response;
    } catch (error) {
      console.error("QuickBooks add API error:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers,
        },
      });
      return rejectWithValue(error.response?.data);
    }
  }
);
