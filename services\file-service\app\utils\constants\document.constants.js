// DOCUMENT CONSTANTS
// Static messages and validation constants for Document Management

export const DOCUMENT_MESSAGES = {
  // Success Messages
  STORED_SUCCESSFULLY: "Document stored successfully",
  UPDATED_SUCCESSFULLY: "Document updated successfully",
  RETRIEVED_SUCCESSFULLY: "Documents retrieved successfully",

  // Error Messages
  STORAGE_FAILED: "Failed to store document",
  RETRIEVAL_FAILED: "Failed to retrieve documents",
  MISSING_REQUIRED_FIELDS: "Missing required fields: organization_id, blob_storage_path, service, month, and year are required",
  MISSING_ORGANIZATION_ID: "organization_id is required",
  ORGANIZATION_ID_REQUIRED_ERROR: "Required fields: organization_id, blob_storage_path, service, month, year",

  // Validation Messages
  INVALID_SERVICE_TYPE: "Invalid service type. Must be one of: financial, operational, pms",
  INVALID_MONTH: "Month must be between 1 and 12",
  INVALID_YEAR: "Year must be between 2000 and 3000",

  // General Error Labels
  INVALID_SERVICE_ERROR: "Invalid service",
  INVALID_MONTH_ERROR: "Invalid month",
  INVALID_YEAR_ERROR: "Invalid year",
  FETCH_FAILED: "Failed to fetch documents",

  // Document Status
  DOCUMENT_EXISTS: "Document already exists for this organization, service, month, and year",
  DOCUMENT_NOT_FOUND: "Document not found",
};

export const DOCUMENT_LOG_MESSAGES = {
  // Service Log Messages
  SERVICE_START_STORAGE: "Starting document storage",
  SERVICE_MISSING_FIELDS: "Missing required fields",
  SERVICE_INVALID_SERVICE: "Invalid service",
  SERVICE_INVALID_MONTH: "Invalid month",
  SERVICE_INVALID_YEAR: "Invalid year",
  SERVICE_DOCUMENT_EXISTS: "Document already exists, updating existing record",
  SERVICE_DOCUMENT_UPDATED: "Document updated successfully",
  SERVICE_DOCUMENT_STORED: "Document stored successfully",
  SERVICE_STORAGE_ERROR: "Error storing document",
  SERVICE_GETTING_DOCUMENTS: "Getting documents",
  SERVICE_RETRIEVAL_ERROR: "Error retrieving documents",

  // Controller Log Messages
  CONTROLLER_START_STORAGE: "Starting document storage request",
  CONTROLLER_FIELD_VALIDATION: "Missing required fields",
  CONTROLLER_STORAGE_SUCCESS: "Document stored successfully",
  CONTROLLER_STORAGE_FAILED: "Document storage failed",
  CONTROLLER_STORAGE_UNEXPECTED_ERROR: "Unexpected error",
  CONTROLLER_GET_REQUEST: "Get documents request",
  CONTROLLER_GET_ERROR: "Error getting documents",
};

export const DOCUMENT_VALIDATION_RULES = {
  MONTH_MIN: 1,
  MONTH_MAX: 12,
  YEAR_MIN: 2000,
  YEAR_MAX: 3000,
};

export const DOCUMENT_SERVICE_LIST = [
  "financial",
  "operational",
  "payroll",
];

export default {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
  DOCUMENT_SERVICE_LIST,
  DOCUMENT_VALIDATION_RULES,
};

