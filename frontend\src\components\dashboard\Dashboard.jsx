import React, { useState, useEffect, useCallback, useMemo, lazy, Suspense, memo } from "react";
import PdfViewer from "@/components/dashboard/PDFViewer";
import DashboardSummaryPopup from "@/components/dashboard/CFO-chatbot/DashboardSummaryPopup";
import {
  monthToFile,
  chpMonthToFile,
  chpOperationsMonthToFile,
  chpPayrollMonthToFile,
  dentalMonthToFile,
  dentalOperationsMonthToFile,
  dentalPayrollMonthToFile,
} from "@/utils/data/dashboard";
import Sidebar from "./Sidebar";
import MobileMenu from "./MobileMenu";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import { useDispatch, useSelector } from 'react-redux';
import { downloadFile } from '@/redux/Thunks/fileOperations';
import { startChatSession, sendSummaryMessage } from '@/redux/Thunks/chat';
import { setDashboardSummary, setLoading } from '@/redux/Slice/chat';
import tokenStorage from "@/lib/tokenStorage";
import { useSearchParams } from "next/navigation";

// Lazy load CFOInsightsPage for better performance
const CFOInsightsPage = lazy(() => import("@/components/dashboard/CFOInsightsPage"));

// Memoized PdfViewer to prevent unnecessary re-renders
const MemoizedPdfViewer = memo(PdfViewer);

// Memoized Sidebar to prevent unnecessary re-renders
const MemoizedSidebar = memo(Sidebar);

// Memoized MobileMenu to prevent unnecessary re-renders
const MemoizedMobileMenu = memo(MobileMenu);

export default function Dashboard() {
  const [userEmail, setUserEmail] = useState("");
  const [userRole, setUserRole] = useState("");
  const [availableMonths, setAvailableMonths] = useState(
    DASHBOARD_CONSTANTS.SIDEBAR.MONTHS
  );
  const [months, setMonths] = useState(
    DASHBOARD_CONSTANTS.SIDEBAR.MONTHS.at(-1)
  );
  const [pageToView, setPageToView] = useState(1);
  const [isFinancialSelected, setIsFinancialSelected] = useState(true);
  const [isOperationsSelected, setIsOperationsSelected] = useState(false);
  const [isPayrollSelected, setIsPayrollSelected] = useState(false);
  const [showCFOInsights, setShowCFOInsights] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState(""); // New state for dashboard selection
  const [organizationName, setOrganizationName] = useState(null);
  const [showSummaryPopup, setShowSummaryPopup] = useState(false);

  const searchParams = useSearchParams();

  // Get dashboard summary from Redux
  const { dashboardSummary, ui: { isLoading }, session } = useSelector((state) => state.chat);

  // Get user data and determine available months
  useEffect(() => {
    const emailParam = searchParams.get("email");
    let effectiveEmail = emailParam;
    let effectiveRole = "";
    if (!effectiveEmail) {
      const userData = tokenStorage.getUserData();
      if (userData?.email) {
        effectiveEmail = userData.email;
        effectiveRole = userData.role?.name;
      }
    }
    if (effectiveEmail) {
      setUserEmail(effectiveEmail);
      setUserRole(effectiveRole);
      const normalizedEmail = effectiveEmail.toLowerCase();
      if (
        normalizedEmail === "<EMAIL>" ||
        normalizedEmail === "<EMAIL>"
      ) {
        setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
        setMonths("June");
      } else {
        setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.MONTHS);
        setMonths(DASHBOARD_CONSTANTS.SIDEBAR.MONTHS.at(-1));
      }
    }
  }, [searchParams]);

  // Auto-select dashboard and organization based on URL parameter
  useEffect(() => {
    const dashboardParam = searchParams.get("dashboard");
    const organizationParam = searchParams.get("organization");
    
    if (organizationParam) {
      setOrganizationName(decodeURIComponent(organizationParam));
    }
    
    if (
      dashboardParam &&
      (dashboardParam === "chp" || dashboardParam === "dental")
    ) {
      setSelectedDashboard(dashboardParam);
      setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
      setMonths("June");
      setIsFinancialSelected(true);
      setIsOperationsSelected(false);
      setIsPayrollSelected(false);
    }
  }, [searchParams]);

  // Get the appropriate file mapping based on user type and section
  const getFileMapping = useCallback(() => {
    let mapping;
    
    // Normalize email for case-insensitive comparison
    const normalizedEmail = userEmail?.toLowerCase() || "";

    // If admin has selected a specific dashboard
    if (userRole === "admin" && selectedDashboard) {
      if (selectedDashboard === "chp") {
        if (isOperationsSelected) mapping = chpOperationsMonthToFile;
        else if (isPayrollSelected) mapping = chpPayrollMonthToFile;
        else mapping = chpMonthToFile;
      } else if (selectedDashboard === "dental") {
        if (isOperationsSelected) mapping = dentalOperationsMonthToFile;
        else if (isPayrollSelected) mapping = dentalPayrollMonthToFile;
        else mapping = dentalMonthToFile;
      }
    } else if (normalizedEmail === "<EMAIL>") {
      // Original logic for specific users (case-insensitive)
      if (isOperationsSelected) mapping = chpOperationsMonthToFile;
      else if (isPayrollSelected) mapping = chpPayrollMonthToFile;
      else mapping = chpMonthToFile;
    } else if (normalizedEmail === "<EMAIL>") {
      // Case-insensitive comparison
      if (isOperationsSelected) mapping = dentalOperationsMonthToFile;
      else if (isPayrollSelected) mapping = dentalPayrollMonthToFile;
      else mapping = dentalMonthToFile;
    } else {
      mapping = monthToFile;
    }

    return mapping;
  }, [
    userEmail,
    userRole,
    selectedDashboard,
    isOperationsSelected,
    isPayrollSelected,
  ]);

  // Memoize PDF path to prevent unnecessary re-renders
  const pdfPath = useMemo(() => {
    const fileMapping = getFileMapping();
    const selectedFile = fileMapping[months];
    // For admin with selected dashboard
    if (userRole === "admin" && selectedDashboard) {
      return selectedFile || null;
    }

    // For CHP and Dental users, use the appropriate file mapping based on section (case-insensitive)
    const normalizedEmail = userEmail?.toLowerCase() || "";
    if (
      normalizedEmail === "<EMAIL>" ||
      normalizedEmail === "<EMAIL>"
    ) {
      return selectedFile || null;
    }

    // For other users: no dashboards available
    return null;
  }, [
    months,
    getFileMapping,
    userEmail,
    userRole,
    selectedDashboard,
  ]);

  // Memoize page number to prevent unnecessary re-renders
  const page = useMemo(() => {
    return isFinancialSelected || isOperationsSelected || isPayrollSelected
      ? pageToView
      : 1;
  }, [
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    pageToView,
  ]);

  // Memoize PDF viewer props to prevent unnecessary re-renders
  const pdfViewerProps = useMemo(
    () => ({
      url: pdfPath,
      pageToView: page,
    }),
    [pdfPath, page]
  );
  const dispatch = useDispatch();

  // Handler function declarations
  const handleDownload = useCallback(async () => {
    if (!pdfPath) return;
    try {
      const filename =
        pdfPath.split("/").pop() ||
        DASHBOARD_CONSTANTS.DEFAULT_DOWNLOAD_FILENAME;
      await dispatch(downloadFile({ url: pdfPath, filename }));
    } catch (error) {
      console.error("Download failed:", error);
      // Error handling is done in the thunk
    }
  }, [pdfPath, dispatch]);

  // Memoized handlers to prevent unnecessary re-renders
  const handleGenAIClick = useCallback(() => setShowCFOInsights(true), []);
  const handleBackFromCFOInsights = useCallback(() => setShowCFOInsights(false), []);

  // Handle View Summary button click - fetches summary for specific section
  const handleViewSummary = useCallback(async (summaryType) => {
    try {
      // Clear previous summary and show loading immediately
      dispatch(setDashboardSummary(null));
      dispatch(setLoading(true));
      
      // Show the popup immediately when clicked
      setShowSummaryPopup(true);

      // Get the appropriate filename based on the summary type and current selections
      let filename = '';

      if (summaryType === 'financial') {
        if (selectedDashboard === 'chp') {
          filename = chpMonthToFile[months] || '';
        } else if (selectedDashboard === 'dental') {
          filename = dentalMonthToFile[months] || '';
        } else {
          filename = monthToFile[months] || '';
        }
      } else if (summaryType === 'operations') {
        if (selectedDashboard === 'chp') {
          filename = chpOperationsMonthToFile[months] || '';
        } else if (selectedDashboard === 'dental') {
          filename = dentalOperationsMonthToFile[months] || '';
        }
      } else if (summaryType === 'payroll') {
        if (selectedDashboard === 'chp') {
          filename = chpPayrollMonthToFile[months] || '';
        } else if (selectedDashboard === 'dental') {
          filename = dentalPayrollMonthToFile[months] || '';
        }
      }

      if (!filename) {
        console.error('No filename found for summary type:', summaryType);
        dispatch(setLoading(false));
        setShowSummaryPopup(false);
        return;
      }

      // Extract only the filename from the path (remove directory structure)
      // Handle both forward and backward slashes
      const actualFilename = filename.includes('/') || filename.includes('\\')
        ? filename.split(/[/\\]/).pop()
        : filename;

      // Check if we need a new session (if filename changed or no active session)
      let sessionId = session.id;
      
      // Compare using the actual filename (not the full path)
      const sessionFilename = session.filename 
        ? (session.filename.includes('/') || session.filename.includes('\\')
            ? session.filename.split(/[/\\]/).pop()
            : session.filename)
        : null;
      
      if (!sessionId || sessionFilename !== actualFilename) {
        // Start a new chat session with only the filename (no path)
        const startResult = await dispatch(startChatSession({ filename: actualFilename }));

        if (startChatSession.fulfilled.match(startResult)) {
          sessionId = startResult.payload.sessionId;
        } else {
          // If session creation failed, close popup
          dispatch(setLoading(false));
          setShowSummaryPopup(false);
          return;
        }
      }

      // Fetch the summary with the sessionId
      await dispatch(sendSummaryMessage({
        sessionId,
        organization: organizationName,
      }));
    } catch (error) {
      console.error('Failed to fetch summary:', error);
      dispatch(setLoading(false));
      setShowSummaryPopup(false);
    }
  }, [dispatch, months, selectedDashboard, organizationName, session]);

  // Memoized mobile menu toggle handler
  const handleMobileMenuToggle = useCallback(() => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  }, [isMobileMenuOpen]);

  // Handle View Summary button click - determines which summary to show based on current selection
  const handleViewSummaryClick = useCallback(() => {
    let summaryType = 'financial'; // default
    if (isOperationsSelected) {
      summaryType = 'operations';
    } else if (isPayrollSelected) {
      summaryType = 'payroll';
    }
    handleViewSummary(summaryType);
  }, [isOperationsSelected, isPayrollSelected, handleViewSummary]);

  // Memoize sidebarProps to prevent unnecessary re-renders
  const sidebarProps = useMemo(() => ({
    months,
    setMonths,
    pageToView,
    setPageToView,
    isFinancialSelected,
    setIsFinancialSelected,
    isOperationsSelected,
    setIsOperationsSelected,
    isPayrollSelected,
    setIsPayrollSelected,
    availableMonths,
    onDownload: handleDownload,
    onGenAIClick: handleGenAIClick,
    onViewSummary: handleViewSummary,
    userRole,
    selectedDashboard,
    onDashboardChange: setSelectedDashboard,
  }), [
    months,
    pageToView,
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    availableMonths,
    handleDownload,
    handleGenAIClick,
    handleViewSummary,
    userRole,
    selectedDashboard,
  ]);

  // Dashboard selection component for admins
  const DashboardSelector = () => {
    if (userRole !== "admin") return null;

    return (
      <div
        className="flex flex-col lg:flex-row w-full h-full min-h-0"
        style={{ height: "100vh" }}
      >
        <div className="flex-1 h-full overflow-y-auto overflow-x-hidden flex items-center justify-center bg-gray-50 min-w-0 p-2 sm:p-4">
          <div className="w-full max-w-4xl">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                  Select Dashboard
                </h1>
                <p className="text-gray-600">
                  Choose which dashboard you want to view
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* CHP Dashboard Button */}
                <button
                  onClick={() => {
                    setSelectedDashboard("chp");
                    setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
                    setMonths("June");
                    setIsFinancialSelected(true);
                    setIsOperationsSelected(false);
                    setIsPayrollSelected(false);
                  }}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-8 text-white transition-all duration-300 hover:from-blue-600 hover:to-blue-700 hover:scale-105 hover:shadow-xl"
                >
                  <div className="relative z-10">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-8 h-8"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold mb-2">CHP Dashboard</h3>
                      <p className="text-blue-100 mb-4">
                        View CHP financial, operations, and payroll data
                      </p>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Finance Reports:</span>
                        <span className="font-semibold">
                          June, July, August
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Operations:</span>
                        <span className="font-semibold">June</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Payroll:</span>
                        <span className="font-semibold">June, July</span>
                      </div>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </button>

                {/* Dental Dashboard Button */}
                <button
                  onClick={() => {
                    setSelectedDashboard("dental");
                    setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
                    setMonths("June");
                    setIsFinancialSelected(true);
                    setIsOperationsSelected(false);
                    setIsPayrollSelected(false);
                  }}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-500 to-green-600 p-8 text-white transition-all duration-300 hover:from-green-600 hover:to-green-700 hover:scale-105 hover:shadow-xl"
                >
                  <div className="relative z-10">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-8 h-8"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold mb-2">
                        Dental Dashboard
                      </h3>
                      <p className="text-green-100 mb-4">
                        View Dental financial, operations, and payroll data
                      </p>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Finance Reports:</span>
                        <span className="font-semibold">
                          June, July, August
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Operations:</span>
                        <span className="font-semibold">June</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Payroll:</span>
                        <span className="font-semibold">June, July</span>
                      </div>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // If no PDF path and not in CFO Insights mode, show proper message
  if (!pdfPath && !showCFOInsights) {
    return (
      <div className="flex flex-col lg:flex-row w-full h-full min-h-0" style={{ height: "100vh" }}>
        <MemoizedMobileMenu
          isOpen={isMobileMenuOpen}
          onToggle={handleMobileMenuToggle}
        >
          <MemoizedSidebar {...sidebarProps} hideAllOptionsExceptBack={true} />
        </MemoizedMobileMenu>
        <div className="hidden lg:block w-56 xl:w-64 flex-shrink-0">
          <MemoizedSidebar {...sidebarProps} hideAllOptionsExceptBack={true} />
        </div>
        <div className="flex-1 h-full flex flex-col items-center justify-center bg-gray-50 min-w-0 p-2 sm:p-4">
          <div className="text-gray-500 text-2xl font-semibold text-center">
            No dashboards available for your account.
          </div>
        </div>
      </div>
    );
  }

  // Show dashboard selector for admins who haven't selected a dashboard
  if (userRole === "admin" && !selectedDashboard) {
    return <DashboardSelector />;
  }

  if (showCFOInsights) {
    return (
      <div
        className="flex flex-col lg:flex-row w-full h-full min-h-0"
        style={{ height: "100vh" }}
      >
        <MemoizedMobileMenu
          isOpen={isMobileMenuOpen}
          onToggle={handleMobileMenuToggle}
        >
          <MemoizedSidebar {...sidebarProps} />
        </MemoizedMobileMenu>
        <div className="hidden lg:block w-64 flex-shrink-0">
          <MemoizedSidebar {...sidebarProps} />
        </div>
        <div className="flex-1 h-full overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 pl-4">
          <Suspense fallback={
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="relative w-16 h-16 mx-auto mb-4">
                  <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 border-r-purple-400 rounded-full animate-spin"></div>
                  <div className="absolute inset-2 border-4 border-transparent border-t-indigo-500 border-r-indigo-400 rounded-full animate-spin" style={{ animationDirection: 'reverse' }}></div>
                </div>
                <p className="text-gray-700 font-medium">Loading CFO Insights...</p>
              </div>
            </div>
          }>
            <CFOInsightsPage
              onBack={handleBackFromCFOInsights}
              selectedMonth={months}
              selectedMonthKey={months}
              selectedPage={pageToView}
              isFinancialSelected={isFinancialSelected}
              isOperationsSelected={isOperationsSelected}
              isPayrollSelected={isPayrollSelected}
              selectedDashboard={selectedDashboard || (userEmail?.toLowerCase() === "<EMAIL>" ? "chp" : userEmail?.toLowerCase() === "<EMAIL>" ? "dental" : "chp")}
              organizationName={organizationName}
            />
          </Suspense>
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col lg:flex-row w-full h-full min-h-0"
      style={{ height: "100vh" }}
    >
      <MemoizedMobileMenu
        isOpen={isMobileMenuOpen}
        onToggle={handleMobileMenuToggle}
      >
        <MemoizedSidebar {...sidebarProps} />
      </MemoizedMobileMenu>
      <div className="hidden lg:block w-56 xl:w-64 flex-shrink-0">
        <MemoizedSidebar {...sidebarProps} />
      </div>


      <div className="flex-1 h-full overflow-y-auto overflow-x-hidden flex flex-col bg-gray-50 min-w-0 p-2 sm:p-4">
        <div className="w-full max-w-7xl mx-auto">
          {/* Top Bar with Summary Button */}
          <div className="flex justify-end mb-4">
            <button
              onClick={handleViewSummaryClick}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg active:scale-95 font-medium text-sm sm:text-base"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z" />
              </svg>
              <span className="hidden sm:inline">View Summary</span>
              <span className="sm:hidden">Summary</span>
            </button>
          </div>

          {/* PDF Viewer */}
          <div className="flex-1 flex items-start justify-center">
            <MemoizedPdfViewer {...pdfViewerProps} />
          </div>
        </div>
      </div>

      {/* Dashboard Summary Popup */}
      <DashboardSummaryPopup
        isOpen={showSummaryPopup}
        onClose={() => setShowSummaryPopup(false)}
        dashboardSummary={dashboardSummary}
        selectedMonth={months}
        isFinancialSelected={isFinancialSelected}
        isOperationsSelected={isOperationsSelected}
        isLoading={isLoading}
      />
    </div>
  );
}
