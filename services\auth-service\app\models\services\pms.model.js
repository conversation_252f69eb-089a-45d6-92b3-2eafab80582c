import { DataTypes } from 'sequelize';

/**
 * PMS (Property Management System) Service Models
 * These models will be created in organization-specific schemas
 */

/**
 * Properties Model - Manages real estate properties
 */
export const PropertiesModel = (sequelize, schemaName) => {
  const Properties = sequelize.define(
    'properties',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      property_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: true,
      },
      property_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      property_type: {
        type: DataTypes.ENUM('residential', 'commercial', 'industrial', 'mixed_use', 'land'),
        allowNull: false,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      city: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      zip_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
      country: {
        type: DataTypes.STRING(50),
        defaultValue: 'USA',
      },
      total_units: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
      },
      total_area: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      purchase_price: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      current_value: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'under_construction', 'sold'),
        defaultValue: 'active',
      },
      owner_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      manager_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'properties',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_properties_code', fields: ['property_code'] },
        { name: 'idx_properties_type', fields: ['property_type'] },
        { name: 'idx_properties_city', fields: ['city'] },
        { name: 'idx_properties_status', fields: ['status'] },
        { name: 'idx_properties_manager', fields: ['manager_id'] },
      ],
    }
  );

  return Properties;
};

/**
 * Tenants Model - Manages property tenants
 */
export const TenantsModel = (sequelize, schemaName) => {
  const Tenants = sequelize.define(
    'tenants',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tenant_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: true,
      },
      first_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      emergency_contact_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      emergency_contact_phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      date_of_birth: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      ssn_last_four: {
        type: DataTypes.STRING(4),
        allowNull: true,
      },
      employment_status: {
        type: DataTypes.ENUM('employed', 'self_employed', 'unemployed', 'retired', 'student'),
        allowNull: true,
      },
      monthly_income: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'pending', 'evicted'),
        defaultValue: 'pending',
      },
      move_in_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      move_out_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'tenants',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_tenants_code', fields: ['tenant_code'] },
        { name: 'idx_tenants_email', fields: ['email'] },
        { name: 'idx_tenants_status', fields: ['status'] },
        { name: 'idx_tenants_name', fields: ['first_name', 'last_name'] },
      ],
    }
  );

  return Tenants;
};

/**
 * Leases Model - Manages property lease agreements
 */
export const LeasesModel = (sequelize, schemaName) => {
  const Leases = sequelize.define(
    'leases',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      lease_number: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      property_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'properties',
          key: 'id',
        },
      },
      tenant_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
      },
      unit_number: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      lease_start_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      lease_end_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      monthly_rent: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
      security_deposit: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      pet_deposit: {
        type: DataTypes.DECIMAL(10, 2),
        defaultValue: 0.00,
      },
      late_fee: {
        type: DataTypes.DECIMAL(8, 2),
        defaultValue: 0.00,
      },
      lease_type: {
        type: DataTypes.ENUM('fixed_term', 'month_to_month', 'week_to_week'),
        defaultValue: 'fixed_term',
      },
      status: {
        type: DataTypes.ENUM('active', 'expired', 'terminated', 'pending'),
        defaultValue: 'pending',
      },
      auto_renew: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'leases',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_leases_number', fields: ['lease_number'] },
        { name: 'idx_leases_property', fields: ['property_id'] },
        { name: 'idx_leases_tenant', fields: ['tenant_id'] },
        { name: 'idx_leases_status', fields: ['status'] },
        { name: 'idx_leases_dates', fields: ['lease_start_date', 'lease_end_date'] },
      ],
    }
  );

  return Leases;
};

// Export all PMS models
export const PMSModels = {
  PropertiesModel,
  TenantsModel,
  LeasesModel,
};

export default PMSModels;
