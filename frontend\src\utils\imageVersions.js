/**
 * Utility function to get versioned image URLs
 * This helps with cache busting for static assets
 */

export const getVersionedImage = (imagePath, version = null) => {
  if (!imagePath) return '';
  
  // If version is provided, append it as a query parameter
  if (version) {
    const separator = imagePath.includes('?') ? '&' : '?';
    return `${imagePath}${separator}v=${version}`;
  }
  
  // For development, use timestamp for cache busting
  if (process.env.NODE_ENV === 'development') {
    const separator = imagePath.includes('?') ? '&' : '?';
    return `${imagePath}${separator}t=${Date.now()}`;
  }
  
  return imagePath;
};

export default getVersionedImage;
