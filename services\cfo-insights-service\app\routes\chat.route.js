// app/routes/chat.route.js
import express from "express";
import {
  handleStartChat,
  handleChatMessage,
  handleEndChat,
  handleChatSummary,
} from "../controllers/chat.controller.js";

const router = express.Router();

router.post("/start", handleStartChat);        // { filename }
router.post("/message", handleChatMessage);    // { sessionId, message, organization? } (chat mode)
router.post("/summary", handleChatSummary);    // { sessionId, organization?, message? } (summary mode; message optional)
router.post("/end", handleEndChat);            // { sessionId }

export default router;
