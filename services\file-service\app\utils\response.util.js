// app/utils/response.util.js

/**
 * Create a success response
 * @param {string} message - Success message
 * @param {any} data - Response data
 * @returns {Object} Success response object
 */
export const successResponse = (message, data = null) => {
  return {
    success: true,
    message,
    data,
  };
};

/**
 * Create an error response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Error response object
 */
export const errorResponse = (message, error = null) => {
  const response = {
    success: false,
    message,
  };
  
  if (error) {
    response.error = error;
  }
  
  return response;
};

