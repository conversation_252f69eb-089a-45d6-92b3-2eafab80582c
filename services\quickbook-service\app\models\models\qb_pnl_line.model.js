import { DataTypes } from "sequelize";

const QbPnLLineModel = (sequelize) => {
  const PnLLine = sequelize.define(
    "PnLLine",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: "qb_pnl_reports",
          key: "id",
        },
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: true,
        defaultValue: 0,
      },
      category: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "qb_pnl_lines",
      timestamps: false,
    }
  );
  return PnLLine;
};

export default QbPnLLineModel;
