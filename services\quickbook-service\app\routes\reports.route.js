import express from 'express';
import * as reportController from '../controllers/reports.controller.js';

const router = express.Router();

router.post('/trial-balance', reportController.fetchTrialBalance);
router.post('/profit-loss', reportController.fetchProfitLoss);
router.post('/balance-sheet', reportController.fetchBalanceSheet); 
router.post('/cash-flow', reportController.fetchCashFlow);

router.get('/trial-balance/:realmId', reportController.getTrialBalanceReports);
router.get('/profit-loss/:realmId', reportController.getProfitLossReports);
router.get('/balance-sheet/:realmId', reportController.getBalanceSheetReports);
router.get('/cash-flow/:realmId', reportController.getCashFlowReports);

export default router;