import sequelize from "../../config/postgres.config.js";
import AppUserModel from "./app_user.model.js";
import AppTokenModel from "./app_token.model.js";
import AppOrganizationModel from "./app_organization.model.js";

const models = {
  app_user: AppUserModel(sequelize),
  app_token: AppTokenModel(sequelize),
  app_organization: AppOrganizationModel(sequelize),
};

export const {
  app_token: AppToken,
  app_organization: AppOrganization,
  app_user: AppUser,
} = models;

export { sequelize };
export default models;
