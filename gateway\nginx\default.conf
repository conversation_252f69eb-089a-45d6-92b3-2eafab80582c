server {
    listen 80;
    server_name localhost;

    # Frontend routing
    location / {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # -------------------
    # API routing with rewrite for each service
    # -------------------

    # ADP Service
    location ~ ^/api/adp(/|$) {
        rewrite ^/api/adp(/.*)?$ /api$1 break;
        proxy_pass http://adp-service:3006;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # User Service
    location ~ ^/api/users(/|$) {
        rewrite ^/api/users(/.*)?$ /api$1 break;
        proxy_pass http://user-service:3003;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
 
    # CFO Insights Service (Chat)
    location ~ ^/api/chat(/|$) {
        rewrite ^/api/chat(/.*)?$ /api$1 break;
        proxy_pass http://cfo-insights-service:3007;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Auth Service
    location ~ ^/api/auth(/|$) {
        rewrite ^/api/auth(/.*)?$ /api$1 break;
        proxy_pass http://auth-service:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Tenant Service
    location ~ ^/api/tenant(/|$) {
        rewrite ^/api/tenant(/.*)?$ /api$1 break;
        proxy_pass http://tenant-service:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Sikka Service
    location ~ ^/api/sikka(/|$) {
        rewrite ^/api/sikka(/.*)?$ /api$1 break;
        proxy_pass http://sikka-services:3004;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Quickbook Service
    location ~ ^/api/quickbook(/|$) {
        rewrite ^/api/quickbook(/.*)?$ /api$1 break;
        proxy_pass http://quickbook-service:3005;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # -------------------
    # Error pages
    # -------------------
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
 
 