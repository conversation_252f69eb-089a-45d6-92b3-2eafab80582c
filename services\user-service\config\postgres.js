// config/aws-config.js
import { Sequelize } from "sequelize";
import dotenv from "dotenv";
import { createLogger } from "../app/utils/logger.util.js";
import {
  LOG_ACTIONS,
  LOG_DATABASE,
  LOG_ERRORS,
  LOGGER_NAMES,
} from "../app/utils/constants.util.js";

const logger = createLogger(LOGGER_NAMES.AWS_CONFIG);

dotenv.config();

// Helper function to get password and strip quotes
const getPassword = (password) => {
  if (!password) return '';
  return String(password).replace(/^["']|["']$/g, '');
};

// Get database environment variables
const dbHost = process.env.DB_HOST;
const dbName = process.env.DB_NAME;
const dbUser = process.env.DB_USER;
const dbPass = process.env.DB_PASS;
const dbSsl = process.env.DB_SSL;
const dbPort = process.env.DB_PORT || 5432;

// Process password (strip quotes)
const password = getPassword(dbPass);

// Determine SSL configuration
// If DB_SSL is explicitly set, use it; otherwise auto-detect based on host
// Enable SSL for AWS RDS or any remote host (not localhost)
const isRemoteHost = dbHost && !dbHost.includes('localhost') && !dbHost.includes('127.0.0.1');
const dbSslEnabled = typeof dbSsl !== 'undefined'
  ? (dbSsl.toLowerCase() === 'true')
  : isRemoteHost;

// Build Sequelize configuration
const sequelizeConfig = {
  host: dbHost,
  port: dbPort,
  dialect: "postgres",
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};

// Add SSL configuration if enabled (required for AWS RDS)
if (dbSslEnabled) {
  sequelizeConfig.dialectOptions = {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  };
}

// Validate required fields
if (!password) {
  logger.error('❌ Database password is missing. Please set DB_PASS');
} else {
  logger.info(`✅ Database password configured (length: ${password.length} characters)`);
}
if (!dbName) {
  logger.error('❌ Database name is missing. Please set DB_NAME');
} else {
  logger.info(`✅ Database name: ${dbName}`);
}
if (!dbUser) {
  logger.error('❌ Database user is missing. Please set DB_USER');
} else {
  logger.info(`✅ Database user: ${dbUser}`);
}
if (!dbHost) {
  logger.error('❌ Database host is missing. Please set DB_HOST');
} else {
  logger.info(`✅ Database host: ${dbHost}`);
}

// Log configuration summary
logger.info(`📊 Database Configuration`, {
  host: dbHost,
  port: dbPort,
  database: dbName,
  user: dbUser,
  ssl: dbSslEnabled,
  isRemote: isRemoteHost,
});

const sequelize = new Sequelize(
  dbName,
  dbUser,
  password,
  sequelizeConfig
);
// Connection status tracking
let connectionStatus = {
  connected: false,
  lastCheck: null,
  retryCount: 0,
  databaseType: "postgres",
  host: dbHost,
  ssl: dbSslEnabled,
};
/**
 * Test database connection
 * @returns {Promise<Object>} Connection test result
 */
async function testConnection() {
  try {
    await sequelize.authenticate();
    const [results] = await sequelize.query("SELECT version() as version");
    const version = results[0]?.version || "Unknown";
    // Update connection status
    connectionStatus.connected = true;
    connectionStatus.lastCheck = new Date();
    connectionStatus.retryCount = 0;
    return {
      success: true,
      message: LOG_DATABASE.CONNECTED_TO_DATABASE,
      version: version,
      config: {
        host: dbHost,
        port: dbPort,
        database: dbName,
        user: dbUser,
        dialect: "postgres",
        ssl: dbSslEnabled,
      },
    };
  } catch (error) {
    // Update connection status
    connectionStatus.connected = false;
    connectionStatus.lastCheck = new Date();
    connectionStatus.retryCount += 1;
    return {
      success: false,
      error: error.message,
      config: {
        host: dbHost,
        port: dbPort,
        database: dbName,
        user: dbUser,
        dialect: "postgres",
        ssl: dbSslEnabled,
      },
    };
  }
}
/**
 * Get current connection status
 * @returns {Object} Connection status object
 */
function getConnectionStatus() {
  return { ...connectionStatus };
}
/**
 * Health check for the database
 * @returns {Promise<Object>} Health check result
 */
async function healthCheck() {
  try {
    const connectionTest = await testConnection();
    return {
      database: connectionTest.success ? "connected" : "disconnected",
      timestamp: new Date().toISOString(),
      status: connectionTest.success ? "healthy" : "unhealthy",
      connection: connectionTest,
    };
  } catch (error) {
    return {
      database: "error",
      timestamp: new Date().toISOString(),
      status: "error",
      connection: {
        success: false,
        error: error.message,
      },
    };
  }
}
/**
 * Close database connection
 * @returns {Promise<void>}
 */
async function closeConnection() {
  try {
    await sequelize.close();
    connectionStatus.connected = false;
    connectionStatus.lastCheck = new Date();
    logger.info(LOG_ACTIONS.DATABASE_CONNECTION_CLOSE_SUCCESS);
  } catch (error) {
    logger.error(LOG_ERRORS.CONNECTION_CLOSE_ERROR, error);
    throw error;
  }
}

/**
 * Create Authentication schema if it doesn't exist
 * @returns {Promise<void>}
 */
async function createAuthenticationSchema() {
  try {
    logger.info("🔧 Creating Authentication schema if not exists...");

    // Create the Authentication schema if it doesn't exist
    await sequelize.query('CREATE SCHEMA IF NOT EXISTS "Authentication"');

    // Set the search path to include the Authentication schema
    await sequelize.query('SET search_path TO "Authentication", public');

    logger.info("✅ Authentication schema created/verified successfully");
  } catch (error) {
    logger.error("❌ Error creating Authentication schema:", error);
    throw error;
  }
}

/**
 * Initialize database connection and create required schemas
 * @returns {Promise<Object>} Initialization result
 */
async function initializeDatabase() {
  try {
    logger.info(`🔌 Initializing PostgreSQL connection...`);

    const connectionResult = await testConnection();

    if (connectionResult.success) {
      // Create Authentication schema if it doesn't exist
      await createAuthenticationSchema();

      logger.info("✅ PostgreSQL database initialized successfully");
      return connectionResult;
    } else {
      logger.error("❌ PostgreSQL database initialization failed");
      return connectionResult;
    }
  } catch (error) {
    logger.error("❌ PostgreSQL database initialization error:", error);
    throw error;
  }
}
// Sequelize CLI configuration
const config = {
  username: dbUser,
  password: password,
  database: dbName,
  host: dbHost,
  port: dbPort,
  dialect: "postgres",
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  ...(dbSslEnabled ? {
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    }
  } : {}),
};
// Export for application use
export {
  testConnection,
  getConnectionStatus,
  healthCheck,
  closeConnection,
  createAuthenticationSchema,
  initializeDatabase,
  config,
};

// Export sequelize instance as default for models
export default sequelize;

// Export configuration object for Sequelize CLI (if needed separately)
export const cliConfig = {
  development: config,
  test: config,
  production: config,
};