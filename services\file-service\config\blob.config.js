// config/blob.config.js
import dotenv from "dotenv";
dotenv.config();

// Auth Service Configuration
export const authConfig = {
  authServiceUrl: process.env.AUTH_SERVICE_URL,
};

export const blobConfig = {
  connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING,
  containerName: process.env.AZURE_BLOB_CONTAINER_NAME,
  
  // Validate required environment variables
  validate() {
    if (!this.connectionString) {
      throw new Error("AZURE_STORAGE_CONNECTION_STRING is not defined in environment variables");
    }
    if (!this.containerName) {
      throw new Error("AZURE_BLOB_CONTAINER_NAME is not defined in environment variables");
    }
    return true;
  }
};

// Default container setup
export const containerOptions = {
  publicAccessLevel: "blob", // Allow public access to blobs (URLs)
};

// Blob upload options
export const uploadOptions = {
  blobHTTPHeaders: {
    blobContentType: "application/pdf",
    blobCacheControl: "public, max-age=31536000", // Cache for 1 year
  },
};
