"use strict";

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface
      .createTable(
        "app_user",
        {
          id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV4,
            primaryKey: true,
          },
          tenant_id: {
            type: Sequelize.UUID,
            allowNull: false,
          },
          email: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          password_hash: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          full_name: {
            type: Sequelize.TEXT,
          },
          phone_number: {
            type: Sequelize.TEXT,
          },
          roles: {
            type: Sequelize.ARRAY(Sequelize.TEXT),
          },
          // Authentication fields
          email_verified: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          email_verified_at: {
            type: Sequelize.DATE,
          },
          last_login: {
            type: Sequelize.DATE,
          },
          // MFA fields
          mfa_enabled: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          mfa_secret: {
            type: Sequelize.TEXT,
          },
          mfa_backup_codes: {
            type: Sequelize.TEXT, // JSON string
          },
          // Invitation fields
          invited_by: {
            type: Sequelize.UUID,
          },
          invited_at: {
            type: Sequelize.DATE,
          },
          is_active: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },
          created_by: {
            type: Sequelize.UUID,
          },
          updated_at: {
            type: Sequelize.DATE,
          },
          updated_by: {
            type: Sequelize.UUID,
          },
        },
        {
          schema: "Authentication",
          timestamps: true,
          createdAt: "created_at",
          updatedAt: "updated_at",
        }
      )
      .then(function () {
        // Create indexes
        return queryInterface.addIndex(
          "app_user",
          {
            name: "idx_user_tenant",
            fields: ["tenant_id"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_user",
          {
            name: "unique_email_per_tenant",
            unique: true,
            fields: ["tenant_id", "email"],
          },
          {
            schema: "Authentication",
          }
        );
      });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable("app_user", {
      schema: "Authentication",
    });
  },
};
