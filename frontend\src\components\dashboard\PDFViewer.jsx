"use client";

import React, { useState, useEffect, memo } from "react";

// Import react-pdf CSS for proper styling
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import "./PDFViewer.css";

// Create a client-side only PDF viewer component
const ClientPDFViewer = memo(function ClientPDFViewer({ url, pageToView = 1 }) {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(pageToView);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [Document, setDocument] = useState(null);
  const [Page, setPage] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [width, setWidth] = useState(800);

  // All hooks must be called before any conditional returns
  useEffect(() => {
    setPageNumber(pageToView);

    // Calculate responsive scale and width
    const calculateDimensions = () => {
      if (typeof window !== "undefined") {
        const screenWidth = window.innerWidth;
        const newScale =
          screenWidth < 768 ? 0.8 : screenWidth < 1024 ? 1.0 : 1.2;
        const newWidth = Math.min(screenWidth * 0.8, 1000);
        setScale(newScale);
        setWidth(newWidth);
      }
    };

    calculateDimensions();
    window.addEventListener("resize", calculateDimensions);

    // Dynamically import react-pdf components
    Promise.all([
      import("react-pdf").then((mod) => mod.Document),
      import("react-pdf").then((mod) => mod.Page),
      import("react-pdf").then((mod) => mod.pdfjs),
    ])
      .then(([DocumentComponent, PageComponent, pdfjs]) => {
        // Set up PDF.js worker - use CDN with correct version
        pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

        setDocument(() => DocumentComponent);
        setPage(() => PageComponent);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error loading PDF components:", err);
        setError("Failed to load PDF viewer");
        setLoading(false);
      });

    return () => {
      window.removeEventListener("resize", calculateDimensions);
    };
  }, [pageToView]);

  // Check if URL is valid - after all hooks
  if (!url) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="text-gray-600 text-lg">No PDF file selected</div>
          <div className="text-gray-500 text-sm mt-2">Please select a month to view the dashboard</div>
        </div>
      </div>
    );
  }

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setError(null);
  };

  const onDocumentLoadError = (error) => {
    console.error("Error loading PDF:", error);
    // Check if it's a network error or file not found
    if (error?.message?.includes("Failed to fetch")) {
      setError("Failed to fetch PDF. Please check if the file exists and try again.");
    } else {
      setError("Failed to load PDF document. The file may be corrupted or unavailable.");
    }
  };

  const onPageLoadError = (error) => {
    console.error("Error loading page:", error);
    setError("Failed to load PDF page. Please try selecting a different month.");
  };

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-gray-600 text-lg">Loading PDF viewer...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="text-red-500 text-xl font-semibold mb-4">Error</div>
          <div className="text-gray-600 text-lg">{error}</div>
        </div>
      </div>
    );
  }

  if (!Document || !Page) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="text-gray-600 text-lg">
            Initializing PDF viewer...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pdf-viewer-container w-full h-full flex flex-col items-center justify-start bg-white p-2 sm:p-4 overflow-visible">
      {/* Page indicator - positioned at top */}
      <div className="mb-4 sm:mb-6 text-xs sm:text-sm text-gray-500 font-medium">
        Page {pageNumber} of {numPages}
      </div>

      {/* PDF container - centered with proper sizing */}
      <div className="flex-1 flex items-start justify-center w-full max-w-7xl overflow-visible">
        <div className="bg-white rounded-lg shadow-lg p-2 sm:p-4 w-full flex justify-center overflow-visible">
          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div className="text-center py-8 sm:py-12">
                <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-500 mx-auto mb-3 sm:mb-4"></div>
                <div className="text-gray-600 text-base sm:text-lg">
                  Loading document...
                </div>
              </div>
            }
          >
            <Page
              key={`page_${pageNumber}`}
              pageNumber={pageNumber}
              onLoadError={onPageLoadError}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              scale={scale}
              width={width}
            />
          </Document>
        </div>
      </div>
    </div>
  );
});

// Main PDFViewer component that handles SSR
const PDFViewer = memo(function PDFViewer({ url, pageToView = 1 }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-gray-600 text-lg">Loading PDF...</div>
        </div>
      </div>
    );
  }

  return <ClientPDFViewer url={url} pageToView={pageToView} />;
});

export default PDFViewer;
