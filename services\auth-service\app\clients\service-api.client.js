// HTTP client for fetching models information from external services
import axios from "axios";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import {
  getServiceConfig,
  API_RESPONSE_SCHEMA,
} from "../config/services.config.js";
import { createServiceResponse } from "../utils/response.util.js";
import * as status from "../utils/status_code.utils.js";

const logger = createLogger(
  LOGGER_NAMES.SERVICE_API_CLIENT || "ServiceApiClient"
);

/**
 * Service API Client for fetching models information
 */
export class ServiceApiClient {
  constructor() {
    this.clients = new Map();
    this.initializeClients();
  }

  /**
   * Initialize HTTP clients for each service
   */
  initializeClients() {
    const services = ["financial", "operational", "payroll"];

    services.forEach((serviceName) => {
      try {
        const config = getServiceConfig(serviceName);

        const client = axios.create({
          baseURL: config.baseUrl,
          timeout: config.timeout,
          headers: config.headers,
          validateStatus: (status) => status < 500, // Don't throw for 4xx errors
        });

        // Request interceptor
        client.interceptors.request.use(
          (config) => {
            logger.debug(
              `ServiceApiClient - Making request to ${serviceName}: ${config.method?.toUpperCase()} ${
                config.url
              }`
            );
            return config;
          },
          (error) => {
            logger.error(
              `ServiceApiClient - Request error for ${serviceName}: ${error.message}`
            );
            return Promise.reject(error);
          }
        );

        // Response interceptor
        client.interceptors.response.use(
          (response) => {
            logger.debug(
              `ServiceApiClient - Response from ${serviceName}: ${response.status} ${response.statusText}`
            );
            return response;
          },
          (error) => {
            logger.error(
              `ServiceApiClient - Response error for ${serviceName}: ${error.message}`
            );
            return Promise.reject(error);
          }
        );

        this.clients.set(serviceName, client);
        logger.info(
          `ServiceApiClient - Initialized client for ${serviceName} service`
        );
      } catch (error) {
        logger.error(
          `ServiceApiClient - Failed to initialize client for ${serviceName}: ${error.message}`
        );
      }
    });
  }

  /**
   * Fetch models information from a specific service
   * @param {string} serviceName - Name of the service (financial, operational, pms)
   * @returns {Promise<Object>} Service response with models information
   */
  async fetchModelsInfo(serviceName) {
    logger.info(
      `ServiceApiClient.fetchModelsInfo - Fetching models from ${serviceName} service`
    );

    try {
      const client = this.clients.get(serviceName);
      if (!client) {
        throw new Error(`No client configured for service: ${serviceName}`);
      }

      const config = getServiceConfig(serviceName);
      const endpoint = config.endpoints.tables;

      // Make the API call with retries
      const response = await this.makeRequestWithRetries(
        client,
        endpoint,
        config.retries
      );
      // Validate response status
      if (response.status !== 200) {
        throw new Error(
          `Service returned status ${response.status}: ${response.statusText}`
        );
      }

      // Validate response structure
      const validationResult = this.validateApiResponse(response.data);
      if (!validationResult.isValid) {
        throw new Error(
          `Invalid API response structure: ${validationResult.errors.join(
            ", "
          )}`
        );
      }

      logger.info(
        `ServiceApiClient.fetchModelsInfo - Successfully fetched ${response.data.data.modelsInfo.totalModels} models from ${serviceName}`
      );
      return createServiceResponse(
        true,
        status.STATUS_CODE_OK,
        `Models information fetched successfully from ${serviceName}`,
        {
          serviceName,
          modelsInfo: response.data.data.modelsInfo,
          fetchedAt: new Date().toISOString(),
        }
      );
    } catch (error) {
      logger.error(
        `ServiceApiClient.fetchModelsInfo - Error fetching models from ${serviceName}: ${error.message}`
      );

      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        `Failed to fetch models from ${serviceName} service`,
        null,
        error.message
      );
    }
  }

  /**
   * Fetch models information from multiple services
   * @param {Array<string>} serviceNames - Array of service names
   * @returns {Promise<Object>} Combined service response
   */
  async fetchModelsFromServices(serviceNames) {
    logger.info(
      `ServiceApiClient.fetchModelsFromServices - Fetching models from services: ${serviceNames.join(
        ", "
      )}`
    );

    const results = [];
    const errors = [];

    // Fetch from all services in parallel
    const promises = serviceNames.map(async (serviceName) => {
      try {
        const result = await this.fetchModelsInfo(serviceName);
        if (result.success) {
          results.push(result.data);
        } else {
          errors.push({ service: serviceName, error: result.error });
        }
        return result;
      } catch (error) {
        errors.push({ service: serviceName, error: error.message });
        return null;
      }
    });

    await Promise.allSettled(promises);

    const totalModels = results.reduce(
      (sum, result) => sum + (result.modelsInfo?.totalModels || 0),
      0
    );

    logger.info(
      `ServiceApiClient.fetchModelsFromServices - Fetched ${totalModels} total models from ${results.length}/${serviceNames.length} services`
    );

    return createServiceResponse(
      results.length > 0,
      results.length > 0
        ? status.STATUS_CODE_OK
        : status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      `Fetched models from ${results.length}/${serviceNames.length} services`,
      {
        services: results,
        totalModels,
        totalServices: serviceNames.length,
        successfulServices: results.length,
        errors: errors.length > 0 ? errors : undefined,
      }
    );
  }

  /**
   * Make HTTP request with retry logic
   * @param {Object} client - Axios client instance
   * @param {string} endpoint - API endpoint
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Promise<Object>} HTTP response
   */
  async makeRequestWithRetries(client, endpoint, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.debug(
          `ServiceApiClient.makeRequestWithRetries - Attempt ${attempt}/${maxRetries} for ${endpoint}`
        );

        const response = await client.get(endpoint);
        return response;
      } catch (error) {
        lastError = error;
        logger.warn(
          `ServiceApiClient.makeRequestWithRetries - Attempt ${attempt}/${maxRetries} failed: ${error.message}`
        );

        if (attempt < maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          const delay = Math.pow(2, attempt - 1) * 1000;
          logger.debug(
            `ServiceApiClient.makeRequestWithRetries - Waiting ${delay}ms before retry`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Validate API response structure
   * @param {Object} responseData - Response data to validate
   * @returns {Object} Validation result
   */
  validateApiResponse(responseData) {
    const errors = [];

    // Check required top-level properties
    if (typeof responseData.success !== "boolean") {
      errors.push('Missing or invalid "success" field');
    }

    if (typeof responseData.message !== "string") {
      errors.push('Missing or invalid "message" field');
    }

    if (!responseData.data || typeof responseData.data !== "object") {
      errors.push('Missing or invalid "data" field');
    } else {
      // Check modelsInfo structure
      const modelsInfo = responseData.data.modelsInfo;
      if (!modelsInfo || typeof modelsInfo !== "object") {
        errors.push('Missing or invalid "modelsInfo" field');
      } else {
        if (typeof modelsInfo.totalModels !== "number") {
          errors.push('Missing or invalid "totalModels" field');
        }

        if (!modelsInfo.models || typeof modelsInfo.models !== "object") {
          errors.push('Missing or invalid "models" field');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check health of a specific service
   * @param {string} serviceName - Name of the service
   * @returns {Promise<Object>} Health check result
   */
  async checkServiceHealth(serviceName) {
    logger.debug(
      `ServiceApiClient.checkServiceHealth - Checking health of ${serviceName} service`
    );

    try {
      const client = this.clients.get(serviceName);
      if (!client) {
        throw new Error(`No client configured for service: ${serviceName}`);
      }

      const config = getServiceConfig(serviceName);
      const response = await client.get(config.endpoints.health, {
        timeout: 5000,
      });

      const isHealthy = response.status === 200;
      logger.debug(
        `ServiceApiClient.checkServiceHealth - ${serviceName} service health: ${
          isHealthy ? "healthy" : "unhealthy"
        }`
      );

      return {
        service: serviceName,
        healthy: isHealthy,
        status: response.status,
        responseTime: response.headers["x-response-time"] || "unknown",
      };
    } catch (error) {
      logger.warn(
        `ServiceApiClient.checkServiceHealth - ${serviceName} service health check failed: ${error.message}`
      );

      return {
        service: serviceName,
        healthy: false,
        error: error.message,
      };
    }
  }
}

// Export singleton instance
export const serviceApiClient = new ServiceApiClient();

export default serviceApiClient;
