apiVersion: v1
kind: Namespace
metadata:
  name: perfino-dashboard
  labels:
    name: perfino-dashboard

---
# infra/k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cpa-config
  namespace: perfino-dashboard
data:
  NODE_ENV: "production"
  MONGODB_URI: "mongodb://mongodb-service:27017"
  REDIS_URL: "redis://redis-service:6379"

---
# infra/k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: cpa-secrets
  namespace: perfino-dashboard
type: Opaque
stringData:
  JWT_SECRET: "your-