// Dynamic table creation service that fetches models from APIs and creates tables
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { serviceApiClient } from "../clients/service-api.client.js";
import { createTablesFromModelsInfo } from "../utils/table-builder.utils.js";
import { createServiceResponse } from "../utils/response.util.js";
import * as status from "../utils/status_code.utils.js";

const logger = createLogger(
  LOGGER_NAMES.DYNAMIC_TABLE_SERVICE || "DynamicTableService"
);

/**
 * Dynamic Table Service for creating tables from service API responses
 */
export class DynamicTableService {
  constructor() {
    this.serviceClient = serviceApiClient;
  }

  /**
   * Create tables for organization schema based on selected services
   * @param {string} schemaName - Name of the organization schema
   * @param {Array<string>} services - Array of service names
   * @returns {Promise<Object>} Service response with creation results
   */
  async createTablesForServices(schemaName, services) {
    logger.info(
      `DynamicTableService.createTablesForServices - Creating tables for services: ${services.join(
        ", "
      )} in schema: ${schemaName}`
    );

    try {
      // Validate inputs
      if (!schemaName || typeof schemaName !== "string") {
        throw new Error("Schema name is required and must be a string");
      }

      if (!Array.isArray(services) || services.length === 0) {
        throw new Error("Services array is required and must not be empty");
      }

      // Fetch models information from all services
      const modelsResponse = await this.serviceClient.fetchModelsFromServices(
        services
      );

      if (!modelsResponse.success) {
        throw new Error(
          `Failed to fetch models from services: ${modelsResponse.error}`
        );
      }

      // Process each service's models
      const serviceResults = [];
      let totalTablesCreated = 0;
      let totalTablesFailed = 0;
      let totalTablesSkipped = 0;

      for (const serviceData of modelsResponse.data.services) {
        try {
          logger.info(
            `DynamicTableService.createTablesForServices - Processing ${serviceData.serviceName} service with ${serviceData.modelsInfo.totalModels} models`
          );

          // Create tables for this service
          const tableCreationResult = await createTablesFromModelsInfo(
            schemaName,
            serviceData.modelsInfo
          );

          const serviceResult = {
            serviceName: serviceData.serviceName,
            totalModels: serviceData.modelsInfo.totalModels,
            createdTables: tableCreationResult.createdTables.length,
            skippedTables: tableCreationResult.skippedTables.length,
            failedTables: tableCreationResult.failedTables.length,
            success: tableCreationResult.success,
            tables: tableCreationResult.createdTables.map((t) => ({
              tableName: t.tableName,
              fullTableName: t.fullTableName,
              columns: t.columns?.length || 0,
              indexes: t.indexes || 0,
            })),
            errors: tableCreationResult.errors,
          };

          serviceResults.push(serviceResult);

          // Update totals
          totalTablesCreated += tableCreationResult.createdTables.length;
          totalTablesFailed += tableCreationResult.failedTables.length;
          totalTablesSkipped += tableCreationResult.skippedTables.length;

          logger.info(
            `DynamicTableService.createTablesForServices - ${serviceData.serviceName}: ${tableCreationResult.createdTables.length} created, ${tableCreationResult.skippedTables.length} skipped, ${tableCreationResult.failedTables.length} failed`
          );
        } catch (serviceError) {
          logger.error(
            `DynamicTableService.createTablesForServices - Error processing ${serviceData.serviceName}: ${serviceError.message}`
          );

          serviceResults.push({
            serviceName: serviceData.serviceName,
            totalModels: serviceData.modelsInfo?.totalModels || 0,
            createdTables: 0,
            skippedTables: 0,
            failedTables: serviceData.modelsInfo?.totalModels || 0,
            success: false,
            tables: [],
            errors: [serviceError.message],
          });

          totalTablesFailed += serviceData.modelsInfo?.totalModels || 0;
        }
      }

      // Determine overall success
      const overallSuccess = totalTablesFailed === 0;
      const hasPartialSuccess = totalTablesCreated > 0;

      // Prepare response message
      let message;
      if (overallSuccess) {
        message = `Successfully created ${totalTablesCreated} tables for ${services.length} services`;
      } else if (hasPartialSuccess) {
        message = `Partially successful: ${totalTablesCreated} tables created, ${totalTablesFailed} failed`;
      } else {
        message = `Failed to create tables: ${totalTablesFailed} tables failed`;
      }

      logger.info(
        `DynamicTableService.createTablesForServices - Completed: ${message}`
      );

      return createServiceResponse(
        overallSuccess || hasPartialSuccess,
        overallSuccess
          ? status.STATUS_CODE_CREATED
          : status.STATUS_CODE_PARTIAL_CONTENT,
        message,
        {
          schemaName,
          services: serviceResults,
          summary: {
            totalServices: services.length,
            totalTablesCreated,
            totalTablesSkipped,
            totalTablesFailed,
            overallSuccess,
            hasPartialSuccess,
          },
        }
      );
    } catch (error) {
      logger.error(
        `DynamicTableService.createTablesForServices - Error: ${error.message}`
      );

      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to create tables for services",
        null,
        error.message
      );
    }
  }

  /**
   * Create tables for a single service
   * @param {string} schemaName - Name of the organization schema
   * @param {string} serviceName - Name of the service
   * @returns {Promise<Object>} Service response with creation results
   */
  async createTablesForService(schemaName, serviceName) {
    logger.info(
      `DynamicTableService.createTablesForService - Creating tables for service: ${serviceName} in schema: ${schemaName}`
    );

    try {
      // Fetch models information from the service
      const modelsResponse = await this.serviceClient.fetchModelsInfo(
        serviceName
      );

      if (!modelsResponse.success) {
        throw new Error(
          `Failed to fetch models from ${serviceName}: ${modelsResponse.error}`
        );
      }

      // Create tables from the models
      const tableCreationResult = await createTablesFromModelsInfo(
        schemaName,
        modelsResponse.data.modelsInfo
      );

      const message = tableCreationResult.success
        ? `Successfully created ${tableCreationResult.createdTables.length} tables for ${serviceName}`
        : `Partially created tables for ${serviceName}: ${tableCreationResult.createdTables.length} created, ${tableCreationResult.failedTables.length} failed`;

      logger.info(`DynamicTableService.createTablesForService - ${message}`);

      return createServiceResponse(
        tableCreationResult.success,
        tableCreationResult.success
          ? status.STATUS_CODE_CREATED
          : status.STATUS_CODE_PARTIAL_CONTENT,
        message,
        {
          schemaName,
          serviceName,
          ...tableCreationResult,
        }
      );
    } catch (error) {
      logger.error(
        `DynamicTableService.createTablesForService - Error: ${error.message}`
      );

      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        `Failed to create tables for ${serviceName}`,
        null,
        error.message
      );
    }
  }

  /**
   * Get models information from services without creating tables
   * @param {Array<string>} services - Array of service names
   * @returns {Promise<Object>} Service response with models information
   */
  async getModelsInfoFromServices(services) {
    logger.info(
      `DynamicTableService.getModelsInfoFromServices - Fetching models info from services: ${services.join(
        ", "
      )}`
    );

    try {
      const modelsResponse = await this.serviceClient.fetchModelsFromServices(
        services
      );

      if (!modelsResponse.success) {
        throw new Error(
          `Failed to fetch models from services: ${modelsResponse.error}`
        );
      }

      // Transform the response to include summary information
      const servicesInfo = modelsResponse.data.services.map((serviceData) => ({
        serviceName: serviceData.serviceName,
        totalModels: serviceData.modelsInfo.totalModels,
        models: Object.keys(serviceData.modelsInfo.models),
        fetchedAt: serviceData.fetchedAt,
      }));

      logger.info(
        `DynamicTableService.getModelsInfoFromServices - Successfully fetched models info from ${servicesInfo.length} services`
      );

      return createServiceResponse(
        true,
        status.STATUS_CODE_OK,
        `Successfully fetched models information from ${servicesInfo.length} services`,
        {
          services: servicesInfo,
          totalServices: services.length,
          totalModels: modelsResponse.data.totalModels,
        }
      );
    } catch (error) {
      logger.error(
        `DynamicTableService.getModelsInfoFromServices - Error: ${error.message}`
      );

      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to fetch models information from services",
        null,
        error.message
      );
    }
  }

  /**
   * Check health of all configured services
   * @returns {Promise<Object>} Service response with health status
   */
  async checkServicesHealth() {
    logger.info(
      "DynamicTableService.checkServicesHealth - Checking health of all services"
    );

    try {
      const services = ["financial", "operational", "payroll"];
      const healthChecks = await Promise.allSettled(
        services.map((service) =>
          this.serviceClient.checkServiceHealth(service)
        )
      );

      const healthResults = healthChecks.map((result, index) => {
        if (result.status === "fulfilled") {
          return result.value;
        } else {
          return {
            service: services[index],
            healthy: false,
            error: result.reason?.message || "Health check failed",
          };
        }
      });

      const healthyServices = healthResults.filter((r) => r.healthy).length;
      const allHealthy = healthyServices === services.length;

      logger.info(
        `DynamicTableService.checkServicesHealth - ${healthyServices}/${services.length} services are healthy`
      );

      return createServiceResponse(
        allHealthy,
        allHealthy ? status.STATUS_CODE_OK : status.STATUS_CODE_PARTIAL_CONTENT,
        `${healthyServices}/${services.length} services are healthy`,
        {
          services: healthResults,
          totalServices: services.length,
          healthyServices,
          allHealthy,
        }
      );
    } catch (error) {
      logger.error(
        `DynamicTableService.checkServicesHealth - Error: ${error.message}`
      );

      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to check services health",
        null,
        error.message
      );
    }
  }
}

// Export singleton instance
export const dynamicTableService = new DynamicTableService();

// Export the main function for creating tables
export const createOrganizationTablesFromServices = async (
  schemaName,
  services
) => {
  return await dynamicTableService.createTablesForServices(
    schemaName,
    services
  );
};

export default dynamicTableService;
