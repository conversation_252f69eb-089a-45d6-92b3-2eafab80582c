import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";

import { SERVICE_PORTS } from "@/utils/constants/api";
import { getMonthDateRange } from "@/utils/methods/formatters";

// Sync Financial data
export const syncFinancialData = createAsyncThunk(
  "bookkeeping/syncFinancial",
  async (
    { clientId, month, files, realmId, schemaName },
    { rejectWithValue }
  ) => {
    try {
      console.log("month", month);
      const { startDate, endDate } = getMonthDateRange(month);
      // Use QUICKBOOK service port (3005)
      const axios = api(SERVICE_PORTS.QUICKBOOK);
      const response = await axios.post("/quickbooks/sync-all-reports", {
        startDate: startDate,
        endDate: endDate,
        realmId: realmId,
        organization_id: clientId,
        schemaName: schemaName,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Sync Operational data
export const syncOperationalData = createAsyncThunk(
  "bookkeeping/syncOperational",
  async ({ clientId, month, files }, { rejectWithValue }) => {
    try {
      const axios = api(SERVICE_PORTS.SIKKA);
      const response = await axios.post("/sikka/sync-all-reports", {
        office_id: "D14699",
        start_date: "2000-09-01",
        end_date: "2025-09-09",
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);
