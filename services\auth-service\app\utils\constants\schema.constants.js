// SCHEMA CONSTANTS - All schema management related messages and field names

export const SCHEMA_MESSAGES = {
  // Success Messages
  SCHEMA_CREATED_SUCCESSFULLY: 'Schema created successfully',
  SCHEMA_ALREADY_EXISTS: 'Schema already exists',
  SCHEMA_DROPPED_SUCCESSFULLY: 'Schema dropped successfully',
  TABLES_CREATED_SUCCESSFULLY: 'Tables created successfully',
  SCHEMA_AND_TABLES_CREATED_SUCCESSFULLY: 'Organization schema and tables created successfully',
  SERVICES_RETRIEVED_SUCCESSFULLY: 'Available services retrieved successfully',
  SCHEMA_INFO_RETRIEVED: 'Schema information retrieved successfully',
  
  // Error Messages
  SCHEMA_CREATION_FAILED: 'Failed to create schema',
  SCHEMA_DROP_FAILED: 'Failed to drop schema',
  TABLE_CREATION_FAILED: 'Failed to create tables',
  SCHEMA_NOT_FOUND: 'Schema not found',
  TABLE_NOT_FOUND: 'Table not found',
  INVALID_SCHEMA_NAME: 'Invalid schema name',
  SCHEMA_ALREADY_EXISTS_ERROR: 'Schema already exists',
  
  // Validation Messages
  ORGANIZATION_NAME_REQUIRED: 'Organization name is required for schema creation',
  SERVICES_REQUIRED: 'Services array is required',
  INVALID_SERVICE_NAME: 'Invalid service name provided',
  SCHEMA_NAME_TOO_LONG: 'Schema name is too long (max 63 characters)',
  SCHEMA_NAME_INVALID_FORMAT: 'Schema name contains invalid characters',
  RESERVED_SCHEMA_NAME: 'Schema name is reserved and cannot be used',
  
  // Partial Success Messages
  PARTIAL_SCHEMA_CREATION: 'Schema created but some tables failed to create',
  PARTIAL_TABLE_CREATION: 'Some tables were created successfully, others failed',
  
  // Service-Specific Messages
  FINANCIAL_TABLES_CREATED: 'Financial service tables created successfully',
  OPERATIONAL_TABLES_CREATED: 'Operational service tables created successfully',
  PMS_TABLES_CREATED: 'PMS service tables created successfully',
  
  // Warning Messages
  SCHEMA_EXISTS_WARNING: 'Schema already exists, skipping creation',
  TABLE_EXISTS_WARNING: 'Table already exists, skipping creation',
  FORCE_DROP_WARNING: 'Force dropping schema with cascade',
  
  // Info Messages
  SCHEMA_CREATION_STARTED: 'Starting schema creation process',
  TABLE_CREATION_STARTED: 'Starting table creation process',
  SCHEMA_VALIDATION_STARTED: 'Validating schema name',
  SERVICE_VALIDATION_STARTED: 'Validating service names',
};

export const SCHEMA_FIELD_NAMES = {
  // Schema Fields
  SCHEMA_NAME: 'schema_name',
  ORGANIZATION_NAME: 'organization_name',
  SERVICES: 'services',
  TABLE_NAME: 'table_name',
  MODEL_NAME: 'model_name',
  
  // Service Fields
  SERVICE_NAME: 'service_name',
  SERVICE_TYPE: 'service_type',
  SERVICE_DESCRIPTION: 'service_description',
  
  // Table Fields
  TABLE_SCHEMA: 'table_schema',
  TABLE_TYPE: 'table_type',
  COLUMN_NAME: 'column_name',
  DATA_TYPE: 'data_type',
  
  // Status Fields
  CREATION_STATUS: 'creation_status',
  EXISTS: 'exists',
  CASCADE: 'cascade',
  FORCE: 'force',
};

export const SCHEMA_VALIDATION_RULES = {
  // Schema Name Rules
  SCHEMA_NAME_MAX_LENGTH: 63,
  SCHEMA_NAME_MIN_LENGTH: 1,
  SCHEMA_NAME_PATTERN: /^[a-z][a-z0-9_]*$/,
  
  // Organization Name Rules
  ORG_NAME_MAX_LENGTH: 255,
  ORG_NAME_MIN_LENGTH: 2,
  
  // Service Rules
  MAX_SERVICES_PER_ORG: 10,
  MIN_SERVICES_PER_ORG: 1,
};

export const SCHEMA_RESERVED_NAMES = [
  'public',
  'information_schema',
  'pg_catalog',
  'pg_toast',
  'pg_temp',
  'pg_toast_temp',
  'postgres',
  'template0',
  'template1',
  'pg_stat',
  'pg_settings',
];

export const SERVICE_TYPES = {
  FINANCIAL: 'financial',
  OPERATIONAL: 'operational',
  PMS: 'pms',
};

export const SERVICE_MODELS = {
  [SERVICE_TYPES.FINANCIAL]: {
    name: 'Financial Services',
    description: 'Financial management and accounting modules',
    models: ['AccountsModel', 'TransactionsModel', 'JournalEntriesModel'],
    tables: ['accounts', 'transactions', 'journal_entries'],
  },
  [SERVICE_TYPES.OPERATIONAL]: {
    name: 'Operational Services',
    description: 'Project and resource management modules',
    models: ['ProjectsModel', 'TasksModel', 'ResourcesModel'],
    tables: ['projects', 'tasks', 'resources'],
  },
  [SERVICE_TYPES.PMS]: {
    name: 'Property Management System',
    description: 'Property and tenant management modules',
    models: ['PropertiesModel', 'TenantsModel', 'LeasesModel'],
    tables: ['properties', 'tenants', 'leases'],
  },
};

export const SCHEMA_STATUS = {
  CREATED: 'created',
  EXISTS: 'exists',
  FAILED: 'failed',
  PARTIAL: 'partial',
  DROPPED: 'dropped',
};

export const TABLE_STATUS = {
  CREATED: 'created',
  EXISTS: 'exists',
  FAILED: 'failed',
  SKIPPED: 'skipped',
};

export const SCHEMA_OPERATIONS = {
  CREATE: 'create',
  DROP: 'drop',
  LIST: 'list',
  CHECK: 'check',
  SYNC: 'sync',
};

export const SCHEMA_ERROR_CODES = {
  INVALID_NAME: 'INVALID_SCHEMA_NAME',
  ALREADY_EXISTS: 'SCHEMA_ALREADY_EXISTS',
  NOT_FOUND: 'SCHEMA_NOT_FOUND',
  CREATION_FAILED: 'SCHEMA_CREATION_FAILED',
  DROP_FAILED: 'SCHEMA_DROP_FAILED',
  INVALID_SERVICE: 'INVALID_SERVICE',
  TABLE_CREATION_FAILED: 'TABLE_CREATION_FAILED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  DATABASE_ERROR: 'DATABASE_ERROR',
};

export const SCHEMA_LOG_ACTIONS = {
  SCHEMA_CREATED: 'schemaCreated',
  SCHEMA_DROPPED: 'schemaDropped',
  TABLE_CREATED: 'tableCreated',
  TABLE_DROPPED: 'tableDropped',
  SERVICE_TABLES_CREATED: 'serviceTablesCreated',
  ORGANIZATION_SETUP: 'organizationSetup',
};

export const SCHEMA_VALIDATION_MESSAGES = {
  // Schema Name Validation
  SCHEMA_NAME_REQUIRED: 'Schema name is required',
  SCHEMA_NAME_TOO_SHORT: 'Schema name must be at least 1 character',
  SCHEMA_NAME_TOO_LONG: 'Schema name must be 63 characters or less',
  SCHEMA_NAME_INVALID_FORMAT: 'Schema name must start with a letter and contain only lowercase letters, numbers, and underscores',
  SCHEMA_NAME_RESERVED: 'Schema name is reserved and cannot be used',
  
  // Organization Name Validation
  ORG_NAME_REQUIRED: 'Organization name is required',
  ORG_NAME_TOO_SHORT: 'Organization name must be at least 2 characters',
  ORG_NAME_TOO_LONG: 'Organization name must be 255 characters or less',
  
  // Service Validation
  SERVICES_REQUIRED: 'At least one service must be specified',
  SERVICES_INVALID: 'One or more invalid services specified',
  SERVICES_TOO_MANY: 'Too many services specified (maximum 10)',
  SERVICE_NOT_SUPPORTED: 'Service is not supported',
  
  // General Validation
  INVALID_INPUT: 'Invalid input provided',
  MISSING_REQUIRED_FIELD: 'Missing required field',
};

// Default export
export default {
  SCHEMA_MESSAGES,
  SCHEMA_FIELD_NAMES,
  SCHEMA_VALIDATION_RULES,
  SCHEMA_RESERVED_NAMES,
  SERVICE_TYPES,
  SERVICE_MODELS,
  SCHEMA_STATUS,
  TABLE_STATUS,
  SCHEMA_OPERATIONS,
  SCHEMA_ERROR_CODES,
  SCHEMA_LOG_ACTIONS,
  SCHEMA_VALIDATION_MESSAGES,
};
