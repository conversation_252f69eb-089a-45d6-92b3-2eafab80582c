import axios from "axios";
import { createLogger } from "./logger.utils.js";
import { QUICKBOOKS_LOGGER_NAMES } from "./constants/config.constants.js";

const logger = createLogger(QUICKBOOKS_LOGGER_NAMES.ORGANIZATION_API);

// Base URL for organization service
export const ORG_API_BASE_URL =
  process.env.ORGANIZATION_SERVICE_URL ||
  "http://localhost:3001/api/organization";

/**
 * Generic API call method for organization service
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request data (optional)
 * @param {Object} params - Query parameters (optional)
 * @returns {Promise<Object>} API response data
 */
export const callOrganizationAPI = async (
  method,
  endpoint,
  data = null,
  params = null
) => {
  const url = `${ORG_API_BASE_URL}${endpoint}`;

  try {
    logger.info(`Calling organization API: ${method} ${url}`);

    const config = {
      method,
      url,
      headers: {
        "Content-Type": "application/json",
      },
    };

    if (data) {
      config.data = data;
    }

    if (params) {
      config.params = params;
    }

    const response = await axios(config);

    if (response.status >= 200 && response.status < 300) {
      logger.info(`Organization API call successful: ${method} ${url}`);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } else {
      logger.warn(
        `Organization API call failed with status ${response.status}: ${method} ${url}`
      );
      return {
        success: false,
        data: null,
        status: response.status,
        error: `Request failed with status ${response.status}`,
      };
    }
  } catch (error) {
    logger.error(`Organization API call error: ${method} ${url}`, {
      error: error.message,
      response: error.response?.data || null,
    });

    return {
      success: false,
      data: null,
      status: error.response?.status || 500,
      error:
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred",
    };
  }
};

/**
 * Update realm_id for an organization
 * @param {string} organizationId - Organization ID
 * @param {string} realmId - Realm ID to update
 * @returns {Promise<Object>} API response
 */
export const updateOrganizationRealmId = async (organizationId, realmId) => {
  if (!realmId || !organizationId) {
    return {
      success: false,
      data: null,
      error: "Organization ID and Realm ID are required",
    };
  }

  return await callOrganizationAPI("PUT", `/realm/${organizationId}`, {
    realm_id: realmId,
  });
};

/**
 * Check if realm_id is associated with any organization
 * @param {string} realmId - Realm ID to check
 * @returns {Promise<Object>} API response with association status
 */
export const checkRealmIdAssociation = async (realmId) => {
  if (!realmId) {
    return {
      success: false,
      data: null,
      error: "Realm ID is required",
    };
  }

  return await callOrganizationAPI(
    "GET",
    `/check-realm-association/${realmId}`
  );
};

/**
 * Fetch organization by ID
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} Organization data
 */
export const getOrganizationById = async (organizationId) => {
  if (!organizationId) {
    return {
      success: false,
      data: null,
      error: "Organization ID is required",
    };
  }

  return await callOrganizationAPI("GET", `/${organizationId}`);
};

/**
 * Update QB connection status for an organization
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} API response
 */
export const updateOrganizationQbConnection = async (organizationId) => {
  if (!organizationId) {
    return {
      success: false,
      data: null,
      error: "Organization ID is required",
    };
  }

  return await callOrganizationAPI("PUT", `/${organizationId}/qb-connect`);
};
