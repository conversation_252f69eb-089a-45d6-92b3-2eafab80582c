import { body } from "express-validator";
import { VALIDATION_MESSAGES } from "../utils/constants/validation.constants.js";
import { ORGANIZATION_FIELD_NAMES } from "../utils/constants/organization.constants.js";

// Common validation rules for organization
const organizationValidations = {
  name: body(ORGANIZATION_FIELD_NAMES.NAME)
    .notEmpty()
    .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
    .isLength({ min: 2, max: 255 })
    .withMessage("Organization name must be between 2 and 255 characters")
    .trim()
    .escape(),

  email: body(ORGANIZATION_FIELD_NAMES.EMAIL)
    .notEmpty()
    .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
    .isEmail()
    .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL)
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage("Email must be less than 255 characters"),

  phone: body(ORGANIZATION_FIELD_NAMES.PHONE)
    .optional({ nullable: true, checkFalsy: true })
    .isMobilePhone()
    .withMessage(VALIDATION_MESSAGES.INVALID_PHONE)
    .isLength({ max: 20 })
    .withMessage("Phone number must be less than 20 characters"),

  website: body(ORGANIZATION_FIELD_NAMES.WEBSITE)
    .optional({ nullable: true, checkFalsy: true })
    .isURL({
      protocols: ["http", "https"],
      require_protocol: true,
      require_valid_protocol: true,
      allow_underscores: false,
      allow_trailing_dot: false,
      allow_protocol_relative_urls: false,
    })
    .withMessage("Website must be a valid URL with http or https protocol")
    .isLength({ max: 500 })
    .withMessage("Website URL must be less than 500 characters"),

  description: body(ORGANIZATION_FIELD_NAMES.DESCRIPTION)
    .optional({ nullable: true, checkFalsy: true })
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters")
    .trim()
    .escape(),

  services: body(ORGANIZATION_FIELD_NAMES.SERVICES)
    .notEmpty()
    .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
    .isArray({ min: 1 })
    .withMessage("Services must be a non-empty array")
    .custom((services) => {
      const validServices = ["financial", "operational", "payroll"];

      // Check if all services are valid
      for (const service of services) {
        if (!validServices.includes(service)) {
          throw new Error(
            `Invalid service: ${service}. Must be one of: ${validServices.join(
              ", "
            )}`
          );
        }
      }

      // Check for duplicates
      const uniqueServices = [...new Set(services)];
      if (uniqueServices.length !== services.length) {
        throw new Error("Duplicate services are not allowed");
      }

      return true;
    }),
};

// Validation schemas for different operations
export const organizationValidationSchemas = {
  /**
   * Validation for adding a new organization
   */
  addOrganization: [
    organizationValidations.name,
    organizationValidations.email,
    organizationValidations.phone,
    organizationValidations.website,
    organizationValidations.description,
    organizationValidations.services,
  ],

  /**
   * Validation for updating an organization (placeholder for future use)
   */
  updateOrganization: [
    body("id")
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),

    // Make all fields optional for updates
    body(ORGANIZATION_FIELD_NAMES.NAME)
      .optional()
      .isLength({ min: 2, max: 255 })
      .withMessage("Organization name must be between 2 and 255 characters")
      .trim()
      .escape(),

    body(ORGANIZATION_FIELD_NAMES.EMAIL)
      .optional()
      .isEmail()
      .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL)
      .normalizeEmail()
      .isLength({ max: 255 })
      .withMessage("Email must be less than 255 characters"),

    organizationValidations.phone,
    organizationValidations.website,
    organizationValidations.description,

    body(ORGANIZATION_FIELD_NAMES.SERVICES)
      .optional()
      .isArray({ min: 1 })
      .withMessage("Services must be a non-empty array")
      .custom((services) => {
        const validServices = ["financial", "operational", "payroll"];

        for (const service of services) {
          if (!validServices.includes(service)) {
            throw new Error(
              `Invalid service: ${service}. Must be one of: ${validServices.join(
                ", "
              )}`
            );
          }
        }

        const uniqueServices = [...new Set(services)];
        if (uniqueServices.length !== services.length) {
          throw new Error("Duplicate services are not allowed");
        }

        return true;
      }),
  ],

  /**
   * Validation for getting organization by ID
   */
  getOrganizationById: [
    body("id")
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  /**
   * Validation for updating sync fields
   */
  updateSyncFields: [
    body("id")
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),

    body("type")
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isIn(["qb", "sikka", "adp"])
      .withMessage("Type must be one of: qb, sikka, adp"),
  ],

  /**
   * Validation for updating realm_id
   */
  updateRealmId: [
    body("realm_id")
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isString()
      .withMessage("Realm ID must be a string")
      .isLength({ min: 1, max: 255 })
      .withMessage("Realm ID must be between 1 and 255 characters")
      .trim(),
  ],
};

// Export individual validation arrays for easier use
export const {
  addOrganization,
  updateOrganization,
  getOrganizationById,
  updateSyncFields,
  updateRealmId,
} = organizationValidationSchemas;

// Default export
export default organizationValidationSchemas;
