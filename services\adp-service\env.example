# Server Configuration
NODE_ENV=development
ADP_SERVICE_PORT=3006
LOG_LEVEL=info

# Database Configuration
DB_HOST=db_host
DB_PORT=5432
DB_NAME=postgres
DB_PASS=db_password
DB_USER=postgres
DB_DIALECT=postgres
DB_SSL=true

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://19rdf6z7-3000.inc1.devtunnels.ms,https://19rdf6z7-3000.inc1.devtunnels.ms/


# ADP API Configuration (for future use)
ADP_CLIENT_ID=your_adp_client_id
ADP_CLIENT_SECRET=your_adp_client_secret
ADP_API_BASE_URL=https://api.adp.com
ADP_API_VERSION=v1

# Logging Configuration
LOG_TO_FILE=true
LOG_TO_CONSOLE=true

# Common
NODE_ENV=production
