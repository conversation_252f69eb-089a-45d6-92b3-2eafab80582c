"use client";

import { useEffect, useCallback, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import PropTypes from "prop-types";
import { Loader } from "@/components/ui/loading";
import tokenStorage from "@/lib/tokenStorage.js";
import { useToast } from "../ui/toast.jsx";
import { ROLE_CONSTANTS } from "@/utils/constants/role.js";

/**
 * Enhanced ProtectedRoute component with better SSR support
 * Handles both client-side and SSR authentication scenarios
 */
export default function ProtectedRoute({
  children,
  requiredRole = null,
  fallbackComponent = null,
  redirectTo = null,
}) {
  const { user, loading } = useSelector((state) => state.auth);
  const [isHydrated, setIsHydrated] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);
  const router = useRouter();
  const { addToast } = useToast();
  const toastShownRef = useRef({ unauth: false, norole: false });

  // Get user role with fallback to token data
  const getUserRole = useCallback(() => {
    // Try Redux state first
    if (user?.role) return user.role;

    // Fallback to token storage
    const tokenUser = tokenStorage.getUserDataFromToken();
    return tokenUser?.role || null;
  }, [user]);

  // Get redirect URL based on user role
  const getRedirectUrl = useCallback(
    (userRole) => {
      if (redirectTo) return redirectTo;

      if (userRole === ROLE_CONSTANTS.ROLE_TYPES.ADMIN) {
        return "/listing";
      }
      return "/dashboard";
    },
    [redirectTo]
  );

  // Check authentication status
  const checkAuthentication = useCallback(() => {
    // Check token storage for authentication
    const hasValidToken =
      tokenStorage.isAuthenticated() && !tokenStorage.isAccessTokenExpired();

    if (!hasValidToken) {
      return { isAuthenticated: false, user: null };
    }

    // Get user data from token or Redux state
    const currentUser = user || tokenStorage.getUserDataFromToken();

    return {
      isAuthenticated: true,
      user: currentUser,
    };
  }, [user]);

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Main authentication effect
  useEffect(() => {
    if (!isHydrated || loading) return;

    const { isAuthenticated } = checkAuthentication();

    if (!isAuthenticated) {
      if (!toastShownRef.current.unauth) {
        router.push("/login");
        toastShownRef.current.unauth = true;
      }
      return;
    }

    if (requiredRole) {
      const userRole = getUserRole();
      if (userRole !== requiredRole) {
        if (!toastShownRef.current.norole) {
          addToast(
            "You do not have sufficient permissions to view this page.",
            "error"
          );
          const redirectUrl = getRedirectUrl(userRole);
          router.push(redirectUrl);
          toastShownRef.current.norole = true;
        }
        return;
      }
    }

    setAuthChecked(true);
  }, [
    isHydrated,
    loading,
    user,
    requiredRole,
    router,
    getUserRole,
    getRedirectUrl,
    checkAuthentication,
    addToast,
  ]);

  // Show loading states
  if (!isHydrated) {
    return <Loader text="Initializing..." />;
  }

  if (loading) {
    return <Loader text="Checking authentication..." />;
  }

  // Check authentication after hydration
  const { isAuthenticated } = checkAuthentication();

  if (!isAuthenticated) {
    return fallbackComponent || <Loader text="Redirecting to login..." />;
  }

  // Check role authorization
  if (requiredRole) {
    const userRole = getUserRole();
    if (userRole !== requiredRole) {
      return fallbackComponent || <Loader text="Redirecting..." />;
    }
  }

  // Only render children if authentication is fully checked
  if (!authChecked) {
    return <Loader text="Verifying access..." />;
  }

  return children;
}

// PropTypes validation
ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  requiredRole: PropTypes.string,
  fallbackComponent: PropTypes.node,
  redirectTo: PropTypes.string,
};
