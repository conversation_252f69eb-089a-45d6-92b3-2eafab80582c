import { createLogger } from '../utils/logger.utils.js';
import { LOGGER_NAMES } from '../utils/constants/log.constants.js';
import sequelize from '../../config/postgres.config.js';
import {
  createSchema,
  checkSchemaExists,
  dropSchema,
  listSchemas,
  getOrganizationSchemaName,
} from '../utils/schema.utils.js';

const logger = createLogger(LOGGER_NAMES.SCHEMA_REPOSITORY);

export const schemaRepository = {
  /**
   * Create a new schema for an organization
   * @param {string} organizationName - The organization name
   * @returns {Promise<Object>} Schema creation result
   */
  async createOrganizationSchema(organizationName) {
    logger.info(`schemaRepository.createOrganizationSchema - Creating schema for: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      
      // Check if schema already exists
      const exists = await checkSchemaExists(schemaName);
      if (exists) {
        logger.warn(`schemaRepository.createOrganizationSchema - Schema already exists: ${schemaName}`);
        return {
          success: true,
          schemaName,
          existed: true,
          message: 'Schema already exists',
        };
      }

      // Create the schema
      await createSchema(schemaName);
      
      logger.info(`schemaRepository.createOrganizationSchema - Schema created successfully: ${schemaName}`);
      return {
        success: true,
        schemaName,
        existed: false,
        message: 'Schema created successfully',
      };

    } catch (error) {
      logger.error(`schemaRepository.createOrganizationSchema - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Check if organization schema exists
   * @param {string} organizationName - The organization name
   * @returns {Promise<Object>} Schema existence result
   */
  async checkOrganizationSchemaExists(organizationName) {
    logger.debug(`schemaRepository.checkOrganizationSchemaExists - Checking schema for: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      const exists = await checkSchemaExists(schemaName);
      
      logger.debug(`schemaRepository.checkOrganizationSchemaExists - Schema ${schemaName} exists: ${exists}`);
      return {
        exists,
        schemaName,
        organizationName,
      };

    } catch (error) {
      logger.error(`schemaRepository.checkOrganizationSchemaExists - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Get organization schema name
   * @param {string} organizationName - The organization name
   * @returns {Promise<string>} The schema name
   */
  async getOrganizationSchemaName(organizationName) {
    logger.debug(`schemaRepository.getOrganizationSchemaName - Getting schema name for: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      logger.debug(`schemaRepository.getOrganizationSchemaName - Schema name: ${schemaName}`);
      return schemaName;

    } catch (error) {
      logger.error(`schemaRepository.getOrganizationSchemaName - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * List all organization schemas
   * @returns {Promise<Array>} Array of organization schema names
   */
  async listOrganizationSchemas() {
    logger.debug('schemaRepository.listOrganizationSchemas - Listing organization schemas');
    
    try {
      const allSchemas = await listSchemas();
      
      // Filter out system schemas and only return organization schemas
      const organizationSchemas = allSchemas.filter(schema => {
        // Exclude system schemas
        const systemSchemas = ['public', 'pg_temp', 'pg_toast'];
        return !systemSchemas.some(sysSchema => schema.startsWith(sysSchema));
      });

      logger.debug(`schemaRepository.listOrganizationSchemas - Found ${organizationSchemas.length} organization schemas`);
      return organizationSchemas;

    } catch (error) {
      logger.error(`schemaRepository.listOrganizationSchemas - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Get tables in an organization schema
   * @param {string} organizationName - The organization name
   * @returns {Promise<Array>} Array of table information
   */
  async getOrganizationSchemaTables(organizationName) {
    logger.debug(`schemaRepository.getOrganizationSchemaTables - Getting tables for: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      
      const query = `
        SELECT 
          table_name,
          table_type,
          table_schema
        FROM information_schema.tables 
        WHERE table_schema = :schemaName
        ORDER BY table_name
      `;
      
      const tables = await sequelize.query(query, {
        replacements: { schemaName },
        type: sequelize.QueryTypes.SELECT
      });

      logger.debug(`schemaRepository.getOrganizationSchemaTables - Found ${tables.length} tables in schema: ${schemaName}`);
      return tables;

    } catch (error) {
      logger.error(`schemaRepository.getOrganizationSchemaTables - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Get table columns in an organization schema
   * @param {string} organizationName - The organization name
   * @param {string} tableName - The table name
   * @returns {Promise<Array>} Array of column information
   */
  async getOrganizationTableColumns(organizationName, tableName) {
    logger.debug(`schemaRepository.getOrganizationTableColumns - Getting columns for table: ${tableName} in org: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      
      const query = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length,
          numeric_precision,
          numeric_scale
        FROM information_schema.columns 
        WHERE table_schema = :schemaName 
          AND table_name = :tableName
        ORDER BY ordinal_position
      `;
      
      const columns = await sequelize.query(query, {
        replacements: { schemaName, tableName },
        type: sequelize.QueryTypes.SELECT
      });

      logger.debug(`schemaRepository.getOrganizationTableColumns - Found ${columns.length} columns in table: ${tableName}`);
      return columns;

    } catch (error) {
      logger.error(`schemaRepository.getOrganizationTableColumns - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Drop organization schema (use with extreme caution)
   * @param {string} organizationName - The organization name
   * @param {boolean} cascade - Whether to cascade the drop
   * @returns {Promise<Object>} Drop result
   */
  async dropOrganizationSchema(organizationName, cascade = false) {
    logger.warn(`schemaRepository.dropOrganizationSchema - Dropping schema for: ${organizationName} (cascade: ${cascade})`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      
      // Check if schema exists
      const exists = await checkSchemaExists(schemaName);
      if (!exists) {
        logger.warn(`schemaRepository.dropOrganizationSchema - Schema does not exist: ${schemaName}`);
        return {
          success: true,
          schemaName,
          existed: false,
          message: 'Schema did not exist',
        };
      }

      // Drop the schema
      await dropSchema(schemaName, cascade);
      
      logger.info(`schemaRepository.dropOrganizationSchema - Schema dropped successfully: ${schemaName}`);
      return {
        success: true,
        schemaName,
        existed: true,
        message: 'Schema dropped successfully',
      };

    } catch (error) {
      logger.error(`schemaRepository.dropOrganizationSchema - Error: ${error.message}`);
      throw error;
    }
  },

  /**
   * Execute a query in an organization schema
   * @param {string} organizationName - The organization name
   * @param {string} query - The SQL query to execute
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Query results
   */
  async executeQueryInOrganizationSchema(organizationName, query, options = {}) {
    logger.debug(`schemaRepository.executeQueryInOrganizationSchema - Executing query in org: ${organizationName}`);
    
    try {
      const schemaName = getOrganizationSchemaName(organizationName);
      
      // Set the search path to include the organization schema
      await sequelize.query(`SET search_path TO "${schemaName}", public`);
      
      // Execute the query
      const results = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        ...options
      });

      // Reset search path
      await sequelize.query('SET search_path TO public');

      logger.debug(`schemaRepository.executeQueryInOrganizationSchema - Query executed successfully`);
      return results;

    } catch (error) {
      // Reset search path on error
      try {
        await sequelize.query('SET search_path TO public');
      } catch (resetError) {
        logger.error(`schemaRepository.executeQueryInOrganizationSchema - Error resetting search path: ${resetError.message}`);
      }
      
      logger.error(`schemaRepository.executeQueryInOrganizationSchema - Error: ${error.message}`);
      throw error;
    }
  },
};
