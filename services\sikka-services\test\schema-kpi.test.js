import { describe, it, beforeEach, afterEach } from 'mocha';
import { expect } from 'chai';
import sinon from 'sinon';
import * as kpisController from '../app/controllers/kpis.controller.js';
import * as kpisService from '../app/services/kpis.services.js';
import * as schemaUtils from '../app/utils/schema.util.js';

describe('Schema-based KPI Controllers', () => {
  let req, res, sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    req = {
      body: {
        office_id: 'test_office_123',
        organization_name: 'Test Organization',
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      }
    };

    res = {
      status: sandbox.stub().returnsThis(),
      json: sandbox.stub().returnsThis()
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('accountReceivables controller', () => {
    it('should extract organization_name from request body and pass to service', async () => {
      // Arrange
      const mockResponse = [{ id: 1, amount: '1000' }];
      const serviceStub = sandbox.stub(kpisService, 'fetchAccountReceivables').resolves(mockResponse);

      // Act
      await kpisController.accountReceivables(req, res);

      // Assert
      expect(serviceStub.calledOnce).to.be.true;
      expect(serviceStub.calledWith('test_office_123', 'Test Organization')).to.be.true;
      expect(res.status.calledWith(200)).to.be.true;
    });

    it('should handle missing organization_name gracefully', async () => {
      // Arrange
      req.body.organization_name = undefined;
      const mockResponse = [{ id: 1, amount: '1000' }];
      const serviceStub = sandbox.stub(kpisService, 'fetchAccountReceivables').resolves(mockResponse);

      // Act
      await kpisController.accountReceivables(req, res);

      // Assert
      expect(serviceStub.calledOnce).to.be.true;
      expect(serviceStub.calledWith('test_office_123', undefined)).to.be.true;
    });
  });

  describe('treatmentAnalysis controller', () => {
    it('should extract organization_name and pass all parameters to service', async () => {
      // Arrange
      const mockResponse = [{ id: 1, practice_id: 'test', value: '500' }];
      const serviceStub = sandbox.stub(kpisService, 'fetchTreatmentAnalysis').resolves(mockResponse);

      // Act
      await kpisController.treatmentAnalysis(req, res);

      // Assert
      expect(serviceStub.calledOnce).to.be.true;
      expect(serviceStub.calledWith(
        'test_office_123',
        '2024-01-01',
        '2024-01-31',
        'Test Organization'
      )).to.be.true;
    });
  });

  describe('directRestorations controller', () => {
    it('should extract organization_name and pass all parameters to service', async () => {
      // Arrange
      const mockResponse = [{ id: 1, practice_id: 'test', type: 'restoration' }];
      const serviceStub = sandbox.stub(kpisService, 'directRestorations').resolves(mockResponse);

      // Act
      await kpisController.directRestorations(req, res);

      // Assert
      expect(serviceStub.calledOnce).to.be.true;
      expect(serviceStub.calledWith(
        'test_office_123',
        '2024-01-01',
        '2024-01-31',
        'Test Organization'
      )).to.be.true;
    });
  });
});

describe('Schema-based KPI Services', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('fetchAccountReceivables service', () => {
    it('should use schema-aware repository when organization_name is provided', async () => {
      // This test would require mocking the entire chain
      // For now, we'll test the logic flow
      const organizationName = 'Test Organization';
      
      // The service should select the correct repository function
      // based on whether organizationName is provided
      expect(organizationName).to.not.be.null;
    });
  });
});

describe('Schema Utilities', () => {
  describe('convertToSnakeCase', () => {
    it('should convert organization name to valid schema name', () => {
      const result = schemaUtils.convertToSnakeCase('Test Organization Inc.');
      expect(result).to.equal('test_organization_inc');
    });

    it('should handle special characters and spaces', () => {
      const result = schemaUtils.convertToSnakeCase('ABC Corp & Co. (2024)');
      expect(result).to.equal('abc_corp_co_2024');
    });

    it('should handle names starting with numbers', () => {
      const result = schemaUtils.convertToSnakeCase('123 Medical Group');
      expect(result).to.equal('org_123_medical_group');
    });
  });

  describe('validateSchemaName', () => {
    it('should validate correct schema names', () => {
      expect(() => schemaUtils.validateSchemaName('test_org')).to.not.throw();
      expect(() => schemaUtils.validateSchemaName('medical_group_123')).to.not.throw();
    });

    it('should reject invalid schema names', () => {
      expect(() => schemaUtils.validateSchemaName('123invalid')).to.throw();
      expect(() => schemaUtils.validateSchemaName('Test-Org')).to.throw();
      expect(() => schemaUtils.validateSchemaName('public')).to.throw();
    });
  });

  describe('getOrganizationSchemaName', () => {
    it('should convert and validate organization name to schema name', () => {
      const result = schemaUtils.getOrganizationSchemaName('Test Medical Group');
      expect(result).to.equal('test_medical_group');
    });
  });
});
