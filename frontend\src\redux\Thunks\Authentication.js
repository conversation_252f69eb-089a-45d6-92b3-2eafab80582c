import { createAsyncThunk } from "@reduxjs/toolkit";
import tokenStorage from "@/lib/tokenStorage";
import { logoutSuccess } from "../Slice/Authentication";
import api from "@/redux/ApiService/ApiService";

import { AUTH_CONSTANTS } from "@/utils/constants/auth";
import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.AUTH);

// 🔹 Login
export const login = createAsyncThunk(
  "auth/login",
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await axios.post("/auth/login", credentials);
      const { user, tokens } = response.data.data;
      const { access_token, refresh_token } = tokens;

      // Store tokens and user data
      tokenStorage.setAuthData({
        accessToken: access_token,
        refreshToken: refresh_token,
        user,
      });
      return user;
    } catch (error) {
      let errorMessage = AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 400:
            errorMessage = data.message || AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
            break;
          case 401:
            errorMessage = data.message || AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
            break;
          case 403:
            errorMessage =
              data.message || AUTH_CONSTANTS.ERRORS.ACCOUNT_DEACTIVATED;
            break;
          case 429:
            errorMessage =
              data.message || AUTH_CONSTANTS.ERRORS.TOO_MANY_ATTEMPTS;
            break;
          case 500:
            errorMessage = AUTH_CONSTANTS.ERRORS.SERVER_ERROR;
            break;
          default:
            errorMessage = data.message || AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
        }
      } else if (error.request) {
        errorMessage = AUTH_CONSTANTS.ERRORS.NETWORK_ERROR;
      } else {
        errorMessage = error.message || AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
      }
      return rejectWithValue(errorMessage);
    }
  }
);

// 🔹 Refresh token
export const refreshAuth = createAsyncThunk(
  "auth/refresh",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.post("/auth/refresh-token");

      // Handle different response formats
      let accessToken, refreshToken, user;

      if (response.data && response.data.data) {
        // If response has nested data structure
        const { user: userData, tokens } = response.data.data;
        user = userData;
        accessToken = tokens?.access_token;
        refreshToken = tokens?.refresh_token;
      } else {
        // If response is direct
        const { access_token, refresh_token, user: userData } = response.data;
        accessToken = access_token;
        refreshToken = refresh_token;
        user = userData;
      }

      tokenStorage.setAuthData({
        accessToken,
        refreshToken,
        user,
      });
      return user;
    } catch (error) {
      console.error("Refresh token error:", error);
      tokenStorage.clearAuthData();
      return rejectWithValue(AUTH_CONSTANTS.ERRORS.SESSION_EXPIRED);
    }
  }
);

// 🔹 Get Profile
export const getProfile = createAsyncThunk(
  "auth/getProfile",
  async (_, { rejectWithValue }) => {
    try {
      return await axios.get("/auth/me");
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message ||
          AUTH_CONSTANTS.ERRORS.FAILED_TO_LOAD_PROFILE
      );
    }
  }
);

// 🔹 Update Profile
export const updateProfile = createAsyncThunk(
  "auth/updateProfile",
  async ({ userId, userData }, { rejectWithValue }) => {
    try {
      return await axios.put(`/auth/profile/${userId}`, userData);
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message || AUTH_CONSTANTS.ERRORS.UPDATE_FAILED
      );
    }
  }
);

// 🔹 Change Password
export const changePassword = createAsyncThunk(
  "auth/changePassword",
  async (
    { oldPassword, newPassword, confirmPassword },
    { rejectWithValue }
  ) => {
    try {
      return await axios.put("/auth/change-password", {
        oldPassword,
        newPassword,
        confirmPassword,
      });
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message ||
          AUTH_CONSTANTS.ERRORS.PASSWORD_CHANGE_FAILED
      );
    }
  }
);

// 🔹 Forgot Password
export const forgotPassword = createAsyncThunk(
  "auth/forgotPassword",
  async ({ email, url }, { rejectWithValue }) => {
    try {
      return await axios.post("/auth/forgot-password", { email, url });
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message ||
          AUTH_CONSTANTS.ERRORS.FAILED_TO_SEND_RESET_EMAIL
      );
    }
  }
);

// 🔹 Reset Password
export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async ({ token, newPassword, confirmPassword }, { rejectWithValue }) => {
    try {
      return await axios.post("/auth/reset-password", {
        token,
        newPassword,
        confirmPassword,
      });
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message ||
          AUTH_CONSTANTS.ERRORS.PASSWORD_RESET_FAILED
      );
    }
  }
);

// 🔹 Logout
export const logout = createAsyncThunk(
  "auth/logout",
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const storedUser = tokenStorage.getUserData();
      const tokenUser = tokenStorage.getUserDataFromToken?.();
      const userId = storedUser?.id || tokenUser?.id;

      await axios.post("/auth/logout", { userId });

      // Clear client auth on successful logout
      tokenStorage.clearAuthData();
      dispatch(logoutSuccess());
      return true;
    } catch (err) {
      // Even if API fails, clear client auth to ensure logout UX
      tokenStorage.clearAuthData();
      dispatch(logoutSuccess());
      return rejectWithValue(
        err?.response?.data?.message || AUTH_CONSTANTS.ERRORS.LOGOUT_FAILED
      );
    }
  }
);
