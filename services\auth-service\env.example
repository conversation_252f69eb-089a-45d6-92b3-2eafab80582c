﻿# AUTH SERVICE ENVIRONMENT CONFIGURATION EXAMPLE
# Copy this file to .env and update the values
 
# SERVER CONFIGURATION
NODE_ENV=development
AUTH_SERVICE_PORT=3001
 
# CLIENT CONFIGURATION
USER_SERVICE_URL=http://localhost:3003/api/users
COOKIE_DOMAIN=localhost
ALLOWED_ORIGINS=http://localhost:3000,https://19rdf6z7-3000.inc1.devtunnels.ms

# Database Configuration
DB_HOST=db_host
DB_PORT=5432
DB_NAME=postgres
DB_PASS=db_password
DB_USER=postgres
DB_DIALECT=postgres
DB_SSL=true
 
# Local Database (Development)
USE_LOCAL_DB=false
LOCAL_DB_NAME=cpa_dashboard_local
LOCAL_DB_USER=postgres
LOCAL_DB_PASS=password
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=5432
 
# AUTHENTICATION & SECURITY
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_SECRET=your-super-secret-access-key-change-this-in-production
JWT_RESET_SECRET=your-super-secret-reset-key-change-this-in-production
JWT_EXPIRATION=15m
REFRESH_TOKEN_EXPIRATION=7d
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
 
# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
 
# Password Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=128
 
# COOKIE CONFIGURATION
COOKIE_MAX_AGE=3600000
COOKIE_DOMAIN=localhost
 
# LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_FORMAT=YYYY-MM-DD HH:mm:ss
LOG_FILE_PATH=logs/auth-service.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14
LOG_ENABLE_CONSOLE=true
 