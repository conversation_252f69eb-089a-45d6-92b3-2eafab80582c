// app/controllers/document.controller.js
import logger from "../../config/logger.config.js";
import { DOCUMENT_MESSAGES, DOCUMENT_LOG_MESSAGES } from "../utils/constants/document.constants.js";
import {
  storeDocumentService,
  getDocumentsService,
} from "../services/document.service.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { errorHandler } from "../utils/error.utils.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "../utils/status_code.utils.js";

/**
 * Store document metadata
 * @route POST /api/document
 * @access Protected (API Key required)
 */
export const storeDocument = async (req, res) => {
  logger.info(DOCUMENT_LOG_MESSAGES.CONTROLLER_START_STORAGE);
  
  try {
    const {
      organization_id,
      blob_storage_path,
      service,
      month,
      year,
      file_name,
      file_size,
      mime_type,
      metadata,
    } = req.body;

    // Validate required fields
    if (!organization_id || !blob_storage_path || !service || !month || !year) {
      logger.warn(DOCUMENT_LOG_MESSAGES.CONTROLLER_FIELD_VALIDATION);
      return res.status(STATUS_CODE_BAD_REQUEST).json(
        errorResponse(DOCUMENT_MESSAGES.MISSING_REQUIRED_FIELDS)
      );
    }

    // Call service to store document
    const serviceResponse = await storeDocumentService({
      organization_id,
      blob_storage_path,
      service,
      month,
      year,
      file_name,
      file_size,
      mime_type,
      metadata,
    });

    // Handle service response
    if (serviceResponse.success) {
      logger.info(`${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_SUCCESS}: ${serviceResponse.data.id}`);
      return res
        .status(serviceResponse.statusCode)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_FAILED}: ${serviceResponse.message}`);
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(`${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_UNEXPECTED_ERROR}: ${error.message}`);
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || DOCUMENT_MESSAGES.STORAGE_FAILED,
          handledError.details || error.message
        )
      );
  }
};

/**
 * Get documents
 * @route GET /api/document
 * @access Protected (API Key required)
 */
export const getDocuments = async (req, res) => {
  logger.info(DOCUMENT_LOG_MESSAGES.CONTROLLER_GET_REQUEST);
  
  try {
    const { organization_id, service, month, year } = req.query;

    if (!organization_id) {
      return res.status(STATUS_CODE_BAD_REQUEST).json(
        errorResponse(DOCUMENT_MESSAGES.MISSING_ORGANIZATION_ID)
      );
    }

    const filters = {};
    if (service) filters.service = service;
    if (month) filters.month = parseInt(month);
    if (year) filters.year = parseInt(year);

    const serviceResponse = await getDocumentsService(organization_id, filters);

    if (serviceResponse.success) {
      return res
        .status(serviceResponse.statusCode)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(`${DOCUMENT_LOG_MESSAGES.CONTROLLER_GET_ERROR}: ${error.message}`);
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(DOCUMENT_MESSAGES.FETCH_FAILED, error.message));
  }
};

