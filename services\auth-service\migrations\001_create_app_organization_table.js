"use strict";

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface
      .createTable(
        "app_organization",
        {
          id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV4,
            primaryKey: true,
          },
          name: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          email: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          phone: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          website: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          description: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          services: {
            type: Sequelize.ARRAY(
              Sequelize.ENUM("financial", "operational", "payroll")
            ),
            allowNull: false,
            defaultValue: [],
          },
          is_active: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
          },
          schema_name: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          is_deleted: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },
          created_by: {
            type: Sequelize.UUID,
            allowNull: true,
          },
          updated_at: {
            type: Sequelize.DATE,
          },
          updated_by: {
            type: Sequelize.UUID,
            allowNull: true,
          },
          office_id: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          realm_id: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
        },
        {
          schema: "Authentication",
          timestamps: true,
          createdAt: "created_at",
          updatedAt: "updated_at",
        }
      )
      .then(function () {
        // Create indexes
        return queryInterface.addIndex(
          "app_organization",
          {
            name: "idx_organization_email",
            fields: ["email"],
            unique: true,
            where: {
              is_deleted: false,
            },
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_organization",
          {
            name: "idx_organization_name",
            fields: ["name"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_organization",
          {
            name: "idx_organization_services",
            fields: ["services"],
            using: "gin",
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_organization",
          {
            name: "idx_organization_created_at",
            fields: ["created_at"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_organization",
          {
            name: "idx_organization_is_deleted",
            fields: ["is_deleted"],
          },
          {
            schema: "Authentication",
          }
        );
      });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable("app_organization", {
      schema: "Authentication",
    });
  },
};
