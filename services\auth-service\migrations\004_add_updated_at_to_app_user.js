"use strict";

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.addColumn(
      "app_user",
      "updated_at",
      {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: true,
      },
      {
        schema: "Authentication",
      }
    );
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.removeColumn("app_user", "updated_at", {
      schema: "Authentication",
    });
  },
};
