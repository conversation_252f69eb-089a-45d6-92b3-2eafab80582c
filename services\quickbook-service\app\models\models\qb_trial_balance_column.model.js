import { DataTypes } from "sequelize";

const QbTrialBalanceColumnModel = (sequelize) => {
  const TrialBalanceColumn = sequelize.define(
    "TrialBalanceColumn",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "qb_trial_balance_reports",
          key: "id",
        },
      },
      col_title: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      col_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      col_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      parent_col_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "qb_trial_balance_columns",
          key: "id",
        },
      },
      parent_col_title: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      col_order: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      period_start: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      period_end: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "qb_trial_balance_columns",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );
  return TrialBalanceColumn;
};

export default QbTrialBalanceColumnModel;
