// server.js
import express from "express";
import cors from "cors";
import morgan from "morgan";
import dotenv from "dotenv";
import fileRoutes from "./app/routes/file.routes.js";
import documentRoutes from "./app/routes/document.routes.js";
import { errorHandler, notFoundHandler } from "./app/middleware/errorHandler.middleware.js";
import { corsOptions } from "./config/cors.config.js";
import logger from "./config/logger.config.js";
import blobStorageService from "./app/services/blobStorage.service.js";
import { sequelize } from "./app/models/index.js";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware setup
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging with Morgan
app.use(morgan("combined", {
  stream: {
    write: (message) => logger.info(message.trim()),
  },
}));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    success: true,
    service: "File Storage Service",
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
});

// Root endpoint
app.get("/", (req, res) => {
  res.status(200).json({
    success: true,
    service: "File Storage Service",
    message: "Welcome to the File Storage API",
    version: "1.0.0",
    endpoints: {
      health: "/health",
      upload: "POST /api/files/upload",
      list: "GET /api/files/list",
      download: "GET /api/files/:filePath",
      delete: "DELETE /api/files/:filePath",
      metadata: "GET /api/files/metadata/:filePath",
      storeDocument: "POST /api/document",
      getDocuments: "GET /api/document",
    },
  });
});

// API routes
app.use("/api/files", fileRoutes);
app.use("/api/document", documentRoutes);

// Error handling middleware (must be last)
app.use(notFoundHandler);
app.use(errorHandler);

// Initialize blob storage service (optional)
try {
  // This will validate configuration and ensure container exists
  await blobStorageService.ensureContainer();
  logger.info("Blob storage service initialized successfully");
} catch (error) {
  logger.warn("Failed to initialize blob storage service:", error.message);
  logger.warn("File upload/download features will not be available, but document metadata API will work");
}

// Initialize database connection
try {
  await sequelize.authenticate();
  logger.info("Database connection established successfully");
} catch (error) {
  logger.error("Failed to connect to database:", error);
  logger.error("Document metadata features will not function properly without database connection");
}

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 File Storage Service running on port ${PORT}`);
  logger.info(`📂 Environment: ${process.env.NODE_ENV || "development"}`);
  logger.info(`🌐 CORS Origin: ${corsOptions.origin}`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM signal received: closing HTTP server");
  process.exit(0);
});

process.on("SIGINT", () => {
  logger.info("SIGINT signal received: closing HTTP server");
  process.exit(0);
});

export default app;

