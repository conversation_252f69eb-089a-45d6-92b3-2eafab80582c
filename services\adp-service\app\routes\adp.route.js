import express from "express";
import multer from "multer";
import * as adpController from "../controllers/adp.controller.js";

const router = express.Router();

// Multer in-memory storage (no file saved to disk)
const storage = multer.memoryStorage();
const upload = multer({ storage });

// Route configuration for better maintainability
const ROUTE_CONFIG = {
  SYNC_DATA: {
    path: "/sync",
    method: "POST",
    handler: adpController.syncData,
    middleware: upload.single("file"),
  },
};

Object.values(ROUTE_CONFIG).forEach(({ path, method, handler, middleware }) => {
  if (middleware) {
    router[method.toLowerCase()](path, middleware, handler);
  } else {
    router[method.toLowerCase()](path, handler);
  }
});

export default router;
