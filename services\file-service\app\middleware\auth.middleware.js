// app/middleware/auth.middleware.js
import logger from "../../config/logger.config.js";

/**
 * API Key authentication middleware
 * Validates x-api-key header
 */
export const authMiddleware = async (req, res, next) => {
  try {
    // Get API key from header
    const apiKey = req.headers["x-api-key"];

    if (!apiKey) {
      logger.warn("API key missing from request");
      return res.status(401).json({
        success: false,
        error: "Unauthorized",
        message: "x-api-key header is required",
      });
    }

    // Get the system API key from environment variables
    const systemApiKey = process.env.SYSTEM_API_KEY;

    if (!systemApiKey) {
      logger.error("System API key not configured in environment variables");
      return res.status(500).json({
        success: false,
        error: "Server configuration error",
        message: "API key verification service is not configured",
      });
    }

    // Verify the API key
    if (apiKey !== systemApiKey) {
      logger.warn("Invalid API key provided");
      return res.status(401).json({
        success: false,
        error: "Unauthorized",
        message: "Invalid API key",
      });
    }

    logger.info("API key verified successfully");
    next();
  } catch (error) {
    logger.error("Auth middleware error:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
      message: "API key verification failed",
    });
  }
};

