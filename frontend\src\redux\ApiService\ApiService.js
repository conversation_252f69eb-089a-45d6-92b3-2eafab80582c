import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";

// Factory to create an Axios API client with base URL and interceptors
export const api = (baseURL) => {
  const instance = axios.create({
    baseURL: baseURL,
    withCredentials: true,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      } else {
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for handling errors
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401 || error.response?.status === 403) {
        tokenStorage.clearAuthData();
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

export default api;
