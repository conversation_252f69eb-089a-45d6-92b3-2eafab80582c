import { DataTypes } from "sequelize";

const QbTrialBalanceReportModel = (sequelize) => {
  const TrialBalanceReport = sequelize.define(
    "TrialBalanceReport",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      report_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      report_basis: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      start_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      summarize_columns_by: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      currency: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "qb_trial_balance_reports",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );
  return TrialBalanceReport;
};

export default QbTrialBalanceReportModel;
