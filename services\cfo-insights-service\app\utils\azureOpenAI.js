// app/utils/azureOpenAI.js
import dotenv from "dotenv";
dotenv.config();

import OpenAI from "openai";
import { buildSummarySystemPrompt } from "./prompts/summary.prompt.js";
import { buildChatSystemPrompt } from "./prompts/chat.prompt.js";

/**
 * Configuration constants
 */
const DEFAULT_TEMPERATURE = {
  SUMMARY: 0.1,
  CHAT: 0.2,
};

const DEFAULT_TOP_P = 0.95;
const DEFAULT_FREQUENCY_PENALTY = 0.3;

/**
 * Azure OpenAI client configuration
 * Uses environment variables for endpoint, deployment name, API key, and version.
 * Lazy-loaded to ensure environment variables are available.
 */
let client = null;

function getClient() {
  if (!client) {
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
    const apiVersion = process.env.AZURE_OPENAI_API_VERSION;

    if (!apiKey) {
      throw new Error("AZURE_OPENAI_API_KEY environment variable is missing or empty");
    }
    if (!endpoint || !deploymentName || !apiVersion) {
      throw new Error("Azure OpenAI configuration is incomplete. Please check environment variables.");
    }
    
    client = new OpenAI({
      apiKey,
      baseURL: `${endpoint}openai/deployments/${deploymentName}`,
      defaultQuery: { "api-version": apiVersion },
      defaultHeaders: { "api-key": apiKey }
      // No timeout - will wait for response indefinitely
    });
  }
  return client;
}

/**
 * Core chat handler for FinChat Assistant.
 * Provides conversational, financial-analysis-style answers using only the provided context.
 * @param {string} contextText - Document/context text
 * @param {string} userQuestion - User's question
 * @param {Array} history - Chat history
 * @param {number} maxTokens - Maximum tokens
 * @param {boolean} summaryMode - If true, use summary prompt; if false, use conversational chat prompt
 */
export async function chatWithContext({
  contextText,
  userQuestion,
  history = [],
  maxTokens = 3000,
  summaryMode = false,
  organization
}) {
  // Build messages array
  const systemPrompt = summaryMode 
    ? buildSummarySystemPrompt(organization) 
    : buildChatSystemPrompt(organization);

  const messages = [
    { role: "system", content: systemPrompt },
    ...(organization ? [{ role: "system", content: `Organization: ${organization}` }] : []),
    { 
      role: "user", 
      content: `---DOCUMENT CONTEXT START---\n${contextText}\n---DOCUMENT CONTEXT END---` 
    },
    ...history,
    { role: "user", content: `Question: ${userQuestion}` }
  ];

  const openAIClient = getClient();
  const temperature = summaryMode ? DEFAULT_TEMPERATURE.SUMMARY : DEFAULT_TEMPERATURE.CHAT;
  
  try {
    const response = await openAIClient.chat.completions.create({
      model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
      messages,
      max_tokens: maxTokens,
      temperature,
      top_p: DEFAULT_TOP_P,
      frequency_penalty: DEFAULT_FREQUENCY_PENALTY
    });

    const content = response.choices?.[0]?.message?.content?.trim();
    
    if (!content) {
      console.warn("OpenAI returned empty response");
      return "No response generated.";
    }
    
    return content;
  } catch (error) {
    console.error("OpenAI API error:", error.message || error);
    throw error;
  }
}

/**
 * Helper to summarize or extract insights from a document
 */
export async function chatWithDocument(documentText, instructionPrompt, opts = {}) {
  const question = instructionPrompt || "Provide a concise, helpful summary.";
  return await chatWithContext({
    contextText: documentText,
    userQuestion: question,
    history: [],
    maxTokens: opts.maxTokens || 3000
  });
}
