// config/database.config.js
import dotenv from "dotenv";

dotenv.config();

const getDatabaseConfig = () => {
  const isLocalDatabase =
    process.env.NODE_ENV === "development" &&
    process.env.USE_LOCAL_DB === "true";

  if (isLocalDatabase) {
    return {
      database: process.env.LOCAL_DB_NAME || process.env.DB_NAME,
      username: process.env.LOCAL_DB_USER || process.env.DB_USER,
      password: process.env.LOCAL_DB_PASS || process.env.DB_PASS,
      host: process.env.LOCAL_DB_HOST || process.env.DB_HOST,
      port: parseInt(process.env.LOCAL_DB_PORT || process.env.DB_PORT, 10) || 5432,
      dialect: "postgres",
      logging: process.env.NODE_ENV === "development" ? console.log : false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: {},
      isLocal: true,
    };
  } else {
    const dbSslEnabled =
      typeof process.env.DB_SSL !== "undefined"
        ? process.env.DB_SSL.toLowerCase() === "true"
        : true;
    return {
      database: process.env.DB_NAME,
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10) || 5432,
      dialect: "postgres",
      logging: false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: dbSslEnabled
        ? {
            ssl: {
              require: true,
              rejectUnauthorized: false,
            },
          }
        : {},
      isLocal: false,
    };
  }
};

export default getDatabaseConfig();

