import { listPdfBlobNames } from "../app/utils/pdfLoader.js";
import { config } from "../app/config/env.js";

async function run() {
  try {
    console.log("Account:", (config.AZURE_BLOB_CONNECTION_STRING || "").includes("SharedAccessSignature=") ? "SAS" : "AccountKey");
    console.log("Container:", config.AZURE_BLOB_CONTAINER_NAME || "<unset>");
    const names = await listPdfBlobNames();
    console.log("Blob OK. PDF count:", names.length);
    console.log("Samples:", names.slice(0, 10));
  } catch (e) {
    const msg = e?.message || String(e);
    console.error("Blob FAILED:", msg);
    if (msg.includes("not authorized") || msg.includes("AuthorizationFailure") || e?.statusCode === 403) {
      console.error("Hints: \n- If using SAS, regenerate a SAS with List (l) and Read (r) permissions and a future expiry (se).\n- If using AccountKey, ensure 'Allow storage account key access' is enabled and networking allows your IP.\n- Verify AZURE_BLOB_CONTAINER_NAME matches an existing container.");
    }
    process.exit(1);
  }
}

run();
