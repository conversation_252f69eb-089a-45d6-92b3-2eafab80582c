// app/routes/file.routes.js
import express from "express";
import multer from "multer";
import {
  uploadFile,
  downloadFile,
  listFiles,
  deleteFile,
  getFileMetadata,
} from "../controllers/file.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";

const router = express.Router();

// Configure multer for file uploads (store in memory)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only PDF files (can be modified to accept other types)
    if (file.mimetype === "application/pdf") {
      cb(null, true);
    } else {
      cb(new Error("Only PDF files are allowed"), false);
    }
  },
});

/**
 * @route   POST /api/files/upload
 * @desc    Upload a file to Azure Blob Storage
 * @access  Protected (optional API key)
 */
router.post("/upload", authMiddleware, upload.single("file"), uploadFile);

/**
 * @route   GET /api/files/list
 * @desc    List all files in the container
 * @access  Protected (optional API key)
 */
router.get("/list", authMiddleware, listFiles);

/**
 * @route   GET /api/files/metadata/:filePath
 * @desc    Get file metadata
 * @access  Protected (optional API key)
 */
router.get("/metadata/:filePath", authMiddleware, getFileMetadata);

/**
 * @route   GET /api/files/:filePath
 * @desc    Download a file from Azure Blob Storage
 * @access  Public (or Protected with API key)
 */
router.get("/:filePath", downloadFile);

/**
 * @route   DELETE /api/files/:filePath
 * @desc    Delete a file from Azure Blob Storage
 * @access  Protected (optional API key)
 */
router.delete("/:filePath", authMiddleware, deleteFile);

export default router;

