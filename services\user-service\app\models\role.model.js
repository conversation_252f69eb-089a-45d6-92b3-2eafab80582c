import { DataTypes } from "sequelize";
import {
  TABLE_NAMES,
  MODEL_FIELDS,
} from "../utils/constants.util.js";

const RoleModel = (sequelize) => {
  const Role = sequelize.define(
    TABLE_NAMES.ROLES,
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
        comment: "User ID who created this role",
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
        comment: "User ID who last updated this role",
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: TABLE_NAMES.ROLES,
      timestamps: true,
      createdAt: MODEL_FIELDS.CREATED_AT,
      updatedAt: MODEL_FIELDS.UPDATED_AT,
      schema: "Authentication",
      indexes: [
        {
          name: "idx_roles_name",
          unique: true,
          fields: ["name"],
        },
        {
          name: "idx_roles_is_active",
          fields: ["is_active"],
        },
        {
          name: "idx_roles_is_deleted",
          fields: ["is_deleted"],
        },
        {
          name: "idx_roles_organization_active",
          fields: ["organization_id", "is_active", "is_deleted"],
        },
      ],
    }
  );

  return Role;
};

export default RoleModel;
