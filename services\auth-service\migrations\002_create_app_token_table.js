"use strict";

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface
      .createTable(
        "app_token",
        {
          id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV4,
            primaryKey: true,
          },
          organization_id: {
            type: Sequelize.UUID,
            allowNull: false,
          },
          user_id: {
            type: Sequelize.UUID,
            allowNull: true, // Null for invite tokens
          },
          token_type: {
            type: Sequelize.ENUM,
            values: [
              "access",
              "refresh",
              "reset",
              "invite",
              "email_verification",
              "mfa_temp",
            ],
          },
          token_hash: {
            type: Sequelize.TEXT,
          },
          // JWT tokens
          access_token: {
            type: Sequelize.TEXT,
          },
          refresh_token: {
            type: Sequelize.TEXT,
          },
          // Token metadata
          metadata: {
            type: Sequelize.JSONB,
          },
          // Token lifecycle
          expires_at: {
            type: Sequelize.DATE,
          },
          last_used_at: {
            type: Sequelize.DATE,
          },
          revoked: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          revoked_at: {
            type: Sequelize.DATE,
          },
          revoked_by: {
            type: Sequelize.UUID,
          },
          used: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          used_at: {
            type: Sequelize.DATE,
          },
          // Device/session info
          user_agent: {
            type: Sequelize.TEXT,
          },
          ip_address: {
            type: Sequelize.INET,
          },
          device_fingerprint: {
            type: Sequelize.TEXT,
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },
          created_by: {
            type: Sequelize.UUID,
          },
          updated_at: {
            type: Sequelize.DATE,
          },
          updated_by: {
            type: Sequelize.UUID,
          },
        },
        {
          schema: "Authentication",
          timestamps: true,
          createdAt: "created_at",
          updatedAt: "updated_at",
        }
      )
      .then(function () {
        // Create indexes
        return queryInterface.addIndex(
          "app_token",
          {
            name: "idx_token_user",
            fields: ["user_id"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_token",
          {
            name: "idx_token_organization",
            fields: ["organization_id"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_token",
          {
            name: "idx_token_type",
            fields: ["token_type"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_token",
          {
            name: "idx_token_hash",
            fields: ["token_hash"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_token",
          {
            name: "idx_token_expires",
            fields: ["expires_at"],
          },
          {
            schema: "Authentication",
          }
        );
      })
      .then(function () {
        return queryInterface.addIndex(
          "app_token",
          {
            name: "unique_access_token_per_user",
            unique: true,
            fields: ["user_id", "access_token"],
            where: {
              revoked: false,
              access_token: { [Sequelize.Op.ne]: null },
            },
          },
          {
            schema: "Authentication",
          }
        );
      });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable("app_token", {
      schema: "Authentication",
    });
  },
};
