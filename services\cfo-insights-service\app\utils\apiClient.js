import axios from "axios";

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_CFO_ENDPOINT || "http://localhost:3007",
  headers: { "Content-Type": "application/json" },
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default apiClient;
