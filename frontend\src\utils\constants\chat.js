/**
 * Chat Constants
 * Centralized configuration for chat functionality
 */

// API Configuration
export const CHAT_API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_CFO_ENDPOINT || "http://localhost:3007",
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  ENDPOINTS: {
    START: "/api/chat/start",
    MESSAGE: "/api/chat/message",
    SUMMARY: "/api/chat/summary",
  },
};

// Default Values
export const CHAT_DEFAULTS = {
  ORGANIZATION: "SmileCare Dental Group",
  DASHBOARD_SUMMARY_MESSAGE: "Provide the summary of this dashboard.",
};

// Message Types
export const MESSAGE_TYPES = {
  USER: "user",
  AI: "ai",
  SYSTEM: "system",
};

// Error Messages
export const CHAT_ERRORS = {
  START_SESSION_FAILED: "Failed to start chat session",
  SEND_MESSAGE_FAILED: "Failed to send message",
  GET_SUMMARY_FAILED: "Failed to get dashboard summary",
  GENERIC_ERROR:
    "Sorry, I encountered an error while processing your question. Please try again.",
  VALIDATION_ERRORS: {
    FILENAME_REQUIRED: "Valid filename is required to start chat session",
    SESSION_ID_REQUIRED: "Valid session ID is required",
    MESSAGE_EMPTY: "Message cannot be empty",
    MESSAGE_TOO_LONG:
      "Message is too long. Please keep it under 2000 characters.",
    FILENAME_INVALID: "Unable to determine filename for chat session",
    SESSION_ID_INVALID: "Invalid session ID received from server",
    NO_AI_RESPONSE: "No response received from AI",
  },
  NETWORK_ERRORS: {
    TIMEOUT: "Request timeout. Please try again.",
    SERVICE_UNAVAILABLE: "Chat service not available. Please try again later.",
    SERVER_ERROR: "Server error. Please try again later.",
    SESSION_NOT_FOUND: "Chat session not found. Please start a new chat.",
    RATE_LIMITED:
      "Too many requests. Please wait a moment before trying again.",
  },
};

// Static Messages
export const CHAT_MESSAGES = {
  WELCOME: `Welcome to CFO Dashboard! 👋

I'm analyzing your **{selectedOption} Dashboard {dashboardType} - {selectedMonth} {currentYear}**.

How can I help you with this {selectedOptionLower} data?`,
  
  DASHBOARD_SUMMARY_REQUEST: "Provide the summary of this dashboard or file",
  
  PLACEHOLDER: "Ask a question about your financial documents...",
  
  BUTTON_TEXT: {
    SEND: "Send",
    FINISH_CHAT: "Finish Chat",
  },
  
  UI_TEXT: {
    CHARACTER_COUNT: "{current}/{max}",
    TYPING: "Typing...",
    THINKING: "Thinking...",
    START_CONVERSATION:
      "Start a conversation by asking a question about your financial documents",
  },
};

// File Name Mapping - matches actual file names in /uploads directories
export const FILENAME_MAPPING = {
  CHP: {
    FINANCIAL: {
      June: "CHP Finance Dashboard - June 2025.pdf",
      July: "CHP Finance Dashboard - July 2025.pdf",
      August: "CHP Finance Dashboard - August 2025.pdf",
    },
    OPERATIONS: {
      June: "CHP Operations Dashboard - June 2025.pdf",
    },
    PAYROLL: {
      June: "CHP Payroll Dashboard - June 2025.pdf",
      July: "CHP Payroll Dashboard - July 2025.pdf",
      August: "CHP Payroll Dashboard - August 2025.pdf",
    },
  },
  DENTAL: {
    FINANCIAL: {
      June: "Dental Finance Dashboard - June 2025.pdf",
      July: "Dental Finance Dashboard - July 2025.pdf",
      August: "Dental Finance Dashboard - August 2025.pdf",
    },
    OPERATIONS: {
      June: "Dental Operations Dashboard - June 2025.pdf",
    },
    PAYROLL: {
      June: "Dental Payroll Dashboard - June 2025.pdf",
      July: "Dental Payroll Dashboard - July 2025.pdf",
      August: "Dental Payroll Dashboard - August 2025.pdf",
    },
  },
  DEFAULT: "CHP Finance Dashboard - June 2025.pdf",
};
