import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import * as status from "../utils/status_code.utils.js";
import logger from "../../config/logger.config.js";
import { getLastMonthDateRange } from "../utils/date.utils.js";
import reportsService from "../services/reports.service.js";
import {
  ReportsError<PERSON>andler,
  ReportsResponseHandler,
  ReportsValidator,
} from "../utils/reports.utils.js";
import { getReportConfig } from "../../config/reports.config.js";
import { GLAccountMaster } from "../models/index.js";
import { decrypt } from "../utils/encryption.utils.js";
import { quickbooksRepository } from "../repositories/quickbooks.repository.js";
import axios from "axios";

/**
 * Fetch organization schema name by realm_id
 * @param {string} realmId - Realm ID
 * @returns {Promise<string|null>} Schema name or null if not found
 */
const fetchOrganizationSchemaName = async (realmId) => {
  try {
    const orgApiUrl = `http://localhost:3001/api/organization/realm/${realmId}`;

    logger.info(`Fetching organization details for realm_id: ${realmId}`);
    const response = await axios.get(orgApiUrl);

    if (response.status === 200 && response.data) {
      const schemaName = response.data.data?.schema_name;

      logger.info(
        `Retrieved schema name: ${schemaName} for realm_id: ${realmId}`
      );
      return schemaName;
    } else {
      logger.warn(
        `Failed to retrieve organization details for realm_id: ${realmId}`
      );
      return null;
    }
  } catch (error) {
    logger.error(
      `Error fetching organization details for realm_id ${realmId}:`,
      { error: error.message }
    );
    return null;
  }
};

export const processReport = async (req, res, reportType) => {
  const config = getReportConfig(reportType);
  if (!config) {
    return ReportsResponseHandler.sendError(
      res,
      `Unsupported report type: ${reportType}`,
      status.STATUS_CODE_BAD_REQUEST
    );
  }

  const { realmId, schemaName } = req.body;
  const { startDate, endDate } = req.body;

  const account = await quickbooksRepository.findByRealmId(
    realmId,
    {},
    schemaName
  );
  const quickBookAccesstoken = account?.access_token
    ? await decrypt(account.access_token)
    : null;

  logger.info("Processing report request", {
    reportType,
    startDate,
    endDate,
    realmId,
    schemaName,
  });

  try {
    // Validate required fields
    if (
      !ReportsValidator.validateRequiredFields(
        req,
        res,
        reportType,
        config.requiresDates
      )
    ) {
      return; // Response already sent
    }

    // Auto-generate dates for P&L if missing
    if (
      reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS &&
      (!startDate || !endDate)
    ) {
      const lastMonthRange = getLastMonthDateRange();
      startDate = startDate || lastMonthRange.startDate;
      endDate = endDate || lastMonthRange.endDate;

      logger.info("Auto-generated dates for P&L report", {
        autoStartDate: startDate,
        autoEndDate: endDate,
      });
    }

    // Create account object
    const mockQuickbookAccount = {
      realm_id: realmId,
      access_token: quickBookAccesstoken,
      refreshToken: account?.refresh_token
        ? await decrypt(account.refresh_token)
        : null,
    };

    // Fetch report data
    const reportData = await reportsService.getReportDataFromQuickBooks(
      mockQuickbookAccount,
      startDate,
      endDate,
      reportType,
      quickBookAccesstoken
    );


    // Map data with error handling
    let mappedData;
    try {
      // Handle async mapping functions
      if (config.isAsync) {
        mappedData = await config.mapFunction(
          reportData,
          null,
          realmId,
          startDate,
          endDate,
          schemaName
        );
      } else {
        mappedData = config.mapFunction(
          reportData,
          null,
          realmId,
          startDate,
          endDate,
          schemaName
        );
      }
    } catch (mappingError) {
      logger.error("Mapping function failed", {
        error: mappingError.message,
        stack: mappingError.stack,
        reportType,
        realmId,
      });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }

    // Validate mapped data
    if (!mappedData?.reportData) {
      logger.error("Failed to map report data", { reportType, realmId });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }


    // Save data
    const savedData = await config.saveFunction(mappedData, schemaName);

    // Validate save result
    if (!savedData?.reportId) {
      logger.error("Failed to save report data", { reportType });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.DATABASE.SAVE_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }

    // Send success response
    const responseData = {
      reportId: savedData.reportId,
      columnsCount: savedData.columnsCount,
      rowsCount: savedData.rowsCount,
      reportType,
      dateRange: { startDate, endDate },
      realmId,
      ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && {
        autoGeneratedDates: !req.body.startDate || !req.body.endDate,
      }),
    };

    // Add balance sheet specific data
    if (
      reportType === HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET &&
      savedData.totals
    ) {
      responseData.totals = savedData.totals;
      responseData.balanceCheck = {
        isBalanced: savedData.totals.isBalanced,
        totalAssets: savedData.totals.totalAssets,
        totalLiabilitiesAndEquity: savedData.totals.totalLiabilitiesAndEquity,
      };
    }

    return ReportsResponseHandler.sendSuccess(
      res,
      config.successMessage,
      responseData
    );
  } catch (error) {

    return ReportsErrorHandler.handleApiError(
      error,
      res,
      reportType,
      realmId,
      "processing"
    );
  }
};

/**
 * Streamlined report controllers using configuration
 */
export const fetchTrialBalance = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE);
export const fetchProfitLoss = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS);
export const fetchBalanceSheet = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET);
export const fetchCashFlow = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW);

/**
 * Generic report retrieval function
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} reportType - Type of report
 * @param {Function} serviceFunction - Service function to call
 * @returns {Promise<Object>} HTTP response
 */

const getReportsByRealmId = async (req, res, reportType, serviceFunction) => {
  try {
    const { realmId } = req.params;

    // Validate realmId
    if (!realmId) {
      return ReportsErrorHandler.handleValidationError(
        res,
        "realmId",
        reportType
      );
    }

    const reports = await serviceFunction(realmId, {
      order: HARDCODED_STRINGS.DB_ORDER.CREATED_AT_DESC,
      limit: parseInt(req.query.limit) || 10,
      offset: parseInt(req.query.offset) || 0,
    });

    return ReportsResponseHandler.sendSuccess(
      res,
      `${reportType} reports retrieved successfully`,
      {
        reports,
        realmId,
        count: reports.length,
      }
    );
  } catch (error) {
    return ReportsErrorHandler.handleApiError(
      error,
      res,
      reportType,
      req.params?.realmId,
      "retrieval"
    );
  }
};

/**
 * Report retrieval controllers using generic function
 */
export const getTrialBalanceReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE_DISPLAY,
    reportsService.getTrialBalanceReportsByRealmId
  );

export const getProfitLossReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.PROFIT_LOSS_DISPLAY,
    reportsService.getProfitLossReportsByRealmId
  );

export const getBalanceSheetReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET_DISPLAY,
    reportsService.getBalanceSheetReportsByRealmId
  );

export const getCashFlowReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW_DISPLAY,
    reportsService.getCashFlowReportsByRealmId
  );

// Helper to process and return report data for use in batch APIs (no Express req/res)
export const processReportRaw = async (inputBody, reportType) => {
  const config = getReportConfig(reportType);
  if (!config) {
    return { success: false, error: `Unsupported report type: ${reportType}` };
  }
  const { realmId } = inputBody;
  let { startDate, endDate } = inputBody;
  try {
    // Fetch organization schema name by realm_id
    let schemaName = null;
    if (realmId) {
      schemaName = await fetchOrganizationSchemaName(realmId);
      if (schemaName) {
        logger.info(
          `Using schema name: ${schemaName} for raw report processing`
        );
      } else {
        logger.warn(`Could not retrieve schema name for realm_id: ${realmId}`);
      }
    }

    // Fetch access token
    const account = await quickbooksRepository.findByRealmId(
      realmId,
      {},
      schemaName
    );
    const quickBookAccesstoken = account?.access_token
      ? await decrypt(account.access_token)
      : null;
    if (!quickBookAccesstoken) {
      return {
        success: false,
        error: `No QBO access token for realmId: ${realmId}`,
      };
    }
    if (
      reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS &&
      (!startDate || !endDate)
    ) {
      const lastMonthRange = getLastMonthDateRange();
      startDate = startDate || lastMonthRange.startDate;
      endDate = endDate || lastMonthRange.endDate;
    }
    const mockQuickbookAccount = {
      realm_id: realmId,
      access_token: quickBookAccesstoken,
    };
    // Get raw report data
    const reportData = await reportsService.getReportDataFromQuickBooks(
      mockQuickbookAccount,
      startDate,
      endDate,
      reportType,
      quickBookAccesstoken
    );
    // Map
    let mappedData;
    if (config.isAsync) {
      mappedData = await config.mapFunction(
        reportData,
        null,
        realmId,
        startDate,
        endDate,
        schemaName
      );
    } else {
      mappedData = config.mapFunction(
        reportData,
        null,
        realmId,
        startDate,
        endDate,
        schemaName
      );
    }
    if (!mappedData?.reportData) {
      return { success: false, error: "Data mapping failed", reportType };
    }
    // Save
    const savedData = await config.saveFunction(mappedData, schemaName);
    if (!savedData?.reportId) {
      return { success: false, error: "Save failed", reportType };
    }
    const responseData = {
      reportId: savedData.reportId,
      columnsCount: savedData.columnsCount,
      rowsCount: savedData.rowsCount,
      reportType,
      dateRange: { startDate, endDate },
      realmId,
      ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && {
        autoGeneratedDates: !inputBody.startDate || !inputBody.endDate,
      }),
    };
    if (
      reportType === HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET &&
      savedData.totals
    ) {
      responseData.totals = savedData.totals;
      responseData.balanceCheck = {
        isBalanced: savedData.totals.isBalanced,
        totalAssets: savedData.totals.totalAssets,
        totalLiabilitiesAndEquity: savedData.totals.totalLiabilitiesAndEquity,
      };
    }
    return { success: true, data: responseData };
  } catch (error) {
    return { success: false, error: error.message, reportType };
  }
};
