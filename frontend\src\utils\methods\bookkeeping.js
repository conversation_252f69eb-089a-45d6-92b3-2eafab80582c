/**
 * Generate dynamic array of completed months for the current year
 * Returns months from January up to (but not including) the current month
 * @returns {string[]} Array of month strings in format "MonthName YYYY" (e.g., "January 2025")
 */
export const getBookkeepingMonths = () => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth(); // 0-indexed (0 = January, 10 = November)

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Generate months from January up to (but not including) the current month
  const months = monthNames
    .slice(0, currentMonth)
    .map((monthName) => `${monthName} ${currentYear}`);

  return months;
}
