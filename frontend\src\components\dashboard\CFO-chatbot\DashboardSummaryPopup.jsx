"use client";

import { useEffect, useCallback, useMemo, memo } from "react";
import { motion } from "framer-motion";
import { Button } from "../../ui/button";
import { X, BarChart3 } from "lucide-react";
import { formatMessage } from "../../../utils/formatMessage";
import "../../../styles/dashboardSummary.css";

const DashboardSummaryPopup = memo(function DashboardSummaryPopup({ 
  isOpen, 
  onClose, 
  dashboardSummary,
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isLoading = false,
  isSummaryLoading = false
}) {
  // Add console.log for the raw response from the API
  // console.log("DashboardSummaryPopup API response:", dashboardSummary);

  const formattedContent = useMemo(() => formatMessage(dashboardSummary), [dashboardSummary]);

  const dashboardType = useMemo(() => {
    if (isFinancialSelected) return 'Financial';
    if (isOperationsSelected) return 'Operations';
    return 'Dashboard';
  }, [isFinancialSelected, isOperationsSelected]);

  const displayLabel = useMemo(() => `${dashboardType} - ${selectedMonth} 2025`, [dashboardType, selectedMonth]);

  const handleEscKey = useCallback((event) => {
    if (event.key === 'Escape' && isOpen) onClose();
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) document.addEventListener('keydown', handleEscKey);
    return () => { document.removeEventListener('keydown', handleEscKey); };
  }, [isOpen, handleEscKey]);

  if (!isOpen) return null;

  // Show loading if explicitly loading or if popup is open but no summary content exists yet
  const loading = isLoading || isSummaryLoading || (isOpen && !dashboardSummary);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md" onClick={onClose}>
      <motion.div
        initial={{ opacity: 0, scale: 0.98, y: 32 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.98, y: 32 }}
        transition={{ duration: 0.22, ease: "easeOut" }}
        className="bg-white rounded-2xl shadow-2xl border border-gray-100/50 max-w-3xl w-full mx-4 max-h-[85vh] flex flex-col overflow-hidden"
        onClick={e => e.stopPropagation()}
      >
        {/* Sticky Header */}
        <div className="sticky top-0 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-b border-gray-100/70 z-10 px-4 py-2.5 flex items-center justify-between shadow-sm">
          <div className="flex items-center gap-2.5">
            <span className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center shadow-md">
              <BarChart3 className="w-4 h-4 text-white" />
            </span>
            <div>
              <h3 className="text-sm font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Dashboard Summary</h3>
              <p className="text-[9px] text-gray-500 mt-0.5 font-medium">Press ESC to close</p>
            </div>
          </div>
          <Button onClick={onClose} variant="ghost" size="sm" className="p-1 hover:bg-white/60 rounded-lg transition-all duration-300" title="Close (ESC)"><X className="w-4 h-4 text-gray-600" /></Button>
        </div>

        {/* Sticky/Compact Label */}
        <div className="sticky top-[52px] bg-white z-10 border-b border-gray-100 px-4 py-1.5 shadow-sm">
          <h4 className="text-xs font-semibold text-indigo-700 flex items-center gap-2">
            {dashboardType} Overview
            <span className="text-[8px] font-normal text-indigo-500 bg-indigo-50 px-1.5 py-0.5 rounded-full">
              {displayLabel}
            </span>
          </h4>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto min-h-0 bg-white px-4 py-3" style={{ maxHeight: 'calc(85vh - 120px)' }}>
          {loading ? (
            <div className="flex flex-col items-center justify-center w-full min-h-[400px] gap-4">
              <div className="relative w-16 h-16">
                {/* Outer ring - Purple */}
                <motion.div
                  className="absolute inset-0 border-4 border-transparent border-t-purple-500 border-r-purple-400 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                
                {/* Middle ring - Indigo */}
                <motion.div
                  className="absolute inset-2 border-4 border-transparent border-t-indigo-500 border-r-indigo-400 rounded-full"
                  animate={{ rotate: -360 }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                
                {/* Inner ring - Blue */}
                <motion.div
                  className="absolute inset-4 border-4 border-transparent border-t-blue-500 border-r-blue-400 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                
                {/* Center pulsing dot */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.5, 1]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </div>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-gray-700 font-medium text-base"
              >
                Generating summary...
              </motion.p>
            </div>
          ) : formattedContent ? (
            <div
              className="dashboard-summary-content compact-tables"
              dangerouslySetInnerHTML={{ __html: formattedContent }}
            />
          ) : (
            <div className="text-center text-gray-400 py-8">
              <p className="text-sm">No summary available</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-4 py-2 border-t border-gray-200/40 bg-gradient-to-r from-gray-50/60 to-indigo-50/40">
          <div className="text-[9px] text-gray-500">💡 Tip: Press ESC to close quickly</div>
          <Button onClick={onClose} variant="outline" className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 hover:border-indigo-300 px-3 py-1 text-xs rounded-lg font-medium transition-all duration-200 hover:scale-105" title="Close (ESC)">Close</Button>
        </div>
      </motion.div>


    </div>
  );
});

export default DashboardSummaryPopup;
