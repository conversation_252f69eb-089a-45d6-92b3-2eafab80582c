"use strict";

module.exports = {
  up: async function (queryInterface, Sequelize) {
    // Ensure schema exists
    await queryInterface.sequelize.query('CREATE SCHEMA IF NOT EXISTS "Authentication"');
    
    // Create table
    await queryInterface.createTable(
      "document",
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        organization_id: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        blob_storage_path: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Azure blob storage file path",
        },
        service: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Service type",
        },
        month: {
          type: Sequelize.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 12,
          },
          comment: "Month (1-12)",
        },
        year: {
          type: Sequelize.INTEGER,
          allowNull: false,
          validate: {
            min: 2000,
            max: 3000,
          },
          comment: "Year (e.g., 2025)",
        },
        file_name: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Original filename",
        },
        file_size: {
          type: Sequelize.BIGINT,
          allowNull: true,
          comment: "File size in bytes",
        },
        mime_type: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "File MIME type",
        },
        summary: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Document summary text",
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
        },
        is_deleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: "{}",
          comment: "Additional metadata",
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        created_by: {
          type: Sequelize.UUID,
          allowNull: true,
        },
        updated_at: {
          type: Sequelize.DATE,
        },
        updated_by: {
          type: Sequelize.UUID,
          allowNull: true,
        },
      },
      {
        schema: "Authentication",
        timestamps: true,
        createdAt: "created_at",
        updatedAt: "updated_at",
      }
    );

    // Create indexes
    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_organization_id",
        fields: ["organization_id"],
      }
    );

    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_service",
        fields: ["service"],
      }
    );

    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_month_year",
        fields: ["month", "year"],
      }
    );

    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_organization_service_month_year",
        fields: ["organization_id", "service", "month", "year"],
      }
    );

    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_is_deleted",
        fields: ["is_deleted"],
      }
    );

    await queryInterface.addIndex(
      { tableName: "document", schema: "Authentication" },
      {
        name: "idx_document_created_at",
        fields: ["created_at"],
      }
    );
  },

  down: async function (queryInterface, Sequelize) {
    await queryInterface.dropTable("document", {
      schema: "Authentication",
    });
  },
};
