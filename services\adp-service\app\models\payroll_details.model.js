import { DataTypes } from "sequelize";

const PayrollDetailsModel = (sequelize) =>
  sequelize.define(
    "adp_payroll_details",
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      employee_id: { type: DataTypes.STRING, allowNull: false },
      payroll_month: { type: DataTypes.STRING, allowNull: false },
      ssn: { type: DataTypes.STRING },
      tin: { type: DataTypes.STRING },
      pay_frequency: { type: DataTypes.STRING },
      earning_1_name: { type: DataTypes.STRING },
      hours_1: { type: DataTypes.STRING },
      rate_1: { type: DataTypes.STRING },
      amount_1: { type: DataTypes.STRING },
      earning_2_name: { type: DataTypes.STRING },
      hours_2: { type: DataTypes.STRING },
      rate_2: { type: DataTypes.STRING },
      amount_2: { type: DataTypes.STRING },
      earning_3_name: { type: DataTypes.STRING },
      hours_3: { type: DataTypes.STRING },
      rate_3: { type: DataTypes.STRING },
      amount_3: { type: DataTypes.STRING },
      earning_4_name: { type: DataTypes.STRING },
      hours_4: { type: DataTypes.STRING },
      rate_4: { type: DataTypes.STRING },
      amount_4: { type: DataTypes.STRING },
      earning_5_name: { type: DataTypes.STRING },
      hours_5: { type: DataTypes.STRING },
      rate_5: { type: DataTypes.STRING },
      amount_5: { type: DataTypes.STRING },
      earning_6_name: { type: DataTypes.STRING },
      hours_6: { type: DataTypes.STRING },
      rate_6: { type: DataTypes.STRING },
      amount_6: { type: DataTypes.STRING },
      earning_7_name: { type: DataTypes.STRING },
      hours_7: { type: DataTypes.STRING },
      rate_7: { type: DataTypes.STRING },
      amount_7: { type: DataTypes.STRING },
      earning_8_name: { type: DataTypes.STRING },
      hours_8: { type: DataTypes.STRING },
      rate_8: { type: DataTypes.STRING },
      amount_8: { type: DataTypes.STRING },
      earning_9_name: { type: DataTypes.STRING },
      hours_9: { type: DataTypes.STRING },
      rate_9: { type: DataTypes.STRING },
      amount_9: { type: DataTypes.STRING },
      total_hours: { type: DataTypes.STRING },
      total_earnings: { type: DataTypes.STRING },
      fed_fit: { type: DataTypes.STRING },
      fed_socsec: { type: DataTypes.STRING },
      fed_medcare: { type: DataTypes.STRING },
      il_sit: { type: DataTypes.STRING },
      total_taxes: { type: DataTypes.STRING },
      adp_rs_employee_before_tax: { type: DataTypes.STRING },
      adp_rs_employee_roth_401k: { type: DataTypes.STRING },
      bcbs_il: { type: DataTypes.STRING },
      deduction_total: { type: DataTypes.STRING },
      net_pay: { type: DataTypes.STRING },
      fed_socsec_er: { type: DataTypes.STRING },
      fed_medcare_er: { type: DataTypes.STRING },
      fed_futa: { type: DataTypes.STRING },
      il_sui_er: { type: DataTypes.STRING },
      adp_rs_employer_match: { type: DataTypes.STRING },
      blue_cross_pre_tax: { type: DataTypes.STRING },
      wage_assignment_1: { type: DataTypes.STRING },
      loan: { type: DataTypes.STRING },
      medical_pre_tax_1: { type: DataTypes.STRING },
      payment_1: { type: DataTypes.STRING },
      payment_1_check_date: { type: DataTypes.STRING },
      payment_1_transaction_id_or_check: { type: DataTypes.STRING },
      payment_1_amount: { type: DataTypes.STRING },
      payment_2: { type: DataTypes.STRING },
      payment_2_check_date: { type: DataTypes.STRING },
      payment_2_transaction_id_or_check: { type: DataTypes.STRING },
      payment_2_amount: { type: DataTypes.STRING },
      payment_3: { type: DataTypes.STRING },
      payment_3_check_date: { type: DataTypes.STRING },
      payment_3_transaction_id_or_check: { type: DataTypes.STRING },
      payment_3_amount: { type: DataTypes.STRING },
      payment_4: { type: DataTypes.STRING },
      payment_4_check_date: { type: DataTypes.STRING },
      payment_4_transaction_id_or_check: { type: DataTypes.STRING },
      payment_4_amount: { type: DataTypes.STRING },
      total_employer_liability: { type: DataTypes.STRING },
    },
    {
      tableName: "adp_payroll_details",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

export default PayrollDetailsModel;
