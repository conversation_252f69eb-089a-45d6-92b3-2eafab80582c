import winston from "winston";

const { combine, timestamp, printf, colorize, errors } = winston.format;

// Custom format for console output
const consoleFormat = printf(({ level, message, timestamp, service, ...meta }) => {
  let log = `${timestamp} [${service || 'ADP_SERVICE'}] ${level}: ${message}`;
  
  // Add metadata if present
  if (Object.keys(meta).length > 0) {
    log += ` ${JSON.stringify(meta)}`;
  }
  
  return log;
});

// Custom format for file output
const fileFormat = printf(({ level, message, timestamp, service, ...meta }) => {
  return JSON.stringify({
    timestamp,
    level,
    service: service || 'adp-service',
    message,
    ...meta
  });
});

// Create logger configuration
const loggerConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: combine(
    errors({ stack: true }),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' })
  ),
  defaultMeta: { service: 'adp-service' },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: combine(
        colorize(),
        consoleFormat
      )
    }),
    
    // File transport for errors
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: fileFormat
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: fileFormat
    })
  ]
};

// Create and export logger
const logger = winston.createLogger(loggerConfig);

export default logger;
