"use client";

import React from "react";
import Loader from "./Loader";

/**
 * PageLoader Component
 * - Wrapper around the consistent Loader component for page-level loading
 * - Supports fullScreen and inline loading modes
 * - Uses the same Loader component across the entire system for consistency
 */
const PageLoader = ({ message = "Loading...", fullScreen = false }) => {
  if (fullScreen) {
    // Full screen overlay - uses consistent Loader component
    return <Loader message={message} show={true} />;
  }

  // Inline loader for page content area - uses consistent Loader component
  return (
    <div className="flex flex-col items-center justify-center py-20 min-h-[400px]">
      <Loader message={message} show={true} />
    </div>
  );
};

export default PageLoader;
