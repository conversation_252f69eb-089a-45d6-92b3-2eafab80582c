import express from "express";
import authRoutes from "./auth.route.js";
import tokenRoutes from "./token.route.js";
import organizationRoutes from "./organization.route.js";

const router = express.Router();

// AUTH MODULE ROUTES

/**
 * Authentication Routes
 * Handles user authentication, registration, MFA, password management
 */
router.use('/auth', authRoutes);

/**
 * Token Management Routes
 * Handles token refresh, revocation, validation, and lifecycle management
 */
router.use('/auth/token', tokenRoutes);

/**
 * Organization Management Routes
 * Handles organization creation, retrieval, updates, and deletion
 */
router.use('/organization', organizationRoutes);

export default router;
