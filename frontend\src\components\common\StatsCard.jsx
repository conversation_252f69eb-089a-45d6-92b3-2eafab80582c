import React from "react";
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  TrendingUp,
  Shield,
  Key,
  Settings,
  Building,
  Building2,
  MapPin,
  Calendar,
  CheckCircle,
} from "lucide-react";

// Icon mapping for different stat types
const iconMap = {
  users: <Users className="w-6 h-6" />,
  "user-check": <UserCheck className="w-6 h-6" />,
  "user-x": <UserX className="w-6 h-6" />,
  clock: <Clock className="w-6 h-6" />,
  "trending-up": <TrendingUp className="w-6 h-6" />,
  shield: <Shield className="w-6 h-6" />,
  key: <Key className="w-6 h-6" />,
  settings: <Settings className="w-6 h-6" />,
  building: <Building className="w-6 h-6" />,
  "building-2": <Building2 className="w-6 h-6" />,
  "map-pin": <MapPin className="w-6 h-6" />,
  calendar: <Calendar className="w-6 h-6" />,
  "check-circle": <CheckCircle className="w-6 h-6" />,
};

export default function StatsCard({
  title,
  value,
  icon,
  bgColor = "bg-blue-100",
  iconColor = "text-blue-600",
  className = "",
  onClick,
  loading = false,
}) {
  const cardClasses = `
    ${bgColor} rounded-xl p-5 shadow-sm hover:shadow-md border border-transparent 
    transition-all duration-200 hover:scale-[1.02] min-h-[96px] flex flex-col justify-between
    ${onClick ? "cursor-pointer" : ""} 
    ${className}
  `;

  const content = (
      <div className="w-full">
        <p className="text-sm font-medium mb-2" style={{ 
          color: iconColor.includes('indigo') ? '#4F46E5' :
                 iconColor.includes('green') ? '#059669' :
                 iconColor.includes('yellow') ? '#D97706' :
                 iconColor.includes('rose') ? '#E11D48' : '#6B7280'
        }}>
          {title}
        </p>
        {loading ? (
          <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-2"></div>
        ) : (
          <p className="text-2xl font-bold mt-2" style={{
            color: iconColor.includes('indigo') ? '#312E81' :
                   iconColor.includes('green') ? '#065F46' :
                   iconColor.includes('yellow') ? '#78350F' :
                   iconColor.includes('rose') ? '#991B1B' : '#111827'
          }}>
            {value}
          </p>
        )}
      </div>
  );

  if (onClick) {
    return (
      <button onClick={onClick} className={cardClasses}>
        {content}
      </button>
    );
  }

  return <div className={cardClasses}>{content}</div>;
}
