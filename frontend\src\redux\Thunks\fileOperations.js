import { createAsyncThunk } from '@reduxjs/toolkit';
import { downloadFile as downloadFileUtil, fileToBase64 } from '@/utils/methods/helpers';

// Download file thunk
export const downloadFile = createAsyncThunk(
  'fileOperations/downloadFile',
  async ({ url, filename }, { rejectWithValue }) => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.statusText}`);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      downloadFileUtil(downloadUrl, filename || 'download');
      
      window.URL.revokeObjectURL(downloadUrl);
      
      return { success: true, filename: filename || 'download' };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to download file');
    }
  }
);

// Upload file thunk
export const uploadFile = createAsyncThunk(
  'fileOperations/uploadFile',
  async ({ file, uploadFunction, uploadOptions = {} }, { rejectWithValue, dispatch }) => {
    try {
      // Validate file if validation function provided
      if (uploadOptions.validateFile) {
        const validation = uploadOptions.validateFile(file);
        if (!validation.isValid) {
          throw new Error(validation.message);
        }
      }

      // Upload file with progress tracking
      const result = await uploadFunction(file, {
        ...uploadOptions,
        onProgress: (progress) => {
          dispatch(setUploadProgress(progress));
        },
      });

      return { file, result };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to upload file');
    }
  }
);

// Upload multiple files thunk
export const uploadMultipleFiles = createAsyncThunk(
  'fileOperations/uploadMultipleFiles',
  async ({ files, uploadFunction, uploadOptions = {} }, { rejectWithValue, dispatch }) => {
    try {
      const fileArray = Array.from(files);
      const results = [];

      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        
        // Validate file if validation function provided
        if (uploadOptions.validateFile) {
          const validation = uploadOptions.validateFile(file);
          if (!validation.isValid) {
            throw new Error(validation.message);
          }
        }

        // Upload file
        const result = await uploadFunction(file, {
          ...uploadOptions,
          onProgress: (progress) => {
            const totalProgress = ((i / fileArray.length) + (progress / 100 / fileArray.length)) * 100;
            dispatch(setUploadProgress(totalProgress));
          },
        });

        results.push({ file, result });
      }

      return results;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to upload files');
    }
  }
);

// Convert file to base64 thunk
export const convertFileToBase64 = createAsyncThunk(
  'fileOperations/convertFileToBase64',
  async (file, { rejectWithValue }) => {
    try {
      const base64 = await fileToBase64(file);
      return { file, base64 };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to convert file to base64');
    }
  }
);

// Validate file thunk
export const validateFile = createAsyncThunk(
  'fileOperations/validateFile',
  async ({ file, rules = {} }, { rejectWithValue, getState }) => {
    try {
      const state = getState();
      const validationRules = state.fileOperations.validationRules;
      
      const {
        allowedTypes = validationRules.allowedTypes,
        maxSize = validationRules.maxSize,
        minSize = validationRules.minSize,
      } = rules;

      // Check file type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        throw new Error(
          `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
        );
      }

      // Check file size
      if (file.size > maxSize) {
        throw new Error(
          `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${(maxSize / 1024 / 1024).toFixed(2)}MB)`
        );
      }

      if (file.size < minSize) {
        throw new Error(
          `File size is too small. Minimum size: ${(minSize / 1024).toFixed(2)}KB`
        );
      }

      return { isValid: true, file };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Preview file thunk
export const previewFile = createAsyncThunk(
  'fileOperations/previewFile',
  async (file, { rejectWithValue }) => {
    try {
      if (!file) {
        throw new Error('No file provided for preview');
      }

      const url = URL.createObjectURL(file);
      
      return {
        file,
        url,
        cleanup: () => URL.revokeObjectURL(url),
      };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to preview file');
    }
  }
);

// Remove uploaded file thunk
export const removeUploadedFile = createAsyncThunk(
  'fileOperations/removeUploadedFile',
  async (index, { rejectWithValue }) => {
    try {
      return index;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to remove file');
    }
  }
);

// Clear uploaded files thunk
export const clearUploadedFiles = createAsyncThunk(
  'fileOperations/clearUploadedFiles',
  async (_, { rejectWithValue }) => {
    try {
      return true;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to clear files');
    }
  }
);

// Import the action from the slice
import { setUploadProgress } from '@/redux/Slice/fileOperations';
