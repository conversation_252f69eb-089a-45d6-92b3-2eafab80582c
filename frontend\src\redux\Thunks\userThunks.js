import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";

import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.USER);

// 🔹 Fetch Users
export const fetchUsers = createAsyncThunk(
  "users/fetchUsers",
  async (params = {}, { rejectWithValue }) => {
    try {
      const {
        status = "all",
        role = "all",
        search = "",
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params;

      const queryParams = new URLSearchParams({
        sortBy,
        sortOrder,
      });

      if (status !== "all") {
        queryParams.append("status", status);
      }
      if (role !== "all") {
        queryParams.append("role", role);
      }
      if (search) {
        queryParams.append("search", search);
      }

      const response = await axios.get(`/users?${queryParams.toString()}`);
      return response.data.data;
    } catch (error) {
      let errorMessage = "Failed to fetch users";
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 400:
            errorMessage = data.message || "Invalid request parameters";
            break;
          case 401:
            errorMessage = "Unauthorized access";
            break;
          case 403:
            errorMessage = "Access forbidden";
            break;
          case 404:
            errorMessage = "Users not found";
            break;
          case 500:
            errorMessage = "Server error occurred";
            break;
          default:
            errorMessage = data.message || "Failed to fetch users";
        }
      } else if (error.request) {
        errorMessage = "Network error - please check your connection";
      }
      return rejectWithValue(errorMessage);
    }
  }
);

// 🔹 Create User
export const createUser = createAsyncThunk(
  "users/createUser",
  async (userData, { rejectWithValue }) => {
    try {
      const response = await axios.post("/users", userData);
      return response.data;
    } catch (error) {
      let errorMessage = "Failed to create user";
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 400:
            errorMessage = data.message || "Invalid user data";
            break;
          case 401:
            errorMessage = "Unauthorized access";
            break;
          case 403:
            errorMessage = "Access forbidden";
            break;
          case 409:
            errorMessage = data.message || "User already exists";
            break;
          case 500:
            errorMessage = "Server error occurred";
            break;
          default:
            errorMessage = data.message || "Failed to create user";
        }
      } else if (error.request) {
        errorMessage = "Network error - please check your connection";
      }
      return rejectWithValue(errorMessage);
    }
  }
);

// 🔹 Update User
export const updateUser = createAsyncThunk(
  "users/updateUser",
  async ({ userId, userData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      let errorMessage = "Failed to update user";
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 400:
            errorMessage = data.message || "Invalid user data";
            break;
          case 401:
            errorMessage = "Unauthorized access";
            break;
          case 403:
            errorMessage = "Access forbidden";
            break;
          case 404:
            errorMessage = "User not found";
            break;
          case 409:
            errorMessage = data.message || "User already exists";
            break;
          case 500:
            errorMessage = "Server error occurred";
            break;
          default:
            errorMessage = data.message || "Failed to update user";
        }
      } else if (error.request) {
        errorMessage = "Network error - please check your connection";
      }
      return rejectWithValue(errorMessage);
    }
  }
);

// 🔹 Delete User
export const deleteUser = createAsyncThunk(
  "users/deleteUser",
  async (userId, { rejectWithValue }) => {
    try {
      await axios.delete(`/users/${userId}`);
      return userId;
    } catch (error) {
      let errorMessage = "Failed to delete user";
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 401:
            errorMessage = "Unauthorized access";
            break;
          case 403:
            errorMessage = "Access forbidden";
            break;
          case 404:
            errorMessage = "User not found";
            break;
          case 500:
            errorMessage = "Server error occurred";
            break;
          default:
            errorMessage = data.message || "Failed to delete user";
        }
      } else if (error.request) {
        errorMessage = "Network error - please check your connection";
      }
      return rejectWithValue(errorMessage);
    }
  }
);

// 🔹 Get User By ID
export const getUserById = createAsyncThunk(
  "users/getUserById",
  async (userId, { rejectWithValue }) => {
    try {
      return response.data.data || response.data;
    } catch (error) {

      let errorMessage = "Failed to fetch user";
      if (error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 401:
            errorMessage = "Unauthorized access";
            break;
          case 403:
            errorMessage = "Access forbidden";
            break;
          case 404:
            errorMessage = "User not found";
            break;
          case 500:
            errorMessage = "Server error occurred";
            break;
          default:
            errorMessage = data.message || "Failed to fetch user";
        }
      } else if (error.request) {
        errorMessage = "Network error - please check your connection";
      }
      return rejectWithValue(errorMessage);
    }
  }
);
