"use client";

import { useState, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Check<PERSON>ircle2, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock } from "lucide-react";
import TableHeader from "../common/TableHeader";
import StatusBadge from "../common/StatusBadge";
import Pagination from "../common/Pagination";
import TableActions from "../common/TableActions";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import { getInitials } from "@/utils/methods";
import { formatTimeAgo } from "@/utils/constants/dateFormats";
import PageLoader from "../common/PageLoader";
import styles from "./SyncBadges.module.css";
import { cn } from "@/utils/methods/cn";

export default function ClientTable({
  searchTerm = LISTING_CONSTANTS.SEARCH.ALL,
  statusFilter = LISTING_CONSTANTS.STATUS_FILTER.ALL,
  organizations = [],
  loading = false,
  error = null,
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [loadingStates, setLoadingStates] = useState({}); // Track loading for each action per client
  const itemsPerPage = LISTING_CONSTANTS.PAGINATION.ITEMS_PER_PAGE;

  const router = useRouter();

  // Replace clientsData with organizations
  const filteredClients = (organizations ?? []).filter((client) => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch =
      !!client.name?.toLowerCase()?.includes(searchLower) ||
      !!client.email?.toLowerCase()?.includes(searchLower) ||
      !!client.schema_name?.toLowerCase()?.includes(searchLower);
    const matchesStatus =
      statusFilter === "All Status" ||
      (statusFilter === "Active" && client.is_active) ||
      (statusFilter === "Inactive" && !client.is_active);
    return matchesSearch && matchesStatus;
  });

  // Sort functionality
  const sortedClients = useMemo(() => {
    if (!filteredClients || filteredClients.length === 0) return [];

    return [...filteredClients].sort((a, b) => {
      if (!sortConfig.key) return 0;

      // Handle null/undefined values
      const aVal = a[sortConfig.key];
      const bVal = b[sortConfig.key];

      if (!aVal && !bVal) return 0;
      if (!aVal) return 1;
      if (!bVal) return -1;

      const aValLower = String(aVal).toLowerCase();
      const bValLower = String(bVal).toLowerCase();

      if (sortConfig.direction === "asc") {
        return aValLower < bValLower ? -1 : aValLower > bValLower ? 1 : 0;
      } else {
        return aValLower > bValLower ? -1 : aValLower < bValLower ? 1 : 0;
      }
    });
  }, [filteredClients, sortConfig]);

  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(sortedClients.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedClients = sortedClients.slice(
      startIndex,
      startIndex + itemsPerPage
    );
    return { totalPages, startIndex, paginatedClients };
  }, [sortedClients, currentPage, itemsPerPage]);

  const { totalPages, startIndex, paginatedClients } = paginationData;

  const handleSort = useCallback((key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return (
        <svg
          className="w-4 h-4 ml-1 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
          />
        </svg>
      );
    }

    return sortConfig.direction === "asc" ? (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 15l7-7 7 7"
        />
      </svg>
    ) : (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    );
  };

  const renderStatusBadge = (status) => {
    return <StatusBadge status={status} variant="outline" />;
  };

  /**
   * Determine sync status based on timestamp
   * Status hierarchy:
   * - 🟢 Green (Synced): Has timestamp, shows relative time
   * - 🟡 Yellow (Syncing): Recently synced (< 5 min) - shows spinning animation
   * - 🔴 Red (Not Synced): No timestamp available
   * 
   * @param {string|null} timestamp - Last sync timestamp
   * @returns {Object} Status object with type, message, and CSS module classes
   */
  const getSyncStatus = useCallback((timestamp) => {
    if (!timestamp) {
      return {
        status: "not_synced",
        icon: AlertCircle,
        label: "Not Synced",
        badgeClass: styles.syncBadgeNotSynced,
        textClass: styles.textNotSynced,
        iconClass: styles.iconNotSynced,
      };
    }

    const syncDate = new Date(timestamp);
    const now = new Date();
    const diffInMs = now - syncDate;
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    // If synced within last 5 minutes, show "syncing" animation to indicate freshness
    if (diffInMinutes < 5) {
      return {
        status: "syncing",
        icon: Loader2,
        label: "Syncing",
        badgeClass: styles.syncBadgeSyncing,
        textClass: styles.textSyncing,
        iconClass: styles.iconSyncing,
        isSpinning: true,
      };
    }

    // Otherwise, it's successfully synced (green)
    return {
      status: "synced",
      icon: CheckCircle2,
      label: "Synced",
      badgeClass: styles.syncBadgeSynced,
      textClass: styles.textSynced,
      iconClass: styles.iconSynced,
    };
  }, []);

  /**
   * Format full timestamp for tooltip
   */
  const formatFullTimestamp = useCallback((timestamp) => {
    if (!timestamp) return "Never synced";
    try {
      const date = new Date(timestamp);
      return date.toLocaleString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return "Invalid date";
    }
  }, []);

  /**
   * Render modern, icon-based sync status badges
   * Each badge shows: Icon + Category name • Status text
   */
  const renderSyncBadges = useCallback((client) => {
    const syncData = [
      {
        name: "Operational",
        timestamp: client.sikka_last_synced_at,
        key: "operational",
      },
      {
        name: "Payroll",
        timestamp: client.adp_last_synced_at,
        key: "payroll",
      },
      {
        name: "Financial",
        timestamp: client.qb_last_synced_at,
        key: "financial",
      },
    ];

    return (
      <div className={styles.syncBadgesContainer}>
        {syncData.map((item) => {
          const status = getSyncStatus(item.timestamp);
          const Icon = status.icon;
          const relativeTime = item.timestamp
            ? formatTimeAgo(item.timestamp)
            : null;
          
          // Show relative time if available, otherwise show status label
          // For syncing status, show relative time with spinning icon
          const displayText = relativeTime || status.label;
          
          // Enhanced tooltip with full timestamp and sync info
          const tooltipText = item.timestamp
            ? `Last synced: ${formatFullTimestamp(item.timestamp)}\nClick to sync now (coming soon)`
            : "Not synced yet\nClick to sync now (coming soon)";

          // Map category to specific CSS classes
          const categoryBadgeClass = {
            operational: styles.syncBadgeOperational,
            payroll: styles.syncBadgePayroll,
            financial: styles.syncBadgeFinancial,
          }[item.key] || status.badgeClass;

          const categoryIconClass = {
            operational: styles.iconOperational,
            payroll: styles.iconPayroll,
            financial: styles.iconFinancial,
          }[item.key] || status.iconClass;

          const categoryTextClass = {
            operational: styles.textOperational,
            payroll: styles.textPayroll,
            financial: styles.textFinancial,
          }[item.key] || status.textClass;

          return (
            <div
              key={item.key}
              className={cn(styles.syncBadge, categoryBadgeClass)}
              title={tooltipText}
              onClick={() => {
                // Future: Trigger manual sync
                // For now, just log
                console.log(`Sync requested for ${item.name}`, item.timestamp);
              }}
            >
              {/* Status Icon - Use category icon or status icon */}
              <Icon
                className={cn(
                  styles.syncIcon,
                  item.timestamp ? categoryIconClass : status.iconClass,
                  status.isSpinning && styles.syncIconSpinning
                )}
              />

              {/* Category name and status */}
              <div className={styles.badgeContent}>
                <span className={cn(styles.categoryName, categoryTextClass)}>
                  {item.name}
                </span>
                {item.timestamp && (
                  <>
                    <span className={cn(styles.separator, categoryTextClass)}>
                      •
                    </span>
                    <span className={cn(styles.statusText, categoryTextClass)}>
                      {displayText}
                    </span>
                  </>
                )}
                {!item.timestamp && (
                  <span className={cn(styles.statusText, categoryTextClass)}>
                    {displayText}
                  </span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  }, [getSyncStatus, formatFullTimestamp]);

  const handleViewDetails = useCallback((client) => {
    // Set loading state
    setLoadingStates((prev) => ({
      ...prev,
      [`${client.id}_view`]: true,
    }));

    const email = (client.email || "").toLowerCase();
    let dashboardType = "chp";
    if (email === "<EMAIL>") {
      dashboardType = "dental";
    } else if (email === "<EMAIL>") {
      dashboardType = "chp";
    }
    const organizationName = encodeURIComponent(client.name);
    
    // Navigate immediately (router.push is async but starts immediately)
    router.push(`/dashboard?dashboard=${dashboardType}&organization=${organizationName}&email=${encodeURIComponent(client.email)}`);
    
    // Note: We don't clear loading state as navigation will unmount the component
  }, [router]);

  const handleBookkeeping = useCallback((client) => {
    // Handle both cases: client object or clientId string
    const clientId = typeof client === 'object' && client?.id ? client.id : client;
    
    // Set loading state
    setLoadingStates((prev) => ({
      ...prev,
      [`${clientId}_bookkeeping`]: true,
    }));

    router.push(`/book-closure/${clientId}`);
    
    // Note: We don't clear loading state as navigation will unmount the component
  }, [router]);

  return (
    <div className="rounded-xl border border-gray-100 overflow-hidden">
      {/* Loading / Error */}
      {loading ? (
        <PageLoader message="Loading clients..." />
      ) : error ? (
        <div className="p-6 text-red-500 font-bold text-center">
          {typeof error === "string" ? error : "Failed to load clients."}
        </div>
      ) : paginatedClients.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 px-4">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Clients Found
          </h3>
          <p className="text-sm text-gray-500 text-center max-w-sm">
            {searchTerm !== LISTING_CONSTANTS.SEARCH.ALL || statusFilter !== LISTING_CONSTANTS.STATUS_FILTER.ALL
              ? "Try adjusting your search or filters to find clients."
              : "No clients have been added yet. Start by adding your first client."}
          </p>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto rounded-t-xl overflow-hidden">
            <table className="w-full table-fixed align-middle">
              <colgroup>
                <col style={{ width: "25%" }} />
                <col style={{ width: "12%" }} />
                <col style={{ width: "25%" }} />
                <col style={{ width: "38%" }} />
              </colgroup>
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  <th
                    className="px-6 py-4 text-left text-xs uppercase text-gray-500 font-medium tracking-wide cursor-pointer hover:text-blue-600 transition-colors duration-200 align-middle"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      {LISTING_CONSTANTS.TABLE_HEADERS.CLIENT_NAME}
                      {getSortIcon("name")}
                    </div>
                  </th>
                  <th className="px-6 py-4 text-left text-xs uppercase text-gray-500 font-medium tracking-wide align-middle">
                    {LISTING_CONSTANTS.TABLE_HEADERS.STATUS}
                  </th>
                  <th className="px-6 py-4 text-left text-xs uppercase text-gray-500 font-medium tracking-wide align-middle">
                    Last sync
                  </th>
                  <th className="px-6 py-4 text-left text-xs uppercase text-gray-500 font-medium tracking-wide align-middle">
                    {LISTING_CONSTANTS.TABLE_HEADERS.ACTIONS}
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedClients.map((client, index) => (
                  <tr
                    key={client.id}
                    className={`align-middle transition-all duration-200 hover:bg-gray-50 border-b ${
                      index === paginatedClients.length - 1
                        ? "border-b-0"
                        : "border-gray-200"
                    }`}
                    onMouseEnter={() => setHoveredRow(client.id)}
                    onMouseLeave={() => setHoveredRow(null)}
                  >
                    <td className="px-6 py-4 min-w-0 align-middle">
                      <div className="flex items-center gap-3 min-w-0">
                        <div
                          className={`w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full flex items-center justify-center transition-all duration-300 flex-shrink-0 ${
                            hoveredRow === client.id ? "scale-110" : ""
                          }`}
                        >
                          <span className="text-white text-sm font-bold">
                            {getInitials(client.name)}
                          </span>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="text-sm font-semibold text-gray-900 transition-colors duration-200 truncate">
                            {client.name}
                          </div>
                          <div className="text-xs text-gray-500 truncate mt-0.5">
                            {client.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 align-middle">
                      {renderStatusBadge(
                        client.is_active ? "Active" : "Inactive"
                      )}
                    </td>
                    <td className="px-6 py-4 align-middle">{renderSyncBadges(client)}</td>
                    <td className="px-6 py-4 align-middle">
                      <div
                        className={`transition-all duration-300 ${
                          hoveredRow === client.id
                            ? LISTING_CONSTANTS.TABLE_STYLING.ACTION_HOVER
                            : ""
                        }`}
                      >
                        <TableActions
                          item={client}
                          actions={[
                            {
                              label: LISTING_CONSTANTS.ACTIONS.VIEW_DETAILS,
                              variant: "outline",
                              onClick: handleViewDetails,
                              loading: loadingStates[`${client.id}_view`] || false,
                              icon: "view",
                            },
                            {
                              label:
                                LISTING_CONSTANTS.ACTIONS.SUBMIT_BOOK_CLOSURE,
                              variant: "default",
                              onClick: handleBookkeeping,
                              loading: loadingStates[`${client.id}_bookkeeping`] || false,
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            totalItems={filteredClients.length}
            startIndex={startIndex}
            endIndex={startIndex + itemsPerPage}
          />
        </>
      )}
    </div>
  );
}
