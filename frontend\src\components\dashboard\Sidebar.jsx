import { useEffect, useState, useCallback, useMemo, memo } from "react";
import { But<PERSON> } from "../ui/button";
import { ArrowLeft, <PERSON>rk<PERSON>, User, ChevronDown } from "lucide-react";
import LogoutButton from "../ui/logout-button";
import { useRouter } from "next/navigation";
import tokenStorage from "@/lib/tokenStorage";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import "@/styles/sidebar.css";

const Sidebar = memo(function Sidebar({
  months,
  setMonths,
  isFinancialSelected,
  setIsFinancialSelected,
  isOperationsSelected,
  setIsOperationsSelected,
  isPayrollSelected,
  setIsPayrollSelected,
  availableMonths,
  onDownload,
  onGenAIClick = () => {},
  userRole,
  selectedDashboard,
  hideAllOptionsExceptBack = false,
}) {
  const router = useRouter();
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    const user = tokenStorage.getUserData();
    setUserData(user);
  }, []);

  const handleBack = useCallback(() => {
    router.push("/listing");
  }, [router]);

  const handleCFOInsightsClick = useCallback(() => {
    onGenAIClick?.();
  }, [onGenAIClick]);

  const handleUserProfileClick = useCallback(() => {
    router.push("/profile");
  }, [router]);

  // Memoized helper functions for tab-specific months
  const getOperationsMonths = useCallback(() => {
    const lowerEmail = (userData?.email || "").toLowerCase();
    const isChp =
      selectedDashboard === "chp" || lowerEmail === "<EMAIL>";
    const isDental =
      selectedDashboard === "dental" ||
      lowerEmail === "<EMAIL>";

    if ((isChp || isDental) && userRole !== "admin") {
      return ["June"];
    }
    return availableMonths;
  }, [userData?.email, selectedDashboard, userRole, availableMonths]);

  const getPayrollMonths = useCallback(() => {
    const lowerEmail = (userData?.email || "").toLowerCase();
    const isChp =
      selectedDashboard === "chp" || lowerEmail === "<EMAIL>";
    const isDental =
      selectedDashboard === "dental" ||
      lowerEmail === "<EMAIL>";

    if ((isChp || isDental) && userRole !== "admin") {
      return ["June", "July", "August"];
    }
    return availableMonths;
  }, [userData?.email, selectedDashboard, userRole, availableMonths]);

  // Reusable accordion section component
  const AccordionSection = ({
    title,
    isOpen,
    onToggle,
    months: sectionMonths,
  }) => (
    <div className="w-full flex flex-col gap-2 accordion-section">
      <button
        type="button"
        aria-expanded={isOpen}
        onClick={onToggle}
        className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between focus:outline-none border-none text-xs sm:text-sm
          transition-all duration-300 ease-out
          ${
            isOpen
              ? "bg-white/10 shadow-md scale-100"
              : "bg-transparent hover:bg-white/10 hover:scale-105"
          }`}
      >
        <span className="text-white font-medium">{title}</span>
        <ChevronDown
          className={`w-4 h-4 text-white/70 transition-all duration-300 ease-out ${
            isOpen ? "rotate-180 scale-110" : "rotate-0 scale-100"
          }`}
        />
      </button>
      <div
        className={`overflow-hidden transition-all duration-500 ease-out ${
          isOpen
            ? "max-h-60 opacity-100"
            : "max-h-0 opacity-0 pointer-events-none"
        }`}
      >
        <div className="pl-3 sm:pl-4 py-1 space-y-1">
          {sectionMonths.map((month, index) => {
            const currentYear = new Date().getFullYear();
            const yearSuffix = currentYear.toString().slice(-2);
            const displayMonth =
              title === DASHBOARD_CONSTANTS.SIDEBAR.FINANCIAL_TAB
                ? `${month}-${yearSuffix}`
                : `${month}-25`;

            return (
              <div
                key={month}
                className={`transition-all duration-300 ease-out ${
                  isOpen
                    ? "opacity-100 translate-x-0"
                    : "opacity-0 -translate-x-2"
                }`}
                style={{
                  transitionDelay: isOpen ? `${index * 50}ms` : "0ms",
                }}
              >
                <Button
                  variant={months === month ? "secondary" : "ghost"}
                  className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2
                    transition-all duration-200 ease-out
                    ${
                      months === month
                        ? "font-bold bg-white/20 shadow-md scale-100 ring-2 ring-white/30"
                        : "hover:bg-white/10 hover:scale-105 hover:shadow-sm"
                    }`}
                  onClick={() => setMonths(month)}
                >
                  {displayMonth}
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  return (
    <div
      className="flex flex-col gap-2 sm:gap-3 w-full lg:w-56 xl:w-64 p-3 sm:p-4 lg:p-5 shadow-md h-full overflow-y-auto custom-scrollbar"
      style={{ backgroundColor: DASHBOARD_CONSTANTS.COLORS.PRIMARY }}
    >
      {/* Back Button */}
      <Button
        variant="default"
        leftIcon={<ArrowLeft className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />}
        onClick={handleBack}
        className="w-full text-xs sm:text-sm border-none focus:outline-none focus:ring-2 focus:ring-white/40 py-2 px-3 transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg active:scale-95"
      >
        <span className="hidden sm:inline">
          {DASHBOARD_CONSTANTS.SIDEBAR.BACK_BUTTON_TEXT}
        </span>
        <span className="sm:hidden">Back</span>
      </Button>

      {/* Main Content */}
      {!hideAllOptionsExceptBack && (
        <>
          {/* Download Button */}
          <Button
            variant="default"
            onClick={onDownload}
            className="w-full text-xs sm:text-sm border-none focus:outline-none focus:ring-2 focus:ring-white/40 py-2 px-3 transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg active:scale-95"
          >
            <span className="hidden sm:inline">
              {DASHBOARD_CONSTANTS.SIDEBAR.DOWNLOAD_BUTTON_TEXT}
            </span>
            <span className="sm:hidden">Download</span>
          </Button>

          {/* Financial Tab */}
          <AccordionSection
            title={DASHBOARD_CONSTANTS.SIDEBAR.FINANCIAL_TAB}
            isOpen={isFinancialSelected}
            onToggle={() => {
              setIsFinancialSelected(!isFinancialSelected);
              if (!isFinancialSelected) {
                setIsOperationsSelected(false);
                setMonths("June");
              }
            }}
            months={availableMonths}
          />

          <div className="w-full flex flex-col gap-2 accordion-section">
            <Button
              type="button"
              aria-expanded={isOperationsSelected}
              onClick={() => {
                setIsOperationsSelected(!isOperationsSelected);
                // Close Financial and Payroll when Operations is opened
                if (!isOperationsSelected) {
                  setIsFinancialSelected(false);
                  setIsPayrollSelected(false);
                  // Don't set months here - let user click on June-25 to load PDF
                }
              }}
              className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between focus:outline-none border-none text-xs sm:text-sm
              transition-all duration-300 ease-out
              ${
                isOperationsSelected
                  ? `shadow-md scale-100 bg-white/10`
                  : `bg-transparent hover:bg-white/10 hover:scale-105`
              }`}
            >
              <span className="font-medium">{DASHBOARD_CONSTANTS.SIDEBAR.OPERATIONS_TAB}</span>
              <span
                className={`ml-2 text-xs transition-all duration-300 ease-out inline-block ${
                  isOperationsSelected
                    ? "text-white rotate-180 scale-110"
                    : "text-white/70 rotate-0 scale-100"
                }`}
              >
                ▲
              </span>
            </Button>
            <div
              className={`overflow-hidden transition-all duration-500 ease-out ${
                isOperationsSelected
                  ? "max-h-60 opacity-100"
                  : "max-h-0 opacity-0 pointer-events-none"
              }`}
            >
              <div className="pl-3 sm:pl-4 py-1 space-y-1">
                {getOperationsMonths().map((month, index) => (
                  <div
                    key={month}
                    className={`transition-all duration-300 ease-out ${
                      isOperationsSelected
                        ? "opacity-100 translate-x-0"
                        : "opacity-0 -translate-x-2"
                    }`}
                    style={{
                      transitionDelay: isOperationsSelected ? `${index * 50}ms` : "0ms",
                    }}
                  >
                    <Button
                      variant={months === month ? "secondary" : "ghost"}
                      className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2
                        transition-all duration-200 ease-out
                        ${
                          months === month
                            ? `font-bold bg-white/20 shadow-md scale-100 ring-2 ring-white/30`
                            : "hover:bg-white/10 hover:scale-105 hover:shadow-sm"
                        }`}
                      onClick={() => setMonths(month)}
                    >
                      {month}-25
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col gap-2 accordion-section">
            <Button
              type="button"
              aria-expanded={isPayrollSelected}
              onClick={() => {
                setIsPayrollSelected(!isPayrollSelected);
                // Close Financial and Operations when Payroll is opened
                if (!isPayrollSelected) {
                  setIsFinancialSelected(false);
                  setIsOperationsSelected(false);
                  setMonths("June");
                }
              }}
              className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between focus:outline-none border-none text-xs sm:text-sm
              transition-all duration-300 ease-out
              ${
                isPayrollSelected
                  ? `shadow-md scale-100 bg-white/10`
                  : `bg-transparent hover:bg-white/10 hover:scale-105`
              }`}
            >
              <span className="font-medium">{DASHBOARD_CONSTANTS.SIDEBAR.PAYROLL_TAB}</span>
              <span
                className={`ml-2 text-xs transition-all duration-300 ease-out inline-block ${
                  isPayrollSelected
                    ? "text-white rotate-180 scale-110"
                    : "text-white/70 rotate-0 scale-100"
                }`}
              >
                ▲
              </span>
            </Button>
            <div
              className={`overflow-hidden transition-all duration-500 ease-out ${
                isPayrollSelected
                  ? "max-h-60 opacity-100"
                  : "max-h-0 opacity-0 pointer-events-none"
              }`}
            >
              <div className="pl-3 sm:pl-4 py-1 space-y-1">
                {getPayrollMonths().map((month, index) => (
                  <div
                    key={month}
                    className={`transition-all duration-300 ease-out ${
                      isPayrollSelected
                        ? "opacity-100 translate-x-0"
                        : "opacity-0 -translate-x-2"
                    }`}
                    style={{
                      transitionDelay: isPayrollSelected ? `${index * 50}ms` : "0ms",
                    }}
                  >
                    <Button
                      variant={months === month ? "secondary" : "ghost"}
                      className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2
                        transition-all duration-200 ease-out
                        ${
                          months === month
                            ? `font-bold bg-white/20 shadow-md scale-100 ring-2 ring-white/30`
                            : "hover:bg-white/10 hover:scale-105 hover:shadow-sm"
                        }`}
                      onClick={() => setMonths(month)}
                    >
                      {month}-25
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-auto">
            <Button
              onClick={handleCFOInsightsClick}
              variant="outline"
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-2xl transition-all duration-300 ease-out mb-4 text-xs sm:text-sm py-2 px-3 hover:scale-105 active:scale-95 group"
            >
              <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 transition-transform duration-300 group-hover:rotate-12 group-hover:scale-110" />
              <span className="hidden sm:inline">CFO Insights</span>
              <span className="sm:hidden">AI</span>
            </Button>
          </div>

          <div className="p-2 sm:p-3 border-t border-white/20 w-full">
            {/* User Info Section */}
            <button
              onClick={handleUserProfileClick}
              className="bg-white/10 rounded-lg p-3 mb-3 w-full text-left hover:bg-white/20 transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-white/40 hover:scale-105 hover:shadow-md active:scale-95"
            >
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-white text-xs font-semibold truncate">
                    {userData?.name || "User"}
                  </div>
                  <div className="text-white/70 text-xs truncate">
                    {userData?.email || "<EMAIL>"}
                  </div>
                  <div className="text-white/60 text-xs">
                    {userData?.role || "Role"}
                  </div>
                </div>
              </div>
            </button>
            <LogoutButton />
          </div>
        </>
      )}
    </div>
  );
});

export default Sidebar;
