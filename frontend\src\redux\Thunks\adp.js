import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";
import { createAsyncThunk } from "@reduxjs/toolkit";

const axios = api(SERVICE_PORTS.ADP);

export const uploadAdpFile = createAsyncThunk(
  "adp/uploadFile",
  async ({ file, schemaName, orgId }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("schemaName", schemaName);
      formData.append("orgId", orgId);

      return await axios.post("/adp/sync", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);
