import {
  SIKKA_API,
  SIKKA_MESSAGES,
  LOG_ACTIONS,
  ERROR_MESSAGES,
  MODEL_FIELDS,
  METHOD_TYPES,
} from "../utils/constants.util.js";
import {
  sikkaApiCall,
  createSikkaHeaders,
  validateAndThrowIfInvalid,
  findPracticeByOfficeId,
  validateAndGetPracticeCredentials,
  createRequestKeyPayload,
  formatRequestKeyResponse,
} from "../utils/methods.util.js";
import { createLogger } from "../utils/logger.util.js";
import { sikkaRepository } from "../repositories/sikka.repository.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import sequelize from "../../config/postgres.js";

const logger = createLogger("sikka-service");
const app_id = process.env.SIKKA_APP_ID;
const app_key = process.env.SIKKA_APP_KEY;

/**
 * Generate and store a new request key
 * This method encapsulates the complete business logic for request key generation
 * @param {string} office_id - Office ID
 * @returns {Object} Created request key record
 */
export const generateRequestKey = async (office_id, schemaName) => {
  logger.info(LOG_ACTIONS.REQUESTING_KEY);

  // Validate credentials
  validateAndThrowIfInvalid(app_id, app_key);

  // Get authorized practices
  const practicesData = await getAuthorizedPractices(app_id, app_key);
  if (!practicesData.data || practicesData.data.items.length === 0) {
    throw new Error(SIKKA_MESSAGES.NO_AUTHORIZED_PRACTICES);
  }

  // Find and validate practice
  const practice = findPracticeByOfficeId(practicesData.data.items, office_id);
  if (!practice) {
    throw new Error(SIKKA_MESSAGES.NO_AUTHORIZED_PRACTICES);
  }
  const secret_key = validateAndGetPracticeCredentials(practice);

  // Create request key payload and call API
  const requestKeyPayload = createRequestKeyPayload({
    office_id,
    secret_key,
    app_id,
    app_key,
  });
  const requestKeyData = await callRequestKeyAPI(requestKeyPayload);
  const formattedData = formatRequestKeyResponse(requestKeyData);
  const requestKeyRecord = await storeRequestKey(formattedData, schemaName);

  logger.info(LOG_ACTIONS.REQUEST_KEY_SUCCESS, {
    [MODEL_FIELDS.OFFICE_ID]: office_id,
  });

  return requestKeyRecord;
};

/**
 * Get authorized practices from Sikka API
 * @param {string} app_id - Application ID
 * @param {string} app_key - Application Key
 * @returns {Object} Response with practices data
 */
export const getAuthorizedPractices = async (app_id, app_key) => {
  const headers = createSikkaHeaders(app_id, app_key);

  return await sikkaApiCall(
    METHOD_TYPES.GET,
    SIKKA_API.ENDPOINTS.AUTHORIZED_PRACTICES,
    {
      headers,
      logContext: { [MODEL_FIELDS.APP_ID]: app_id },
      successMessage: LOG_ACTIONS.PRACTICES_FETCHED,
      errorMessage: ERROR_MESSAGES.API_CALL_FAILED_FOR,
    }
  ).then((data) => ({
    message: SIKKA_MESSAGES.AUTHORIZED_PRACTICES_SUCCESS,
    data,
  }));
};

/**
 * Call Sikka request key API
 * @param {Object} requestData - Request payload
 * @returns {Object} Response with request key data
 */
export const callRequestKeyAPI = (requestData) => {
  return sikkaApiCall(METHOD_TYPES.POST, SIKKA_API.ENDPOINTS.REQUEST_KEY, {
    data: requestData,
    successMessage: LOG_ACTIONS.REQUEST_KEY_SUCCESS,
    errorMessage: ERROR_MESSAGES.REQUEST_KEY_API_FAILED,
  });
};

/**
 * Store Sikka request key data in the database
 * @param {Object} params - { request_key, start_time, end_time, expires_in }
 * @returns {Promise<Object>} Created record
 */
export const storeRequestKey = async (params, schemaName) => {
  const existingRecord = await sikkaRepository.findRequestKey({
    office_id: params.office_id,
  }, schemaName);
  if (existingRecord) {
    return await sikkaRepository.updateRequestKey(existingRecord.id, params, schemaName);
  } else {
    return await sikkaRepository.createRequestKey(params, schemaName);
  }
};

/**
 * Get request key from database
 * @param {string} office_id - Office ID
 * @param {string} schemaName - Schema name
 * @returns {Object} Request key data
 */
export const getRequestKey = async (office_id, schemaName) => {
  const requestKeyRecord = await sikkaRepository.findRequestKey({ office_id, schemaName });

  return (
    (requestKeyRecord && {
      request_key: requestKeyRecord.get(MODEL_FIELDS.REQUEST_KEY),
      end_time: requestKeyRecord.get(MODEL_FIELDS.END_TIME),
    }) ||
    {}
  );
};

/**
 * Fetch information about all Sequelize models in the tables directory
 * @returns {Object} Models information including table names, columns, types, and associations
 */
export const fetchAllModelsInfo = async () => {
  try {
    logger.info(LOG_ACTIONS.GETTING_MODELS_INFO);

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const modelsDir = path.join(__dirname, "../models/tables");

    if (!fs.existsSync(modelsDir)) {
      throw new Error(SIKKA_MESSAGES.MODELS_DIRECTORY_NOT_FOUND);
    }

    const modelFiles = fs
      .readdirSync(modelsDir)
      .filter((file) => file.endsWith(".model.js") || file.endsWith(".js"))
      .map((file) => file.replace(/\.(model\.)?js$/, ""));

    const modelsInfo = {};

    for (const modelFileName of modelFiles) {
      try {
        const modelPath = path.join(
          modelsDir,
          `${modelFileName}.${
            modelFileName.includes(".model") ? "js" : "model.js"
          }`
        );

        // Try both naming conventions
        let actualPath = modelPath;
        if (!fs.existsSync(modelPath)) {
          const altPath = path.join(modelsDir, `${modelFileName}.js`);
          if (fs.existsSync(altPath)) {
            actualPath = altPath;
          }
        }

        const modelModule = await import(`file://${actualPath}`);
        const ModelFunction = modelModule.default;

        if (typeof ModelFunction === "function") {
          const model = ModelFunction(sequelize);

          const modelInfo = {
            tableName: model.tableName || model.name,
            modelName: model.name,
            columns: {},
            associations: {},
            indexes: model.options?.indexes || [],
            timestamps: model.options?.timestamps || false,
            createdAt: model.options?.createdAt || "createdAt",
            updatedAt: model.options?.updatedAt || "updatedAt",
          };

          // Extract column information
          Object.keys(model.rawAttributes).forEach((columnName) => {
            const attribute = model.rawAttributes[columnName];
            modelInfo.columns[columnName] = {
              type:
                attribute.type?.constructor?.name ||
                attribute.type?.toString() ||
                "UNKNOWN",
              allowNull: attribute.allowNull !== false,
              defaultValue: attribute.defaultValue,
              primaryKey: attribute.primaryKey || false,
              autoIncrement: attribute.autoIncrement || false,
              unique: attribute.unique || false,
              comment: attribute.comment || null,
            };
          });

          // Extract associations
          if (model.associations) {
            Object.keys(model.associations).forEach((associationName) => {
              const association = model.associations[associationName];
              modelInfo.associations[associationName] = {
                type: association.associationType,
                target: association.target?.name,
                foreignKey: association.foreignKey,
                sourceKey: association.sourceKey,
                as: association.as,
              };
            });
          }

          modelsInfo[modelFileName] = modelInfo;
        }
      } catch (error) {
        logger.error(`Error processing model ${modelFileName}:`, error.message);
        modelsInfo[modelFileName] = {
          error: `Failed to load model: ${error.message}`,
          tableName: null,
          columns: {},
          associations: {},
        };
      }
    }

    return {
      totalModels: Object.keys(modelsInfo).length,
      models: modelsInfo,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error(LOG_ACTIONS.ERROR_GETTING_MODELS_INFO, {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};
