const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

/**
 * Parse environment variable value to appropriate type
 * @param {string} value - Environment variable value
 * @param {string} type - Target type ('string', 'number', 'boolean')
 * @param {*} defaultValue - Default value if parsing fails
 * @returns {*} Parsed value
 */
const parseEnvValue = (value, type, defaultValue) => {
  if (!value) return defaultValue;

  switch (type) {
    case "number":
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
    case "boolean":
      return value.toLowerCase() === "true";
    default:
      return value;
  }
};

/**
 * Helper function to get password and strip quotes
 */
const getPassword = (password) => {
  if (!password) return '';
  return String(password).replace(/^["']|["']$/g, '');
};

/**
 * Get database configuration based on environment
 * @returns {Object} Database configuration object
 */
const getDatabaseConfig = () => {
  // Get database environment variables
  const dbHost = process.env.DB_HOST;
  const dbName = process.env.DB_NAME;
  const dbUser = process.env.DB_USER;
  const dbPass = process.env.DB_PASS;
  const dbSsl = process.env.DB_SSL;
  const dbPort = parseEnvValue(process.env.DB_PORT, "number", 5432);

  // Process password (strip quotes)
  const password = getPassword(dbPass);

  // Determine SSL configuration
  // If DB_SSL is explicitly set, use it; otherwise auto-detect based on host
  // Enable SSL for AWS RDS or any remote host (not localhost)
  const isRemoteHost = dbHost && !dbHost.includes('localhost') && !dbHost.includes('127.0.0.1');
  const dbSslEnabled = typeof dbSsl !== "undefined"
    ? parseEnvValue(dbSsl, "boolean", true)
    : isRemoteHost;

  return {
    database: dbName || "cpa_dashboard",
    username: dbUser || "postgres",
    password: password,
    host: dbHost || "localhost",
    port: dbPort,
    dialect: "postgres",
    logging: process.env.NODE_ENV === "development" ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    dialectOptions: dbSslEnabled
      ? {
          ssl: {
            require: true,
            rejectUnauthorized: false,
          },
        }
      : {},
    isLocal: !isRemoteHost,
  };
};

// Get database configuration
const dbConfig = getDatabaseConfig();

module.exports = {
  development: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
  test: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
  production: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
};