"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { message } from "antd";
import axiosInstance from "@/lib/axiosConfig";
import { MESSAGES } from "@/utils/constants";
import { useSelector } from "react-redux";

const QuickBooksCallback = () => {
  const router = useRouter();
  const user = useSelector((state) => state.auth.user);
  const apiCallMade = useRef(false);

  useEffect(() => {
    const handleAuthCode = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get("code");
      const realmId = urlParams.get("realmId");

      // Only proceed if we have all required data and haven't made the API call yet
      if (code && realmId && user?.organization_id && !apiCallMade.current) {
        apiCallMade.current = true;
        try {
          // Retrieve company_name and email from local storage
          const company_name = localStorage.getItem("company_name") || "";
          const email = localStorage.getItem("email") || "";

          // Send GET request to backend with code, realmId, company_name, email, and organization_id
          const response = await axiosInstance.get(
            `/api/quickbooks/add?code=${code}&realmId=${realmId}&company_name=${company_name}&email=${email}&organization_id=${user.organization_id}`
          );

          // Clear local storage after API response
          localStorage.removeItem("company_name");
          localStorage.removeItem("email");

          // Show success or info message based on response
          if (response.data.status) {
            message.success(
              response.data.message || MESSAGES.QUICKBOOKS_SUCCESS
            );
          } else {
            message.info(response.data.message || MESSAGES.QUICKBOOKS_ERROR);
          }
        } catch (error) {
          message.error(
            error.response?.data?.message || MESSAGES.QUICKBOOKS_ERROR
          );
        } finally {
          // Redirect to /apps in all cases (success or failure)
          router.push("/apps");
        }
      }
    };

    // Execute the auth code handling
    handleAuthCode();
  }, [router, user?.organization_id]);

  return null;
};

export default QuickBooksCallback;
