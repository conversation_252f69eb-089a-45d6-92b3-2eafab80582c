import { DataTypes } from 'sequelize';

/**
 * Financial Service Models
 * These models will be created in organization-specific schemas
 */

/**
 * Accounts Model - Manages chart of accounts
 */
export const AccountsModel = (sequelize, schemaName) => {
  const Accounts = sequelize.define(
    'accounts',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      account_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: true,
      },
      account_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      account_type: {
        type: DataTypes.ENUM('asset', 'liability', 'equity', 'revenue', 'expense'),
        allowNull: false,
      },
      parent_account_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      balance: {
        type: DataTypes.DECIMAL(15, 2),
        defaultValue: 0.00,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'accounts',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_accounts_code', fields: ['account_code'] },
        { name: 'idx_accounts_type', fields: ['account_type'] },
        { name: 'idx_accounts_parent', fields: ['parent_account_id'] },
      ],
    }
  );

  return Accounts;
};

/**
 * Transactions Model - Manages financial transactions
 */
export const TransactionsModel = (sequelize, schemaName) => {
  const Transactions = sequelize.define(
    'transactions',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      transaction_number: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      transaction_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      reference_number: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      total_amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('draft', 'posted', 'cancelled'),
        defaultValue: 'draft',
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'transactions',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_transactions_number', fields: ['transaction_number'] },
        { name: 'idx_transactions_date', fields: ['transaction_date'] },
        { name: 'idx_transactions_status', fields: ['status'] },
      ],
    }
  );

  return Transactions;
};

/**
 * Journal Entries Model - Manages double-entry bookkeeping
 */
export const JournalEntriesModel = (sequelize, schemaName) => {
  const JournalEntries = sequelize.define(
    'journal_entries',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      transaction_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'transactions',
          key: 'id',
        },
      },
      account_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      debit_amount: {
        type: DataTypes.DECIMAL(15, 2),
        defaultValue: 0.00,
      },
      credit_amount: {
        type: DataTypes.DECIMAL(15, 2),
        defaultValue: 0.00,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 'journal_entries',
      schema: schemaName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { name: 'idx_journal_transaction', fields: ['transaction_id'] },
        { name: 'idx_journal_account', fields: ['account_id'] },
        { name: 'idx_journal_debit', fields: ['debit_amount'] },
        { name: 'idx_journal_credit', fields: ['credit_amount'] },
      ],
    }
  );

  return JournalEntries;
};

// Export all financial models
export const FinancialModels = {
  AccountsModel,
  TransactionsModel,
  JournalEntriesModel,
};

export default FinancialModels;
