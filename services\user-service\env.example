# Database Configuration
DB_HOST=db_host
DB_PORT=5432
DB_NAME=postgres
DB_PASS=db_password
DB_USER=postgres
DB_DIALECT=postgres
DB_SSL=true

# ENVIRONMENT CONFIGURATION
NODE_ENV=development

# SERVER CONFIGURATION
PORT=3003
ALLOWED_ORIGINS=http://localhost:3000

# LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_TO_FILE=logs/user-service.log

# Comma-separated list of frontend origins (no trailing slash)
ALLOWED_ORIGINS=http://localhost:3000,https://19rdf6z7-3000.inc1.devtunnels.ms,https://your-production-frontend.com
