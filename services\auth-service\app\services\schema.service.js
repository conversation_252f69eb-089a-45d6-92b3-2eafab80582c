// Updated schema service for creating organization schemas with dynamic table creation
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { SCHEMA_MESSAGES } from "../utils/constants/schema.constants.js";
import { createOrganizationSchema } from "../utils/schema.utils.js";
import { createOrganizationTablesFromServices } from "./dynamic-table.service.js";
import { createServiceResponse } from "../utils/response.util.js";
import * as status from "../utils/status_code.utils.js";

const logger = createLogger(LOGGER_NAMES.SCHEMA_SERVICE);

/**
 * Create organization schema and tables based on selected services
 * @param {string} organizationName - The organization name
 * @param {Array} services - Array of service names
 * @returns {Promise<Object>} Service response
 */
export const createOrganizationSchemaAndTables = async (
  organizationName,
  services
) => {
  logger.info(
    `schemaService.createOrganizationSchemaAndTables - Creating schema and tables for: ${organizationName}`
  );

  try {
    // Validate inputs
    if (!organizationName || typeof organizationName !== "string") {
      throw new Error("Organization name is required and must be a string");
    }

    if (!Array.isArray(services) || services.length === 0) {
      throw new Error("Services array is required and must not be empty");
    }

    // Validate service names
    const validServices = ["financial", "operational", "payroll"];
    const invalidServices = services.filter(
      (service) => !validServices.includes(service)
    );

    if (invalidServices.length > 0) {
      throw new Error(
        `Invalid services: ${invalidServices.join(
          ", "
        )}. Valid services are: ${validServices.join(", ")}`
      );
    }

    // Step 1: Create the organization schema
    logger.info(
      `schemaService.createOrganizationSchemaAndTables - Creating schema for: ${organizationName}`
    );

    const schemaName = await createOrganizationSchema(organizationName);

    // Step 2: Create tables dynamically from service APIs
    logger.info(
      `schemaService.createOrganizationSchemaAndTables - Creating tables dynamically for services: ${services.join(
        ", "
      )}`
    );

    const tablesResult = await createOrganizationTablesFromServices(
      schemaName,
      services
    );

    if (tablesResult.success) {
      logger.info(
        `schemaService.createOrganizationSchemaAndTables - Successfully created schema and tables for: ${organizationName}`
      );

      return createServiceResponse(
        true,
        status.STATUS_CODE_CREATED,
        SCHEMA_MESSAGES.SCHEMA_AND_TABLES_CREATED_SUCCESSFULLY,
        {
          organizationName,
          schemaName,
          services: tablesResult.data.services,
          summary: tablesResult.data.summary,
          totalTables: tablesResult.data.summary.totalTablesCreated,
        }
      );
    } else {
      // Partial or complete failure
      const hasPartialSuccess =
        tablesResult.data?.summary?.totalTablesCreated > 0;

      logger.warn(
        `schemaService.createOrganizationSchemaAndTables - ${
          hasPartialSuccess ? "Partial" : "Complete"
        } failure for: ${organizationName}`
      );

      return createServiceResponse(
        hasPartialSuccess,
        hasPartialSuccess
          ? status.STATUS_CODE_PARTIAL_CONTENT
          : status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        hasPartialSuccess
          ? SCHEMA_MESSAGES.PARTIAL_SCHEMA_CREATION
          : SCHEMA_MESSAGES.SCHEMA_CREATION_FAILED,
        {
          organizationName,
          schemaName,
          services: tablesResult.data?.services || [],
          summary: tablesResult.data?.summary || {},
          error: tablesResult.error,
        }
      );
    }
  } catch (error) {
    logger.error(
      `schemaService.createOrganizationSchemaAndTables - Error: ${error.message}`
    );

    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      SCHEMA_MESSAGES.SCHEMA_CREATION_FAILED,
      null,
      error.message
    );
  }
};

/**
 * Get available services and their configuration
 * @returns {Object} Available services information
 */
export const getAvailableServices = () => {
  logger.info(
    "schemaService.getAvailableServices - Getting available services"
  );

  const services = {
    financial: {
      name: "Financial Service",
      description: "Financial management and accounting modules",
      endpoint: "/api/v1/models/info",
    },
    operational: {
      name: "Operational Service",
      description: "Project and resource management modules",
      endpoint: "/api/v1/models/info",
    },
    pms: {
      name: "Property Management System",
      description: "Property and tenant management modules",
      endpoint: "/api/v1/models/info",
    },
  };

  return createServiceResponse(
    true,
    status.STATUS_CODE_OK,
    SCHEMA_MESSAGES.SERVICES_RETRIEVED_SUCCESSFULLY,
    {
      services,
      totalServices: Object.keys(services).length,
      serviceNames: Object.keys(services),
    }
  );
};

/**
 * Validate service names
 * @param {Array} services - Array of service names to validate
 * @returns {Object} Validation result
 */
export const validateServices = (services) => {
  logger.debug(
    `schemaService.validateServices - Validating services: ${services?.join(
      ", "
    )}`
  );

  const validServices = ["financial", "operational", "payroll"];
  const errors = [];

  if (!Array.isArray(services)) {
    errors.push("Services must be an array");
  } else if (services.length === 0) {
    errors.push("At least one service must be specified");
  } else {
    const invalidServices = services.filter(
      (service) => !validServices.includes(service)
    );
    if (invalidServices.length > 0) {
      errors.push(
        `Invalid services: ${invalidServices.join(
          ", "
        )}. Valid services are: ${validServices.join(", ")}`
      );
    }

    const duplicateServices = services.filter(
      (service, index) => services.indexOf(service) !== index
    );
    if (duplicateServices.length > 0) {
      errors.push(
        `Duplicate services found: ${[...new Set(duplicateServices)].join(
          ", "
        )}`
      );
    }
  }

  const isValid = errors.length === 0;

  return {
    isValid,
    errors,
    validServices: isValid ? services : [],
    message: isValid
      ? "Services validation passed"
      : `Services validation failed: ${errors.join(", ")}`,
  };
};

/**
 * Create schema only (without tables)
 * @param {string} organizationName - The organization name
 * @returns {Promise<Object>} Service response
 */
export const createOrganizationSchemaOnly = async (organizationName) => {
  logger.info(
    `schemaService.createOrganizationSchemaOnly - Creating schema for: ${organizationName}`
  );

  try {
    if (!organizationName || typeof organizationName !== "string") {
      throw new Error("Organization name is required and must be a string");
    }

    const schemaName = await createOrganizationSchema(organizationName);

    logger.info(
      `schemaService.createOrganizationSchemaOnly - Successfully created schema: ${schemaName}`
    );

    return createServiceResponse(
      true,
      status.STATUS_CODE_CREATED,
      SCHEMA_MESSAGES.SCHEMA_CREATED_SUCCESSFULLY,
      {
        organizationName,
        schemaName,
      }
    );
  } catch (error) {
    logger.error(
      `schemaService.createOrganizationSchemaOnly - Error: ${error.message}`
    );

    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      SCHEMA_MESSAGES.SCHEMA_CREATION_FAILED,
      null,
      error.message
    );
  }
};

/**
 * Create tables for specific services in existing schema
 * @param {string} schemaName - The schema name
 * @param {Array} services - Array of service names
 * @returns {Promise<Object>} Service response
 */
export const createTablesForServices = async (schemaName, services) => {
  logger.info(
    `schemaService.createTablesForServices - Creating tables for services: ${services.join(
      ", "
    )} in schema: ${schemaName}`
  );

  try {
    if (!schemaName || typeof schemaName !== "string") {
      throw new Error("Schema name is required and must be a string");
    }

    const validation = validateServices(services);
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    const tablesResult = await createOrganizationTablesFromServices(
      schemaName,
      services
    );

    if (tablesResult.success) {
      logger.info(
        `schemaService.createTablesForServices - Successfully created tables in schema: ${schemaName}`
      );

      return createServiceResponse(
        true,
        status.STATUS_CODE_CREATED,
        SCHEMA_MESSAGES.TABLES_CREATED_SUCCESSFULLY,
        {
          schemaName,
          services: tablesResult.data.services,
          summary: tablesResult.data.summary,
          totalTables: tablesResult.data.summary.totalTablesCreated,
        }
      );
    } else {
      const hasPartialSuccess =
        tablesResult.data?.summary?.totalTablesCreated > 0;

      return createServiceResponse(
        hasPartialSuccess,
        hasPartialSuccess
          ? status.STATUS_CODE_PARTIAL_CONTENT
          : status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        hasPartialSuccess
          ? SCHEMA_MESSAGES.PARTIAL_TABLE_CREATION
          : SCHEMA_MESSAGES.TABLE_CREATION_FAILED,
        {
          schemaName,
          services: tablesResult.data?.services || [],
          summary: tablesResult.data?.summary || {},
          error: tablesResult.error,
        }
      );
    }
  } catch (error) {
    logger.error(
      `schemaService.createTablesForServices - Error: ${error.message}`
    );

    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      SCHEMA_MESSAGES.TABLE_CREATION_FAILED,
      null,
      error.message
    );
  }
};

export default {
  createOrganizationSchemaAndTables,
  getAvailableServices,
  validateServices,
  createOrganizationSchemaOnly,
  createTablesForServices,
};
