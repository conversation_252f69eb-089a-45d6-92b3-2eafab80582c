"use client";

import { useRouter } from "next/navigation";
import AddForm from "@/components/common/AddForm";
import { organizationFields } from "@/utils/data/organizations";
import { ORGANIZATION_CONSTANTS } from "@/utils/constants/organization";
import { useDispatch } from "react-redux";
import { createOrganization } from "@/redux/Thunks/organization.js";
import { useToast } from "@/components/ui/toast";
import { extractApiErrorMessage } from "@/utils/errorHandler";
import { useState, useEffect } from "react";
import Loader from "@/components/common/Loader";

export default function AddOrganizationPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const [isAdding, setIsAdding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Page loader overlay - shows when navigating to the page
  useEffect(() => {
    // Simulate initial page load
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500); // 0.5-1 second loading state

    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (values) => {
    setIsAdding(true);
    try {
      const resultAction = await dispatch(createOrganization(values));
      if (createOrganization.fulfilled.match(resultAction)) {
        addToast("Successfully added organization", "success");
        router.push("/masters/org");
      } else {
        // Extract error message from the error response
        const errorMessage = extractApiErrorMessage(resultAction.payload);
        addToast(errorMessage, "error");
      }
    } catch (error) {
      addToast(extractApiErrorMessage(error), "error");
    } finally {
      setIsAdding(false);
    }
  };

  // Page loader overlay - using consistent Loader component
  if (isLoading) {
    return <Loader message="Loading form..." show={true} />;
  }

  return (
    <AddForm
      heading={ORGANIZATION_CONSTANTS.ADD_PAGE.HEADING}
      subTitle={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBTITLE}
      onBack={() => router.push("/masters/org")}
      backLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.BACK_LABEL}
      title={ORGANIZATION_CONSTANTS.ADD_PAGE.TITLE}
      fields={organizationFields}
      onSubmit={handleSubmit}
      submitLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBMIT_LABEL}
      isSubmitting={isAdding}
    />
  );
}
