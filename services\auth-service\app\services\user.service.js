import axios from "axios";
import { createLogger } from "../utils/logger.utils.js";
import {
  LOGGER_MESSAGES,
  LOGGER_NAMES,
} from "../utils/constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.USER_SERVICE);

// User service base URL from config
// Use Docker service name for container-to-container communication
const USER_SERVICE_BASE_URL = process.env.USER_SERVICE_URL;

/**
 * Get user by ID from user service
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User data or null
 */
export const getUserById = async (userId) => {
  try {
    if (!USER_SERVICE_BASE_URL) {
      logger.error("USER_SERVICE_URL environment variable is not set");
      return null;
    }

    const url = `${USER_SERVICE_BASE_URL}/${userId}`;
    const response = await axios.get(url, {
      timeout: 5000,
      validateStatus: (status) => status < 500,
    });

    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    if (response.status === 404 || !response.data?.data) {
      logger.warn(`User not found in user service: ${userId}`);
      return null;
    }

    return null;
  } catch (error) {
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      logger.error("User service is unavailable", {
        error: error.message,
        code: error.code,
        userId,
      });
    } else {
      logger.error(LOGGER_MESSAGES.USER_SERVICE.GET_USER_BY_ID_ERROR, {
        error: error.message,
        userId,
        status: error.response?.status,
      });
    }
    return null;
  }
};

/**
 * Get user by email from user service
 * @param {string} email - User email
 * @returns {Promise<Object>} User data or null
 */
export const getUserByEmail = async (email) => {
  try {
    if (!USER_SERVICE_BASE_URL) {
      logger.error("USER_SERVICE_URL environment variable is not set");
      return null;
    }

    // URL encode the email to handle special characters like @ and .
    const encodedEmail = encodeURIComponent(email);
    const url = `${USER_SERVICE_BASE_URL}/email/${encodedEmail}`;
    
    logger.info(`Fetching user from user service: ${url}`);
    
    const response = await axios.get(url, {
      timeout: 5000, // 5 second timeout
      validateStatus: (status) => status < 500, // Don't throw on 404
    });

    // Check if response has data
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    // If 404 or no data, return null (user not found)
    if (response.status === 404 || !response.data?.data) {
      logger.warn(`User not found in user service: ${email}`);
      return null;
    }

    return null;
  } catch (error) {
    // Handle network errors, timeouts, etc.
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      logger.error("User service is unavailable", {
        error: error.message,
        code: error.code,
        email,
      });
    } else {
      logger.error(LOGGER_MESSAGES.USER_SERVICE.GET_USER_BY_EMAIL_ERROR, {
        error: error.message,
        email,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });
    }
    return null;
  }
};
