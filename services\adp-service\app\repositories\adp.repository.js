import { createLogger } from "../utils/logger.util.js";

const logger = createLogger("ADP_REPOSITORY");

export const adpRepository = {
  /**
   * Create tables in the provided schema dynamically
   * @param {object} client - pg client
   * @param {array} payrollColumnMapping - array of { sqlName, dataType }
   * @param {string} schemaName - target schema name
   */
  createTables: async (client, payrollColumnMapping, schemaName) => {
    if (!schemaName) throw new Error("Schema name is required");

    // Ensure schema exists
    await client.query(`CREATE SCHEMA IF NOT EXISTS ${schemaName};`);

    // Fully qualified table names
    const employeeTable = `${schemaName}.adp_employee`;
    const payrollTable = `${schemaName}.adp_payroll_details`;

    // Create employee table
    await client.query(`
      CREATE TABLE IF NOT EXISTS ${employeeTable} (
        id SERIAL PRIMARY KEY,
        employee_name VARCHAR(255) NOT NULL,
        department VARCHAR(100),
        company_name VARCHAR(255),
        UNIQUE (employee_name, department)
      );
    `);

    // Create payroll_details table
    await client.query(`
      CREATE TABLE IF NOT EXISTS ${payrollTable} (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER REFERENCES ${employeeTable}(id) ON DELETE CASCADE,
        payroll_month VARCHAR(255) NOT NULL,
        ssn VARCHAR(100),
        tin VARCHAR(100),
        pay_frequency VARCHAR(50),
        from_date DATE,
        to_date DATE,
        UNIQUE (employee_id, payroll_month)
      );
    `);

    // Add dynamic columns
    for (const col of payrollColumnMapping) {
      await client.query(`
        ALTER TABLE ${payrollTable}
        ADD COLUMN IF NOT EXISTS "${col.sqlName}" ${col.dataType};
      `);
    }
  },

  /**
   * Insert payroll data into provided schema dynamically
   *
   * @param {object} client - pg client
   * @param {array} records - payroll records
   * @param {array} payrollColumnMapping - dynamic columns
   * @param {string} schemaName - target schema name
   */
  insertData: async (client, records, payrollColumnMapping, schemaName) => {
    if (!schemaName) throw new Error("Schema name is required");

    const employeeTable = `${schemaName}.adp_employee`;
    const payrollTable = `${schemaName}.adp_payroll_details`;

    await client.query("BEGIN");

    try {
      for (const record of records) {
        const employeeResult = await client.query(
          `
          INSERT INTO ${employeeTable} (employee_name, department, company_name)
          VALUES ($1, $2, $3)
          ON CONFLICT (employee_name, department) DO UPDATE
          SET company_name = EXCLUDED.company_name
          RETURNING id;
        `,
          [record.employee_name, record.department, record.company_name]
        );

        const employeeId = employeeResult.rows[0]?.id;
        if (!employeeId) continue;

        // Prepare columns and values
        const fixedColumns = [
          "employee_id",
          "payroll_month",
          "ssn",
          "tin",
          "pay_frequency",
          "from_date",
          "to_date",
        ];
        const dynamicColumns = payrollColumnMapping.map((c) => c.sqlName);
        const allColumns = [...fixedColumns, ...dynamicColumns];

        const allValues = [
          employeeId,
          record.payroll_month,
          record.ssn,
          record.tin,
          record.pay_frequency,
          record.from_date,
          record.to_date,
          ...dynamicColumns.map((c) => record[c]),
        ];

        const placeholders = allValues.map((_, i) => `$${i + 1}`).join(", ");
        const quotedColumns = allColumns.map((c) => `"${c}"`).join(",");

        await client.query(
          `
          INSERT INTO ${payrollTable} (${quotedColumns})
          VALUES (${placeholders})
          ON CONFLICT (employee_id, payroll_month) DO NOTHING;
        `,
          allValues
        );
      }

      await client.query("COMMIT");
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    }
  },

  /**
   * Optional method to manually trigger sync update if needed
   * @param {object} client - pg client
   * @param {string} orgId - Organization ID
   * @param {string} lastSyncedAt - Date in YYYY-MM-DD format (e.g., "2025-08-31")
   */
  updateSyncTime: async (client, orgId, lastSyncedAt) => {
    if (!orgId) {
      logger.error("Organization ID is required for sync time update");
      throw new Error("Organization ID is required");
    }

    if (!lastSyncedAt) {
      logger.error("Last synced date is required for sync time update");
      throw new Error("Last synced date is required");
    }
    const dateObj = new Date(`${lastSyncedAt}T12:00:00.000Z`);
    const formattedDate = dateObj
      .toISOString()
      .replace("T", " ")
      .replace("Z", "+00");

    logger.info("Updating sync time in database", {
      orgId,
      lastSyncedAt,
      formattedDate,
    });
    try {
      await client.query(
        `
        UPDATE "Authentication"."app_organization"
        SET adp_last_synced_at = $2::timestamptz
        WHERE id = $1;
      `,
        [orgId, formattedDate]
      );
      logger.info("Successfully updated sync time in database", {
        orgId,
        formattedDate,
      });
    } catch (error) {
      logger.error("Failed to update sync time in database", {
        orgId,
        lastSyncedAt,
        formattedDate,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  },
};
