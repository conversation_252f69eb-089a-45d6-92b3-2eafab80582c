import { bulkCreate, bulkCreateInSchema } from "../utils/database.util.js";
import {
  AccountsReceivable,
  TreatmentPlanAnalysis,
  NoShowAppointments,
  AvgDailyProduction,
  NewPatients,
  TotalProductionPerDay,
  TotalProductionByDentist,
  TotalProductionByHygienist,
  HygieneReappointment,
  DirectRestorations,
} from "../models/index.model.js";

/**
 * KPI Repository
 * @module kpiRepository
 * @description Repository for KPIs
 */
export const kpiRepository = {
  /**
   * Create a single account receivable record
   * @param {Object} data - Account receivable data
   * @returns {Promise<Object>} Created record
   */
  createAccountReceivable: async (data) => {
    return await bulkCreate(AccountsReceivable, data);
  },

  /**
   * Create treatment analysis records
   * @param {Object} data - Treatment analysis data
   * @returns {Promise<Object>} Created record
   */
  createTreatmentAnalysis: async (data) => {
    return await bulkCreate(TreatmentPlanAnalysis, data);
  },

  /**
   * Create treatment analysis records
   * @param {Object} data - Treatment analysis data
   * @returns {Promise<Object>} Created record
   */
  directRestorations: async (data) => {
    return await bulkCreate(DirectRestorations, data);
  },

  /**
   * Create avg daily production records
   * @param {Object} data - Avg daily production data
   * @returns {Promise<Object>} Created record
   */
  createAvgDailyProduction: async (data) => {
    return await bulkCreate(AvgDailyProduction, data);
  },

  /**
   * Create new patients records
   * @param {Object} data - New patients data
   * @returns {Promise<Object>} Created record
   */
  createNewPatients: async (data) => {
    return await bulkCreate(NewPatients, data);
  },

  /**
   * Create no show appointments records
   * @param {Object} data - No show appointments data
   * @returns {Promise<Object>} Created record
   */
  createNoShowAppointments: async (data) => {
    return await bulkCreate(NoShowAppointments, data);
  },

  /**
   * Create total production per day records
   * @param {Object} data - Total production per day data
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionPerDay: async (data) => {
    return await bulkCreate(TotalProductionPerDay, data);
  },

  /**
   * Create total production by dentist records
   * @param {Object} data - Total production by dentist data
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionByDentist: async (data) => {
    return await bulkCreate(TotalProductionByDentist, data);
  },

  /**
   * Create total production by hygienist records
   * @param {Object} data - Total production by hygienist data
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionByHygienist: async (data) => {
    return await bulkCreate(TotalProductionByHygienist, data);
  },

  /**
   * Create hygiene reappointment records
   * @param {Object} data - Hygiene reappointment data
   * @returns {Promise<Object>} Created record
   */
  createHygieneReappointment: async (data) => {
    return await bulkCreate(HygieneReappointment, data);
  },

  // Schema-aware repository functions
  /**
   * Create account receivable records in organization schema
   * @param {Object} data - Account receivable data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createAccountReceivableInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(AccountsReceivable, data, organizationName);
  },

  /**
   * Create treatment analysis records in organization schema
   * @param {Object} data - Treatment analysis data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createTreatmentAnalysisInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(
      TreatmentPlanAnalysis,
      data,
      organizationName
    );
  },

  /**
   * Create direct restorations records in organization schema
   * @param {Object} data - Direct restorations data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  directRestorationsInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(DirectRestorations, data, organizationName);
  },

  /**
   * Create avg daily production records in organization schema
   * @param {Object} data - Avg daily production data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createAvgDailyProductionInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(AvgDailyProduction, data, organizationName);
  },

  /**
   * Create new patients records in organization schema
   * @param {Object} data - New patients data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createNewPatientsInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(NewPatients, data, organizationName);
  },

  /**
   * Create no show appointments records in organization schema
   * @param {Object} data - No show appointments data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createNoShowAppointmentsInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(NoShowAppointments, data, organizationName);
  },

  /**
   * Create total production per day records in organization schema
   * @param {Object} data - Total production per day data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionPerDayInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(
      TotalProductionPerDay,
      data,
      organizationName
    );
  },

  /**
   * Create total production by dentist records in organization schema
   * @param {Object} data - Total production by dentist data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionByDentistInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(
      TotalProductionByDentist,
      data,
      organizationName
    );
  },

  /**
   * Create total production by hygienist records in organization schema
   * @param {Object} data - Total production by hygienist data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createTotalProductionByHygienistInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(
      TotalProductionByHygienist,
      data,
      organizationName
    );
  },

  /**
   * Create hygiene reappointment records in organization schema
   * @param {Object} data - Hygiene reappointment data
   * @param {string} organizationName - Organization name for schema
   * @returns {Promise<Object>} Created record
   */
  createHygieneReappointmentInSchema: async (data, organizationName) => {
    return await bulkCreateInSchema(
      HygieneReappointment,
      data,
      organizationName
    );
  },
};
