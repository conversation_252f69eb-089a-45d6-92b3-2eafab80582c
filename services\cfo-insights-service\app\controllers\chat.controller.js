// app/controllers/chat.controller.js
import { startChat, sendMessage, endChat } from "../services/chat.service.js";
import { CHAT_ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { CHAT_DEFAULTS } from "../utils/constants/server.constants.js";
import { normalizeSessionId } from "../utils/helpers.js";

// Utilities
function missingParams(res, message) {
  return res.status(400).json({ error: message });
}

function mapServiceError(err, res, fallback = "Server error") {
  if (err && typeof err.message === "string" && /invalid sessionid/i.test(err.message)) {
    return res.status(404).json({ error: CHAT_ERROR_MESSAGES.SESSION_NOT_FOUND_OR_EXPIRED });
  }
  return res.status(500).json({ error: err.message || fallback });
}

/**
 * POST /api/chat/start
 * Starts a chat session for a selected document.
 */
export const handleStartChat = async (req, res) => {
  try {
    const { filename } = req.body;
    if (!filename) return missingParams(res, CHAT_ERROR_MESSAGES.FILENAME_REQUIRED);

    const session = await startChat(filename);
    res.json(session);
  } catch (err) {
    console.error("handleStartChat error:", err);
    res.status(500).json({ error: err.message || CHAT_ERROR_MESSAGES.FAILED_START_CHAT });
  }
};

/**
 * POST /api/chat/message
 * Sends user's message in chat mode (summary off by default). Organization is optional.
 * @param {string} sessionId - Chat session ID
 * @param {string} message - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summary] - If true, use summary prompt; if false, use conversational chat prompt
 */
export const handleChatMessage = async (req, res) => {
  try {
    const { sessionId, message, organization, summary } = req.body;

    if (!sessionId || !message) return missingParams(res, CHAT_ERROR_MESSAGES.SESSION_AND_MESSAGE_REQUIRED);

    // summary flag is optional for /message; defaults to false (chat mode)
    const isSummaryMode = summary === true || summary === "true" || summary === 1 || summary === "1" ? true : false;

    // Normalize sessionId (trim whitespace)
    const normalizedSessionId = normalizeSessionId(sessionId);

    // organization and summary flag passed from frontend
    const result = await sendMessage(normalizedSessionId, message, organization, isSummaryMode);
    res.json({
      plainAnswer: result.plainAnswer,
      filename: result.filename,
    });
  } catch (err) {
    console.error("handleChatMessage error:", err);
    return mapServiceError(err, res, CHAT_ERROR_MESSAGES.FAILED_SEND_MESSAGE);
  }
};

/**
 * POST /api/chat/summary
 * Sends user's message using Summary prompt (always summary mode). Organization is optional.
 * Body: { sessionId, message, organization? }
 */
export const handleChatSummary = async (req, res) => {
  try {
    const { sessionId, message, organization } = req.body;

    if (!sessionId) return missingParams(res, CHAT_ERROR_MESSAGES.SESSION_ID_REQUIRED);

    const normalizedSessionId = normalizeSessionId(sessionId);
    const promptMessage = (message && String(message).trim().length > 0)
      ? String(message).trim()
      : CHAT_DEFAULTS.DASHBOARD_SUMMARY_MESSAGE;
    const result = await sendMessage(normalizedSessionId, promptMessage, organization, true);
    res.json({
      plainAnswer: result.plainAnswer,
      filename: result.filename,
    });
  } catch (err) {
    console.error("handleChatSummary error:", err);
    return mapServiceError(err, res, CHAT_ERROR_MESSAGES.FAILED_SEND_SUMMARY);
  }
};

/**
 * POST /api/chat/end
 * Explicitly expire a chat session
 */
export const handleEndChat = async (req, res) => {
  try {
    const { sessionId } = req.body;
    const normalizedSessionId = normalizeSessionId(sessionId);
    if (!normalizedSessionId) return missingParams(res, CHAT_ERROR_MESSAGES.SESSION_ID_REQUIRED);

    const result = await endChat(normalizedSessionId);
    return res.status(200).json(result);
  } catch (err) {
    console.error("handleEndChat error:", err);
    return mapServiceError(err, res, CHAT_ERROR_MESSAGES.FAILED_END_SESSION);
  }
};
