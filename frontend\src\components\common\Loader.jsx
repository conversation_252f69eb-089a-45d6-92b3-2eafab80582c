"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Optimized Loader Component
 * - Centered vertically and horizontally
 * - Smooth fade-in/out animation
 * - Purple/blue spinner matching dashboard theme
 * - Semi-transparent background overlay
 */
const Loader = ({ message = "Loading...", show = true }) => {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="fixed inset-0 flex items-center justify-center bg-white/60 backdrop-blur-sm z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex flex-col items-center justify-center gap-4"
          >
            {/* Animated Spinner */}
            <div className="relative w-16 h-16">
              {/* Outer ring - Purple */}
              <motion.div
                className="absolute inset-0 border-4 border-transparent border-t-purple-500 border-r-purple-400 rounded-full"
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
              
              {/* Middle ring - Indigo */}
              <motion.div
                className="absolute inset-2 border-4 border-transparent border-t-indigo-500 border-r-indigo-400 rounded-full"
                animate={{ rotate: -360 }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
              
              {/* Inner ring - Blue */}
              <motion.div
                className="absolute inset-4 border-4 border-transparent border-t-blue-500 border-r-blue-400 rounded-full"
                animate={{ rotate: 360 }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
              
              {/* Center pulsing dot */}
              <motion.div
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [1, 0.5, 1]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </div>

            {/* Loading Message */}
            {message && (
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-gray-700 font-medium text-base bg-white/80 px-4 py-2 rounded-full shadow-sm"
              >
                {message}
              </motion.p>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Loader;

