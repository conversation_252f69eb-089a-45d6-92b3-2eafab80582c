import { AppOrganization } from "../models/index.js";
import { create, update, findAll } from "../utils/database.utils.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.AUTH_REPOSITORY);

export const organizationRepository = {
  /**
   * Find organization by ID
   * @param {string} organizationId - Organization ID (UUID)
   * @param {Array} include - Associations to include
   * @returns {Promise<Object|null>} Organization object or null
   */
  async findOrganizationById(organizationId, include = []) {
    logger.info("Finding organization by ID", { organizationId });

    return await AppOrganization.findOne({
      where: {
        id: organizationId,
        is_active: true,
        is_deleted: false,
      },
      include,
    });
  },

  /**
   * Find organization by email
   * @param {string} email - Organization email
   * @param {Array} include - Associations to include
   * @returns {Promise<Object|null>} Organization object or null
   */
  async findOrganizationByEmail(email, include = []) {
    logger.info("Finding organization by email", { email });

    return await AppOrganization.findOne({
      where: {
        email,
        is_active: true,
        is_deleted: false,
      },
      include,
    });
  },

  /**
   * Find organization by office_id
   * @param {string} officeId - Organization office_id
   * @param {Array} include - Associations to include
   * @returns {Promise<Object|null>} Organization object or null
   */
  async findOrganizationByOfficeId(officeId, include = []) {
    logger.info("Finding organization by office_id", { officeId });

    return await AppOrganization.findOne({
      where: {
        office_id: officeId,
        is_active: true,
        is_deleted: false,
      },
      include,
    });
  },

  /**
   * Find organization by realm_id
   * @param {string} realmId - Organization realm_id
   * @param {Array} include - Associations to include
   * @returns {Promise<Object|null>} Organization object or null
   */
  async findOrganizationByRealmId(realmId, include = []) {
    logger.info("Finding organization by realm_id", { realmId });

    return await AppOrganization.findOne({
      where: {
        realm_id: realmId,
        is_active: true,
        is_deleted: false,
      },
      include,
    });
  },

  /**
   * Find organization with flexible filtering
   * @param {Object} filter - Filter options
   * @param {string} filter.id - Organization ID
   * @param {string} filter.email - Organization email
   * @param {string} filter.name - Organization name
   * @param {boolean} filter.is_active - Organization active status
   * @param {Array} filter.include - Associations to include
   * @param {Array} filter.attributes - Attributes to select
   * @returns {Promise<Object|null>} Organization object or null
   */
  async findOrganization(filter = {}) {
    const {
      id,
      email,
      name,
      is_active,
      include = [],
      attributes,
      ...otherFilters
    } = filter;

    // Build where conditions
    const where = {};

    if (id) where.id = id;
    if (email) where.email = email;
    if (name) where.name = name;
    if (is_active !== undefined) where.is_active = is_active;

    // Always filter out deleted organizations
    where.is_deleted = false;

    // Add any other filters
    Object.assign(where, otherFilters);

    return await AppOrganization.findOne({ where, include, attributes });
  },

  /**
   * Create a new organization
   * @param {Object} organizationData - Organization data to create
   * @returns {Promise<Object>} Created organization
   */
  async createOrganization(organizationData) {
    logger.info("Creating new organization", { name: organizationData.name });

    return await create(AppOrganization, organizationData);
  },

  /**
   * Update organization data
   * @param {string} organizationId - Organization ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated organization or null
   */
  async updateOrganization(organizationId, updateData) {
    logger.info("Updating organization", { organizationId });

    return await update(AppOrganization, organizationId, updateData);
  },

  /**
   * Get all organizations with optional filtering
   * @param {Object} filter - Filter options
   * @param {boolean} filter.is_active - Active organizations only
   * @param {Array} filter.include - Associations to include
   * @param {Array} filter.attributes - Attributes to select
   * @param {Object} filter.pagination - Pagination options
   * @param {number} filter.pagination.limit - Number of records to return
   * @param {number} filter.pagination.offset - Number of records to skip
   * @param {number} filter.limit - Direct limit parameter
   * @param {number} filter.offset - Direct offset parameter
   * @returns {Promise<Array>} Array of organizations
   */
  async findAllOrganizations(filter = {}) {
    const {
      is_active,
      include = [],
      attributes,
      pagination,
      limit,
      offset,
      ...otherFilters
    } = filter;

    // Build where conditions
    const where = {};

    if (is_active !== undefined) where.is_active = is_active;

    // Always filter out deleted organizations
    where.is_deleted = false;

    // Add any other filters, but filter out empty objects and invalid values
    for (const [key, value] of Object.entries(otherFilters)) {
      // Only add non-empty objects and valid values
      if (
        value !== null &&
        value !== undefined &&
        !(typeof value === "object" && Object.keys(value).length === 0)
      ) {
        where[key] = value;
      }
    }

    const order = [["created_at", "DESC"]];

    const options = { where, include, attributes, order };

    // Add pagination if provided (check both pagination object and direct limit/offset)
    if (pagination) {
      options.limit = pagination.limit;
      options.offset = pagination.offset;
    } else if (limit !== undefined || offset !== undefined) {
      options.limit = limit;
      options.offset = offset;
    }

    return await findAll(AppOrganization, options);
  },

  /**
   * Check if organization exists by ID
   * @param {string} organizationId - Organization ID
   * @returns {Promise<boolean>} True if organization exists, false otherwise
   */
  async organizationExists(organizationId) {
    const organization = await AppOrganization.findOne({
      where: {
        id: organizationId,
        is_active: true,
        is_deleted: false,
      },
      attributes: ["id"],
    });
    return !!organization;
  },

  /**
   * Get organization count for analytics/monitoring
   * @param {Object} filter - Filter options
   * @param {boolean} filter.is_active - Active organizations only
   * @returns {Promise<number>} Organization count
   */
  async getOrganizationCount(filter = {}) {
    const where = {};

    if (filter.is_active !== undefined) where.is_active = filter.is_active;

    // Always filter out deleted organizations
    where.is_deleted = false;

    const organizations = await findAll(AppOrganization, {
      where,
      attributes: ["id"],
    });

    return organizations.length;
  },

  /**
   * Soft delete organization (deactivate and mark as deleted)
   * @param {string} organizationId - Organization ID
   * @returns {Promise<boolean>} Success status
   */
  async deactivateOrganization(organizationId) {
    logger.info("Deactivating organization", { organizationId });

    return await update(AppOrganization, organizationId, {
      is_active: false,
      is_deleted: true,
    });
  },

  /**
   * Update sync field for organization
   * @param {string} organizationId - Organization ID
   * @param {string} syncType - Type of sync (qb, sikka, adp)
   * @returns {Promise<Object|null>} Updated organization or null
   */
  async updateSyncField(organizationId, syncType, lastSyncedAt) {
    logger.info("Updating sync field", { organizationId, syncType });

    // Map sync types to their corresponding field names
    const syncFieldMap = {
      qb: "qb_last_synced_at",
      sikka: "sikka_last_synced_at",
      adp: "adp_last_synced_at",
    };

    const fieldName = syncFieldMap[syncType];
    if (!fieldName) {
      throw new Error(
        `Invalid sync type: ${syncType}. Must be one of: qb, sikka, adp`
      );
    }
    const date = new Date(`${lastSyncedAt}T15:16:00.276+05:30`);
    const formattedDate = date.toISOString().replace("T", " ").replace("Z", "+05:30");


    const updateData = {
      [fieldName]: formattedDate,
      updated_at: new Date(),
    };

    return await update(AppOrganization, organizationId, updateData);
  },

  /**
   * Update realm_id for organization
   * @param {string} organizationId - Organization ID
   * @param {string} realmId - New realm_id value
   * @returns {Promise<Object|null>} Updated organization or null
   */
  async updateRealmId(organizationId, realmId) {
    logger.info("Updating realm_id", { organizationId, realmId });

    const updateData = {
      realm_id: realmId,
      updated_at: new Date(),
    };

    return await update(AppOrganization, organizationId, updateData);
  },

  /**
   * Update QB connection status for organization
   * @param {string} organizationId - Organization ID
   * @param {boolean} isConnected - QB connection status
   * @returns {Promise<Object|null>} Updated organization or null
   */
  async updateQbConnection(organizationId, isConnected) {
    logger.info("Updating QB connection status", {
      organizationId,
      isConnected,
    });

    const updateData = {
      is_qb_connected: isConnected,
      updated_at: new Date(),
    };

    return await update(AppOrganization, organizationId, updateData);
  },
};

export default organizationRepository;
