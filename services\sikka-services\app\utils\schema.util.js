import { createLogger } from "./logger.util.js";
import { LOGGER_NAMES } from "./constants.util.js";
import sequelize from "../../config/postgres.js";

const logger = createLogger(LOGGER_NAMES.SCHEMA_UTILS || "SCHEMA_UTILS");

/**
 * Convert organization name to snake_case for schema naming
 * @param {string} organizationName - The organization name
 * @returns {string} Snake case schema name
 */
export const convertToSnakeCase = (organizationName) => {
  if (!organizationName || typeof organizationName !== "string") {
    throw new Error("Organization name must be a non-empty string");
  }

  return (
    organizationName
      .toLowerCase()
      .trim()
      // Replace spaces and special characters with underscores
      .replace(/[^a-z0-9]/g, "_")
      // Remove multiple consecutive underscores
      .replace(/_+/g, "_")
      // Remove leading and trailing underscores
      .replace(/^_+|_+$/g, "")
      // Ensure it starts with a letter (PostgreSQL requirement)
      .replace(/^[0-9]/, "org_$&")
      // Limit length to 63 characters (PostgreSQL limit)
      .substring(0, 63)
  );
};

/**
 * Validate schema name according to PostgreSQL rules
 * @param {string} schemaName - The schema name to validate
 * @returns {boolean} True if valid, throws error if invalid
 */
export const validateSchemaName = (schemaName) => {
  if (!schemaName || typeof schemaName !== "string") {
    throw new Error("Schema name must be a non-empty string");
  }

  // PostgreSQL identifier rules
  const validPattern = /^[a-z][a-z0-9_]*$/;

  if (!validPattern.test(schemaName)) {
    throw new Error(
      "Schema name must start with a letter and contain only lowercase letters, numbers, and underscores"
    );
  }

  if (schemaName.length > 63) {
    throw new Error("Schema name must be 63 characters or less");
  }

  // Check for reserved words
  const reservedWords = [
    "public",
    "information_schema",
    "pg_catalog",
    "pg_toast",
    "pg_temp",
    "pg_toast_temp",
    "postgres",
    "template0",
    "template1",
  ];

  if (reservedWords.includes(schemaName.toLowerCase())) {
    throw new Error(
      `Schema name '${schemaName}' is reserved and cannot be used`
    );
  }

  return true;
};

/**
 * Create a new PostgreSQL schema
 * @param {string} schemaName - The name of the schema to create
 * @returns {Promise<boolean>} True if created successfully
 */
export const createSchema = async (schemaName) => {
  logger.info(`schemaUtils.createSchema - Creating schema: ${schemaName}`);

  try {
    // Validate schema name
    validateSchemaName(schemaName);

    // Check if schema already exists
    const schemaExists = await checkSchemaExists(schemaName);
    if (schemaExists) {
      logger.warn(
        `schemaUtils.createSchema - Schema already exists: ${schemaName}`
      );
      return true;
    }

    // Create the schema
    const query = `CREATE SCHEMA IF NOT EXISTS "${schemaName}"`;
    await sequelize.query(query);

    logger.info(
      `schemaUtils.createSchema - Schema created successfully: ${schemaName}`
    );
    return true;
  } catch (error) {
    logger.error(
      `schemaUtils.createSchema - Error creating schema ${schemaName}: ${error.message}`
    );
    throw error;
  }
};

/**
 * Check if a schema exists
 * @param {string} schemaName - The name of the schema to check
 * @returns {Promise<boolean>} True if schema exists
 */
export const checkSchemaExists = async (schemaName) => {
  logger.debug(
    `schemaUtils.checkSchemaExists - Checking if schema exists: ${schemaName}`
  );

  try {
    const query = `
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name = :schemaName
    `;

    const results = await sequelize.query(query, {
      replacements: { schemaName },
      type: sequelize.QueryTypes.SELECT,
    });

    const exists = Array.isArray(results) && results.length > 0;
    logger.debug(
      `schemaUtils.checkSchemaExists - Schema ${schemaName} exists: ${exists}`
    );

    return exists;
  } catch (error) {
    logger.error(
      `schemaUtils.checkSchemaExists - Error checking schema ${schemaName}: ${error.message}`
    );
    throw error;
  }
};

/**
 * Get organization schema name from organization name
 * @param {string} organizationName - The organization name
 * @returns {string} The schema name for the organization
 */
export const getOrganizationSchemaName = (organizationName) => {
  logger.debug(
    `schemaUtils.getOrganizationSchemaName - Converting organization name: ${organizationName}`
  );

  try {
    const schemaName = convertToSnakeCase(organizationName);
    validateSchemaName(schemaName);

    logger.debug(
      `schemaUtils.getOrganizationSchemaName - Schema name: ${schemaName}`
    );
    return schemaName;
  } catch (error) {
    logger.error(
      `schemaUtils.getOrganizationSchemaName - Error converting organization name: ${error.message}`
    );
    throw error;
  }
};

/**
 * Create organization schema with validation
 * @param {string} organizationName - The organization name
 * @returns {Promise<string>} The created schema name
 */
export const createOrganizationSchema = async (organizationName) => {
  logger.info(
    `schemaUtils.createOrganizationSchema - Creating schema for organization: ${organizationName}`
  );

  try {
    const schemaName = getOrganizationSchemaName(organizationName);
    await createSchema(schemaName);

    logger.info(
      `schemaUtils.createOrganizationSchema - Organization schema created: ${schemaName}`
    );
    return schemaName;
  } catch (error) {
    logger.error(
      `schemaUtils.createOrganizationSchema - Error creating organization schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Create table in specific schema if it doesn't exist
 * @param {string} schemaName - The schema name
 * @param {string} tableName - The table name
 * @param {Object} model - Sequelize model
 * @returns {Promise<boolean>} True if table created or already exists
 */
export const createTableInSchema = async (schemaName, tableName, model) => {
  logger.info(`schemaUtils.createTableInSchema - Creating table ${tableName} in schema: ${schemaName}`);

  try {
    // Check if table exists in schema
    const tableExists = await checkTableExistsInSchema(schemaName, tableName);
    if (tableExists) {
      logger.debug(`schemaUtils.createTableInSchema - Table ${tableName} already exists in schema: ${schemaName}`);
      return true;
    }

    // Set search path to the schema
    await sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Create the table using Sequelize model sync
    await model.sync({ force: false });

    // Reset search path
    await sequelize.query('SET search_path TO public');

    logger.info(`schemaUtils.createTableInSchema - Table ${tableName} created successfully in schema: ${schemaName}`);
    return true;
  } catch (error) {
    // Reset search path on error
    try {
      await sequelize.query('SET search_path TO public');
    } catch (resetError) {
      logger.error(`schemaUtils.createTableInSchema - Error resetting search path: ${resetError.message}`);
    }

    logger.error(`schemaUtils.createTableInSchema - Error creating table ${tableName} in schema ${schemaName}: ${error.message}`);
    throw error;
  }
};

/**
 * Check if table exists in specific schema
 * @param {string} schemaName - The schema name
 * @param {string} tableName - The table name
 * @returns {Promise<boolean>} True if table exists
 */
export const checkTableExistsInSchema = async (schemaName, tableName) => {
  logger.debug(`schemaUtils.checkTableExistsInSchema - Checking table ${tableName} in schema: ${schemaName}`);

  try {
    const query = `
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = :schemaName AND table_name = :tableName
    `;

    const results = await sequelize.query(query, {
      replacements: { schemaName, tableName },
      type: sequelize.QueryTypes.SELECT,
    });

    const exists = Array.isArray(results) && results.length > 0;
    logger.debug(`schemaUtils.checkTableExistsInSchema - Table ${tableName} exists in schema ${schemaName}: ${exists}`);

    return exists;
  } catch (error) {
    logger.error(`schemaUtils.checkTableExistsInSchema - Error checking table ${tableName} in schema ${schemaName}: ${error.message}`);
    throw error;
  }
};

/**
 * Execute query in organization schema with automatic search path management
 * @param {string} organizationName - The organization name
 * @param {string} query - The SQL query to execute
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Query results
 */
export const executeQueryInOrganizationSchema = async (organizationName, query, options = {}) => {
  logger.debug(`schemaUtils.executeQueryInOrganizationSchema - Executing query in org: ${organizationName}`);

  try {
    const schemaName = getOrganizationSchemaName(organizationName);

    // Set the search path to include the organization schema
    await sequelize.query(`SET search_path TO "${schemaName}", public`);

    // Execute the query
    const results = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      ...options
    });

    // Reset search path
    await sequelize.query('SET search_path TO public');

    logger.debug(`schemaUtils.executeQueryInOrganizationSchema - Query executed successfully`);
    return results;

  } catch (error) {
    // Reset search path on error
    try {
      await sequelize.query('SET search_path TO public');
    } catch (resetError) {
      logger.error(`schemaUtils.executeQueryInOrganizationSchema - Error resetting search path: ${resetError.message}`);
    }

    logger.error(`schemaUtils.executeQueryInOrganizationSchema - Error: ${error.message}`);
    throw error;
  }
};

export default {
  convertToSnakeCase,
  validateSchemaName,
  createSchema,
  checkSchemaExists,
  getOrganizationSchemaName,
  createOrganizationSchema,
  createTableInSchema,
  checkTableExistsInSchema,
  executeQueryInOrganizationSchema,
};
