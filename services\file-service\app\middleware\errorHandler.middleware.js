// app/middleware/errorHandler.middleware.js
import logger from "../../config/logger.config.js";

/**
 * Global error handler middleware
 */
export const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error("Error occurred:", err);

  // Handle specific error types
  if (err.name === "MulterError") {
    return res.status(400).json({
      success: false,
      error: "File upload error",
      message: err.message,
    });
  }

  // Default error response
  const statusCode = err.statusCode || 500;
  const message = err.message || "Internal server error";

  res.status(statusCode).json({
    success: false,
    error: message,
    message: "An error occurred processing your request",
  });
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req, res) => {
  res.status(404).json({
    success: false,
    error: "Not Found",
    message: `Route ${req.originalUrl} not found`,
  });
};

