// Table builder utility for creating tables from API response models
import { DataTypes } from "sequelize";
import sequelize from "../../config/postgres.config.js";
import { createLogger } from "./logger.utils.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.TABLE_BUILDER || "TableBuilder");

/**
 * Map API data types to Sequelize DataTypes
 */
const DATA_TYPE_MAPPING = {
  // String types
  STRING: DataTypes.STRING,
  TEXT: DataTypes.TEXT,
  CHAR: DataTypes.CHAR,

  // Number types
  INTEGER: DataTypes.INTEGER,
  BIGINT: DataTypes.BIGINT,
  FLOAT: DataTypes.FLOAT,
  DOUBLE: DataTypes.DOUBLE,
  DECIMAL: DataTypes.DECIMAL,
  REAL: DataTypes.REAL,

  // Boolean type
  BOOLEAN: DataTypes.BOOLEAN,

  // Date types
  DATE: DataTypes.DATE,
  DATEONLY: DataTypes.DATEONLY,
  TIME: DataTypes.TIME,

  // JSON types
  JSON: DataTypes.JSON,
  JSONB: DataTypes.JSONB,

  // Binary types
  BLOB: DataTypes.BLOB,

  // UUID type
  UUID: DataTypes.UUID,

  // Enum type (will be handled specially)
  ENUM: DataTypes.ENUM,

  // Array types
  ARRAY: DataTypes.ARRAY,

  // Geometry types (PostGIS)
  GEOMETRY: DataTypes.GEOMETRY,
  GEOGRAPHY: DataTypes.GEOGRAPHY,
};

/**
 * Table Builder class for creating tables from API model definitions
 */
export class TableBuilder {
  constructor(schemaName) {
    this.schemaName = schemaName;
    this.createdTables = [];
    this.failedTables = [];
  }

  /**
   * Create tables from models information
   * @param {Object} modelsInfo - Models information from API response
   * @returns {Promise<Object>} Creation result
   */
  async createTablesFromModels(modelsInfo) {
    logger.info(
      `TableBuilder.createTablesFromModels - Creating ${modelsInfo.totalModels} tables in schema: ${this.schemaName}`
    );

    const results = {
      totalModels: modelsInfo.totalModels,
      createdTables: [],
      failedTables: [],
      skippedTables: [],
      errors: [],
    };

    try {
      // Process each model
      for (const [modelName, modelDefinition] of Object.entries(
        modelsInfo.models
      )) {
        try {
          const tableResult = await this.createTableFromModel(
            modelName,
            modelDefinition
          );

          if (tableResult.success) {
            if (tableResult.created) {
              results.createdTables.push(tableResult);
            } else {
              results.skippedTables.push(tableResult);
            }
          } else {
            results.failedTables.push(tableResult);
            results.errors.push(tableResult.error);
          }
        } catch (error) {
          logger.error(
            `TableBuilder.createTablesFromModels - Error processing model ${modelName}: ${error.message}`
          );
          results.failedTables.push({
            modelName,
            tableName: modelDefinition.tableName || modelName,
            success: false,
            error: error.message,
          });
          results.errors.push(error.message);
        }
      }

      const successCount =
        results.createdTables.length + results.skippedTables.length;
      logger.info(
        `TableBuilder.createTablesFromModels - Completed: ${successCount}/${modelsInfo.totalModels} tables processed successfully`
      );

      return {
        success: results.failedTables.length === 0,
        ...results,
      };
    } catch (error) {
      logger.error(
        `TableBuilder.createTablesFromModels - Fatal error: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Create a single table from model definition
   * @param {string} modelName - Name of the model
   * @param {Object} modelDefinition - Model definition from API
   * @returns {Promise<Object>} Creation result
   */
  async createTableFromModel(modelName, modelDefinition) {
    const tableName = modelDefinition.tableName || modelName;
    const fullTableName = `${this.schemaName}.${tableName}`;

    logger.debug(
      `TableBuilder.createTableFromModel - Processing model: ${modelName} -> table: ${fullTableName}`
    );

    try {
      // Check if table already exists
      const tableExists = await this.checkTableExists(tableName);
      if (tableExists) {
        logger.info(
          `TableBuilder.createTableFromModel - Table already exists: ${fullTableName}`
        );
        return {
          modelName,
          tableName,
          fullTableName,
          success: true,
          created: false,
          message: "Table already exists",
        };
      }

      // Build table definition
      const tableDefinition = this.buildTableDefinition(modelDefinition);

      // Create the table
      await this.executeCreateTable(
        tableName,
        tableDefinition,
        modelDefinition
      );

      // Create indexes if specified
      if (modelDefinition.indexes && Array.isArray(modelDefinition.indexes)) {
        await this.createIndexes(tableName, modelDefinition.indexes);
      }

      logger.info(
        `TableBuilder.createTableFromModel - Successfully created table: ${fullTableName}`
      );

      return {
        modelName,
        tableName,
        fullTableName,
        success: true,
        created: true,
        columns: Object.keys(modelDefinition.columns || {}),
        indexes: modelDefinition.indexes?.length || 0,
        message: "Table created successfully",
      };
    } catch (error) {
      logger.error(
        `TableBuilder.createTableFromModel - Error creating table ${fullTableName}: ${error.message}`
      );

      return {
        modelName,
        tableName,
        fullTableName,
        success: false,
        created: false,
        error: error.message,
      };
    }
  }

  /**
   * Build Sequelize table definition from model definition
   * @param {Object} modelDefinition - Model definition from API
   * @returns {Object} Sequelize table definition
   */
  buildTableDefinition(modelDefinition) {
    const columns = {};
    const options = {
      schema: this.schemaName,
      timestamps: modelDefinition.timestamps || false,
      freezeTableName: true,
    };

    // Process columns
    if (modelDefinition.columns) {
      for (const [columnName, columnDef] of Object.entries(
        modelDefinition.columns
      )) {
        columns[columnName] = this.buildColumnDefinition(columnDef);
      }
    }

    // Add default timestamps if enabled
    if (modelDefinition.timestamps) {
      if (!columns.createdAt) {
        columns.createdAt = {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        };
      }
      if (!columns.updatedAt) {
        columns.updatedAt = {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        };
      }
    }

    return { columns, options };
  }

  /**
   * Build Sequelize column definition from API column definition
   * @param {Object} columnDef - Column definition from API
   * @returns {Object} Sequelize column definition
   */
  buildColumnDefinition(columnDef) {
    const sequelizeColumn = {};

    // Map data type
    const dataType = this.mapDataType(columnDef.type, columnDef);
    if (dataType) {
      sequelizeColumn.type = dataType;
    }

    // Debug logging for problematic columns
    if (columnDef.defaultValue && typeof columnDef.defaultValue === "object") {
      logger.debug(
        `TableBuilder.buildColumnDefinition - Found object defaultValue: ${JSON.stringify(
          columnDef.defaultValue
        )} for type: ${columnDef.type}`
      );
    }

    // Set allowNull
    if (typeof columnDef.allowNull === "boolean") {
      // Primary keys should typically not allow null, even if API says they can
      if (columnDef.primaryKey === true) {
        sequelizeColumn.allowNull = false;
      } else {
        sequelizeColumn.allowNull = columnDef.allowNull;
      }
    }

    // Set default value
    if (
      columnDef.defaultValue !== undefined &&
      columnDef.defaultValue !== null
    ) {
      // Handle special UUID default value
      if (
        columnDef.defaultValue === "uuid_generate_v4()" &&
        dataType === DataTypes.UUID
      ) {
        sequelizeColumn.defaultValue = DataTypes.UUIDV4;
      } else if (
        // Handle empty object default value (common in API responses)
        typeof columnDef.defaultValue === "object" &&
        Object.keys(columnDef.defaultValue).length === 0
      ) {
        // For UUID primary keys with empty object default, use UUIDV4
        if (dataType === DataTypes.UUID && columnDef.primaryKey === true) {
          sequelizeColumn.defaultValue = DataTypes.UUIDV4;
        }
        // For other cases with empty object, don't set defaultValue
      } else if (
        // Handle string default values that might be functions
        typeof columnDef.defaultValue === "string" &&
        columnDef.defaultValue.includes("()")
      ) {
        // Handle various UUID generation functions
        if (
          columnDef.defaultValue.toLowerCase().includes("uuid") &&
          dataType === DataTypes.UUID
        ) {
          sequelizeColumn.defaultValue = DataTypes.UUIDV4;
        } else if (
          columnDef.defaultValue.toLowerCase().includes("now") &&
          (dataType === DataTypes.DATE || dataType === DataTypes.TIME)
        ) {
          sequelizeColumn.defaultValue = DataTypes.NOW;
        }
        // For other function-like strings, don't set defaultValue to avoid errors
      } else {
        // Handle normal default values
        sequelizeColumn.defaultValue = columnDef.defaultValue;
      }
    }

    // Set primary key
    if (columnDef.primaryKey === true) {
      sequelizeColumn.primaryKey = true;
    }

    // Set unique constraint
    if (columnDef.unique === true) {
      sequelizeColumn.unique = true;
    }

    // Set auto increment
    if (columnDef.autoIncrement === true) {
      sequelizeColumn.autoIncrement = true;
    }

    // Set field name (if different from column name)
    if (columnDef.field && typeof columnDef.field === "string") {
      sequelizeColumn.field = columnDef.field;
    }

    // Set validation rules
    if (columnDef.validate && typeof columnDef.validate === "object") {
      sequelizeColumn.validate = columnDef.validate;
    }

    return sequelizeColumn;
  }

  /**
   * Map API data type to Sequelize DataType
   * @param {string} apiType - Data type from API
   * @param {Object} columnDef - Full column definition for context
   * @returns {Object} Sequelize DataType
   */
  mapDataType(apiType, columnDef = {}) {
    if (!apiType || typeof apiType !== "string") {
      logger.warn(
        `TableBuilder.mapDataType - Invalid data type: ${apiType}, defaulting to STRING`
      );
      return DataTypes.STRING;
    }

    const upperType = apiType.toUpperCase();

    // Handle parameterized types
    if (upperType.includes("(")) {
      const baseType = upperType.split("(")[0];
      const params = upperType.match(/\(([^)]+)\)/)?.[1];

      if (baseType === "STRING" && params) {
        const length = parseInt(params);
        return DataTypes.STRING(length);
      }

      if (baseType === "CHAR" && params) {
        const length = parseInt(params);
        return DataTypes.CHAR(length);
      }

      if (baseType === "DECIMAL" && params) {
        const [precision, scale] = params
          .split(",")
          .map((p) => parseInt(p.trim()));
        return DataTypes.DECIMAL(precision, scale);
      }
    }

    // Handle ENUM type
    if (
      upperType === "ENUM" &&
      columnDef.values &&
      Array.isArray(columnDef.values)
    ) {
      return DataTypes.ENUM(...columnDef.values);
    }

    // Handle ARRAY type
    if (upperType === "ARRAY" && columnDef.arrayType) {
      const arrayElementType = this.mapDataType(columnDef.arrayType);
      return DataTypes.ARRAY(arrayElementType);
    }

    // Map standard types
    const mappedType = DATA_TYPE_MAPPING[upperType];
    if (mappedType) {
      return mappedType;
    }

    // Default fallback
    logger.warn(
      `TableBuilder.mapDataType - Unknown data type: ${apiType}, defaulting to STRING`
    );
    return DataTypes.STRING;
  }

  /**
   * Execute CREATE TABLE statement
   * @param {string} tableName - Name of the table
   * @param {Object} tableDefinition - Sequelize table definition
   * @param {Object} modelDefinition - Original model definition
   */
  async executeCreateTable(tableName, tableDefinition, modelDefinition) {
    const { columns, options } = tableDefinition;

    // Create a temporary model to generate the table
    const TempModel = sequelize.define(tableName, columns, options);

    // Sync the model to create the table
    await TempModel.sync({ force: false });

    logger.debug(
      `TableBuilder.executeCreateTable - Table created: ${this.schemaName}.${tableName}`
    );
  }

  /**
   * Create indexes for a table
   * @param {string} tableName - Name of the table
   * @param {Array} indexes - Array of index definitions
   */
  async createIndexes(tableName, indexes) {
    for (const indexDef of indexes) {
      try {
        await this.createIndex(tableName, indexDef);
      } catch (error) {
        logger.warn(
          `TableBuilder.createIndexes - Failed to create index on ${tableName}: ${error.message}`
        );
      }
    }
  }

  /**
   * Create a single index
   * @param {string} tableName - Name of the table
   * @param {Object} indexDef - Index definition
   */
  async createIndex(tableName, indexDef) {
    const indexName =
      indexDef.name || `${tableName}_${indexDef.fields?.join("_")}_idx`;
    const fields = indexDef.fields || [];
    const unique = indexDef.unique || false;

    if (!fields.length) {
      logger.warn(
        `TableBuilder.createIndex - No fields specified for index: ${indexName}`
      );
      return;
    }

    const query = `
      CREATE ${unique ? "UNIQUE" : ""} INDEX IF NOT EXISTS "${indexName}"
      ON "${this.schemaName}"."${tableName}" (${fields
      .map((f) => `"${f}"`)
      .join(", ")})
    `;

    await sequelize.query(query);
    logger.debug(
      `TableBuilder.createIndex - Created index: ${indexName} on ${this.schemaName}.${tableName}`
    );
  }

  /**
   * Check if a table exists in the schema
   * @param {string} tableName - Name of the table
   * @returns {Promise<boolean>} True if table exists
   */
  async checkTableExists(tableName) {
    try {
      const query = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = :schemaName 
          AND table_name = :tableName
        )
      `;

      const [results] = await sequelize.query(query, {
        replacements: { schemaName: this.schemaName, tableName },
        type: sequelize.QueryTypes.SELECT,
      });

      return results.exists;
    } catch (error) {
      logger.error(
        `TableBuilder.checkTableExists - Error checking table existence: ${error.message}`
      );
      return false;
    }
  }
}

/**
 * Create tables from models information in a specific schema
 * @param {string} schemaName - Name of the schema
 * @param {Object} modelsInfo - Models information from API response
 * @returns {Promise<Object>} Creation result
 */
export const createTablesFromModelsInfo = async (schemaName, modelsInfo) => {
  logger.info(
    `createTablesFromModelsInfo - Creating tables in schema: ${schemaName}`
  );

  const tableBuilder = new TableBuilder(schemaName);
  return await tableBuilder.createTablesFromModels(modelsInfo);
};

export default {
  TableBuilder,
  createTablesFromModelsInfo,
  DATA_TYPE_MAPPING,
};
