/**
 * Extracts a user-friendly error message from various error response formats
 * @param {any} error - The error response object or string
 * @returns {string} - A user-friendly error message
 */
export const extractErrorMessage = (error) => {
  // If error is a string, return it directly
  if (typeof error === "string") {
    return error;
  }

  // If error is null or undefined
  if (!error) {
    return "An unexpected error occurred";
  }

  // Handle structured error responses
  // Priority: data field (most detailed) > message > error > default
  if (error.data) {
    return error.data;
  }

  if (error.message) {
    return error.message;
  }

  if (error.error) {
    return error.error;
  }

  // Handle axios error responses
  if (error.response?.data) {
    const errorData = error.response.data;

    if (typeof errorData === "string") {
      return errorData;
    }

    if (errorData.data) {
      return errorData.data;
    }

    if (errorData.message) {
      return errorData.message;
    }

    if (errorData.error) {
      return errorData.error;
    }
  }

  return "";
};

/**
 * Extracts error message from API response structure
 * @param {object} payload - The payload from Redux action or API response
 * @returns {string} - Error message
 */
export const extractApiErrorMessage = (payload) => {
  if (!payload) {
    return "An unexpected error occurred";
  }

  if (typeof payload === "string") {
    return payload;
  }

  // Handle API response structure: { success, message, data, timestamp }
  if (payload.data && typeof payload.data === "string") {
    return payload.data;
  }

  if (payload.message) {
    return payload.message;
  }

  if (payload.error) {
    return payload.error;
  }

  return "An unexpected error occurred";
};
