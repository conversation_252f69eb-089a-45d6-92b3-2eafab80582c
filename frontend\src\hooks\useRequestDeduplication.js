import { useRef, useCallback } from 'react';

/**
 * Hook to deduplicate API requests
 * Prevents the same request from being made multiple times within a time window
 * @param {number} timeWindow - Time window in milliseconds (default: 1000ms)
 * @returns {Object} - { makeRequest, clearCache }
 */
export const useRequestDeduplication = (timeWindow = 1000) => {
  const requestCacheRef = useRef(new Map());
  const timeoutsRef = useRef(new Map());

  const makeRequest = useCallback(async (key, requestFn) => {
    // Check if request is already in progress
    if (requestCacheRef.current.has(key)) {
      console.log(`[Dedup] Request "${key}" already in progress, returning cached promise`);
      return requestCacheRef.current.get(key);
    }

    // Create the request promise
    const requestPromise = (async () => {
      try {
        const result = await requestFn();
        return result;
      } finally {
        // Clear from cache after time window
        const timeout = setTimeout(() => {
          requestCacheRef.current.delete(key);
          timeoutsRef.current.delete(key);
        }, timeWindow);
        
        timeoutsRef.current.set(key, timeout);
      }
    })();

    // Store in cache
    requestCacheRef.current.set(key, requestPromise);

    return requestPromise;
  }, [timeWindow]);

  const clearCache = useCallback((key) => {
    if (key) {
      requestCacheRef.current.delete(key);
      const timeout = timeoutsRef.current.get(key);
      if (timeout) {
        clearTimeout(timeout);
        timeoutsRef.current.delete(key);
      }
    } else {
      // Clear all
      requestCacheRef.current.clear();
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current.clear();
    }
  }, []);

  return { makeRequest, clearCache };
};

export default useRequestDeduplication;

