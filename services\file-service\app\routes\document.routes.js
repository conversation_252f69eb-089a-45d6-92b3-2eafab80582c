// app/routes/document.routes.js
import express from "express";
import {
  storeDocument,
  getDocuments,
} from "../controllers/document.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";

const router = express.Router();

// DOCUMENT ROUTES

/**
 * Store document metadata
 * @route POST /api/document
 * @access Protected (x-api-key header required)
 * @description Stores document metadata in the database
 * @body {string} organization_id - Organization ID
 * @body {string} blob_storage_path - Azure blob storage file path
 * @body {string} service - Service type (financial, operational, pms)
 * @body {number} month - Month (1-12)
 * @body {number} year - Year
 * @body {string} [file_name] - Original filename (optional)
 * @body {number} [file_size] - File size in bytes (optional)
 * @body {string} [mime_type] - MIME type (optional)
 * @body {object} [metadata] - Additional metadata (optional)
 * @headers {string} x-api-key - System API key
 */
router.post(
  "/",
  authMiddleware,
  storeDocument
);

/**
 * Get documents
 * @route GET /api/document
 * @access Protected (x-api-key header required)
 * @description Retrieves documents for an organization with optional filters
 * @query {string} organization_id - Organization ID (required)
 * @query {string} [service] - Service type filter (optional)
 * @query {number} [month] - Month filter (optional)
 * @query {number} [year] - Year filter (optional)
 * @headers {string} x-api-key - System API key
 */
router.get(
  "/",
  authMiddleware,
  getDocuments
);

export default router;

